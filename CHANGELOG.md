## [0.5.0](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/compare/v0.4.0...v0.5.0) (2025-06-30)

### :sparkles: Features

* **csharp:** additional definitions support ([d4fe85d](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/d4fe85d44f5c3abcdda39b5c5c6cabb3d33797ed)) by <PERSON><PERSON><PERSON>
* **releases:** include chore and other types in changelog ([150ecb1](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/150ecb1fff71f98b9175aaa59ebcbe56b8d0621d)) by <PERSON>

### :bug: Fixes

* **ci:** disable toolchain auto update during release ([c87eb3c](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/c87eb3c0647abea155096bbc429d2c69581bede3)) by <PERSON>

### :repeat: Chore

* cargo edition bump to 2024, and related linting, clippy fixes ([a4867a8](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/a4867a8a8ed0955d7624096ab5d9ca520b51f97b)) by <PERSON> Usachenko
* **deps:** fix lint for rust 1.88 ([b903eb5](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/b903eb51f19b801f2dbaf745c2301d6dd699f14e)) by Bohdan Parkhomchuk

# [0.4.0](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/compare/v0.3.0...v0.4.0) (2025-06-26)


### Features

* **ci:** run builds on large runner ([0ffe4bc](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/0ffe4bcb3a6e4c64d6f886d6acc9afd98ba5efe0)) by Bohdan Parkhomchuk
* **java:** support Java callable lambdas ([3b4ffbd](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/3b4ffbdbbfc30490057a6d981400f576c497c539)) by Jean-Gabriel Doyon
* **kotlin:** add Kotlin lambdas support ([7e6e578](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/7e6e57866428a98d40a7eb9e0bf77de234508909)) by Jean-Gabriel Doyon
* **parser:** expose supported extensions method ([c939d66](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/c939d66409e3a7c0eda953e518dc0de962e5809f)) by Jean-Gabriel Doyon
* **py:** added parsing for Python imports ([305cf7c](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/305cf7cfbef4e9d09b2efb754d8b04eada1c8e71)) by Jonathan Shobrook

# [0.3.0](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/compare/v0.2.0...v0.3.0) (2025-06-23)


### Bug Fixes

* **changelog:** use shortHash in commit links and add newline ([68b9ac7](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/68b9ac7f373cfc4a2c8e375743365fc8d7c7a2d8)) by michaelangeloio
* **csharp:** benches, tests and fixes ([c4373b2](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/c4373b265863740b922612c6c1cf272037487f00)) by Bohdan Parkhomchuk
* **releases:** release prepare script ([0ff4d60](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/0ff4d60261f6a5f1d1b6caa0b600b74c7b6e1be1)) by Michael Angelo Rivera


### Features

* **chunker:** add language to chunks ([2051a7c](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/2051a7c442fe81ce03caca5f53050aa6e85d7226)) by James Fargher
* **java:** parse Java callable definitions ([80429aa](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/80429aa272ad838e1de3cd7edfcbf9be0027c7c7)) by Jean-Gabriel Doyon
* **kotlin:** differentiate annotation, enum and interface in Kotlin ([1287995](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/1287995a793efa3186ecbb911d6e1191f236c197)) by Jean-Gabriel Doyon
* **parser:** setup initial import generics ([f388495](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/f3884950406d7db55e175a4a7371ed84ea14c09c)) by Jonathan Shobrook
* **release:** add slackbot integration ([670bad0](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/670bad0020cb2c9eb7be02d419253d61462253cd)) by Michael Angelo Rivera

# [0.3.0](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/compare/v0.2.0...v0.3.0) (2025-06-20)


### Features

* **kotlin:** differentiate annotation, enum and interface in Kotlin ([1287995a793efa3186ecbb911d6e1191f236c197](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/1287995a793efa3186ecbb911d6e1191f236c197)) by Jean-Gabriel Doyon* **release:** add slackbot integration ([670bad0020cb2c9eb7be02d419253d61462253cd](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/670bad0020cb2c9eb7be02d419253d61462253cd)) by Michael Angelo Rivera

# [0.2.0](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/compare/v0.1.0...v0.2.0) (2025-06-20)


### Bug Fixes

* **ci:** add artifact fetcher to combine benchmark results ([4fe6c2a](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/4fe6c2a2716dc2a8f3d328844c014a8367ec25b3))
* **ruby:** expand FQN ranges to cover full definitions ([754870a](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/754870a9712e6a4c4e7120783144c08fd8d9c6bc))


### Features

* **chunker:** initial code parsing chunker ([b2c087c](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/b2c087cd1f97e3a9cdac706e482a14d96933a5fa))
* **csharp:** add C# definitions ([7ce83c7](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/7ce83c7c3a03e65264b22f96286c7de76abc1616))
* **js:** implement JS definitions ([4704f70](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/4704f700d7b493364fcc255028d6c418fe2ff4ee))
* **kotlin:** add Kotlin definitions ([f2afb0a](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/f2afb0aae1273404677e9136262b6b301e33df0b))
* **parser:** setup initial library ([dc112e1](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/dc112e19a6442a04e52cbf55b308d1cabfb5316e))
* **py:** added parsing for Python definitions ([fbdffcf](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/fbdffcfe1f5c8eff09ab1f906688a13712894641))
* **ruby:** implement ruby definitions ([99d241b](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/99d241b530f21a12c21056bd5b8cb7d165a431fc))
* **ts:** support for TS specific definitions + TS tests ([68e1781](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/68e1781c606c64180a394090cff500480a34282c))
* **ts:** use ast-grep TS parser for both JS and TS ([b18e6e0](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/b18e6e0d2ef92898cab9b1c6e1231a65a177f4dc))
* **xtask:** add flag to skip setting a stable toolchain ([a4f8213](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/a4f821364f6669263c1c5569bbab909eb14684aa))

# [0.2.0](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/compare/v0.1.0...v0.2.0) (2025-06-19)


### Bug Fixes

* **ci:** add artifact fetcher to combine benchmark results ([4fe6c2a](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/4fe6c2a2716dc2a8f3d328844c014a8367ec25b3))
* **ruby:** expand FQN ranges to cover full definitions ([754870a](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/754870a9712e6a4c4e7120783144c08fd8d9c6bc))


### Features

* **chunker:** initial code parsing chunker ([b2c087c](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/b2c087cd1f97e3a9cdac706e482a14d96933a5fa))
* **js:** implement JS definitions ([4704f70](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/4704f700d7b493364fcc255028d6c418fe2ff4ee))
* **kotlin:** add Kotlin definitions ([f2afb0a](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/f2afb0aae1273404677e9136262b6b301e33df0b))
* **parser:** setup initial library ([dc112e1](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/dc112e19a6442a04e52cbf55b308d1cabfb5316e))
* **py:** added parsing for Python definitions ([fbdffcf](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/fbdffcfe1f5c8eff09ab1f906688a13712894641))
* **ruby:** implement ruby definitions ([99d241b](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/99d241b530f21a12c21056bd5b8cb7d165a431fc))
* **ts:** support for TS specific definitions + TS tests ([68e1781](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/68e1781c606c64180a394090cff500480a34282c))
* **ts:** use ast-grep TS parser for both JS and TS ([b18e6e0](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/b18e6e0d2ef92898cab9b1c6e1231a65a177f4dc))
* **xtask:** add flag to skip setting a stable toolchain ([a4f8213](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/commit/a4f821364f6669263c1c5569bbab909eb14684aa))
