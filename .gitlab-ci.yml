stages:
  - test
  - bench
  - tag
  - deploy
  
include:
  - template: Security/Dependency-Scanning.gitlab-ci.yml
  - template: Security/SAST.gitlab-ci.yml
  - template: Security/Secret-Detection.gitlab-ci.yml
  - component: ${CI_SERVER_FQDN}/gitlab-org/components/danger-review/danger-review@2.1.0

workflow:
  rules: &workflow_rules
    - if: '$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH && $GITLAB_USER_LOGIN =~ /project_\d+_bot\d*/'
      variables:
        GITLAB_DEPENDENCY_PROXY: ""
    # For merge requests, create a pipeline.
    - if: '$CI_MERGE_REQUEST_IID'
    # For `main` branch, create a pipeline (this includes on schedules, pushes, merges, etc.).
    - if: '$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH'
    # For tags, create a pipeline.
    - if: '$CI_COMMIT_TAG'
    # Allow manual pipeline runs from the web UI
    - if: '$CI_PIPELINE_SOURCE == "web"'

variables:
  GLAB_VERSION: "1.55.0"

default:
  image: ${GITLAB_DEPENDENCY_PROXY}alpine:3.21

semgrep-sast:
  rules: *workflow_rules

gemnasium-dependency_scanning:
  rules: *workflow_rules

secret_detection:
  rules: *workflow_rules

danger-review:
  stage: test
  needs: []
  before_script:
    - bundle init
    - bundle add gitlab-dangerfiles

lint:mr-title:
  image: ${GITLAB_DEPENDENCY_PROXY}node:lts-alpine
  stage: test
  needs: []
  before_script:
    - npm install -g @commitlint/cli @commitlint/config-conventional
  script:
    - ./scripts/check-mr-title.sh

.rust-job-template:
  image: ${GITLAB_DEPENDENCY_PROXY}rust:latest
  tags:
    - saas-linux-large-amd64
  needs: []
  before_script:
    - rustc --version && cargo --version
    - export PATH=$PATH:$CARGO_HOME/bin
  only:
    - main
    - merge_requests
  variables:
    CARGO_HOME: ${CI_PROJECT_DIR}/.cargo
  cache:
    key:
      files:
        - Cargo.toml
        - Cargo.lock
    paths:
      - .cargo/bin
      - .cargo/registry/index
      - .cargo/registry/cache
      - target/debug/deps
      - target/debug/build
    policy: pull-push

rust-unit-tests:
  stage: test
  script:
    - RUST_BACKTRACE=1 cargo test -- --nocapture
  extends: .rust-job-template

rust-unit-test-report:
  stage: test
  before_script:
    - rustup toolchain install nightly
    - cargo install --root . cargo2junit
  script:
    - cargo +nightly test -- -Z unstable-options --format json --report-time | bin/cargo2junit > report.xml
  extends: .rust-job-template
  artifacts:
    when: always
    reports:
      junit:
        - report.xml

lint:rust-fmt-check:
  stage: test
  script:
    - cargo fmt --check
  extends: .rust-job-template

lint:rust-lint-check:
  stage: test
  script:
    - rustup component add clippy
    - cargo clippy --all-targets --all-features -- -D warnings
  extends: .rust-job-template

lint:xtask-version-check:
  stage: test
  script:
    - cargo run --package xtask --manifest-path crates/xtask/Cargo.toml -- check-versions
  extends: .rust-job-template

lint:xtask-newlines-check:
  stage: test
  script:
    - cargo run --package xtask --manifest-path crates/xtask/Cargo.toml -- verify-newlines
  extends: .rust-job-template

publish-release::manual:
  image: registry.gitlab.com/gitlab-org/editor-extensions/build-images/node-codesign:22.14.0
  stage: deploy
  variables:
    # Prevent rustup from auto-updating to avoid cross-device link issues in Docker
    RUSTUP_UPDATE_ROOT: "false"
    RUSTUP_DISABLE_AUTOUPDATE: "1"
  before_script:
    # Validate that the repository contains a package.json and extract a few values from it.
    - |
      if [[ ! -f package.json ]]; then
        echo "No package.json found! A package.json file is required to publish a package to GitLab's NPM registry."
        echo 'For more information, see https://docs.gitlab.com/ee/user/packages/npm_registry/#creating-a-project'
        exit 1
      fi
    - NPM_PACKAGE_NAME=$(node -p "require('./package.json').name")
    - NPM_PACKAGE_VERSION=$(node -p "require('./package.json').version")
    # Validate that the package name is properly scoped to the project's root namespace.
    # For more information, see https://docs.gitlab.com/user/packages/npm_registry/#naming-convention
    - |
      if [[ ! $NPM_PACKAGE_NAME =~ ^@$CI_PROJECT_ROOT_NAMESPACE/ ]]; then
        echo "Invalid package scope! Packages must be scoped in the root namespace of the project, e.g. \"@${CI_PROJECT_ROOT_NAMESPACE}/${CI_PROJECT_NAME}\""
        echo 'For more information, see https://docs.gitlab.com/user/packages/npm_registry/#naming-convention'
        exit 1
      fi
  script:
    - npm ci
    - PREVIOUS_VERSION=$(node -p "require('./package.json').version")
    - npm run semantic-release
    - echo "PACKAGE_VERSION=$(node -p "require('./package.json').version")" >> ./build.env
  when: manual
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  allow_failure: false

rust-criterion-benchmarks:
  stage: bench
  script:
    # Note: the xtask cli will detect if the branch is `main` and save the benchmark results to `target/criterion/main`
    # This will allow subsequent benchmark jobs on other branches to compare against the main branch as a baseline.
    - cargo run --package xtask -- run-criterion --package-name parser-core --commit-branch "$CI_COMMIT_BRANCH" --default-branch "$CI_DEFAULT_BRANCH"
  extends: .rust-job-template
  artifacts:
    paths:
      - target/criterion
      
rust-flamegraph-benchmarks:
  stage: bench
  before_script:
    - apt-get update && apt-get install -y linux-perf
    - cargo install flamegraph --force
  script:
    - cargo run --package xtask -- run-flamegraph --package-name parser-core --use-debug-symbols
  extends: .rust-job-template
  artifacts:
    paths:
      - target/flamegraphs

pages:
  stage: deploy
  extends: .rust-job-template
  script:
    - echo "Deploying benchmark reports to GitLab Pages..."
    # Add current branch artifacts to public directory
    - mkdir -p public/$CI_COMMIT_REF_SLUG
    - cp -r target/criterion public/$CI_COMMIT_REF_SLUG/
    - cp -r target/flamegraphs public/$CI_COMMIT_REF_SLUG/
    # Use xtask to fetch and combine artifacts from all branches
    - cargo run --package xtask -- combine-artifacts --output-dir public
    - echo "Reports available at https://gitlab-org.gitlab.io/rust/gitlab-code-parser/"
  artifacts:
    paths:
      - public
  needs:
    - job: rust-criterion-benchmarks
      artifacts: true
    - job: rust-flamegraph-benchmarks
      artifacts: true

comment-benchmark-reports:
  stage: deploy
  allow_failure: true
  image: registry.gitlab.com/gitlab-org/cli:v${GLAB_VERSION} # Use an image with glab and jq
  needs:
    - job: rust-criterion-benchmarks
      artifacts: false # We don't need artifacts from this job, just that it succeeded
    - job: rust-flamegraph-benchmarks
      artifacts: false # We don't need artifacts from this job, just that it succeeded
    - job: pages
      artifacts: false # We don't need artifacts from this job, just that it succeeded
  rules:
    - if: '$CI_MERGE_REQUEST_IID && $GITLAB_TOKEN' # Run only on MRs and if GITLAB_TOKEN is set
  before_script:
    # alpine image for glab doesn't have bash or jq by default.
    # Ensure sh is used and install jq.
    # The glab image uses /bin/sh, so that's fine.
    - apk add --no-cache jq
  script:
    - echo "Commenting on MR !${CI_MERGE_REQUEST_IID} that benchmark reports are ready..."
    - ./scripts/comment-benchmark-reports.sh

set_milestone:
  stage: test
  allow_failure: true
  image: registry.gitlab.com/gitlab-org/cli:v${GLAB_VERSION}
  rules:
    # Run on merge requests only
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
  before_script:
    - apk add --no-cache jq
  script:
    - echo "Setting current milestone for merge request $CI_MERGE_REQUEST_IID"
    - glab auth login --hostname $CI_SERVER_HOST --token $GITLAB_TOKEN
    - ./scripts/set-milestone.sh
