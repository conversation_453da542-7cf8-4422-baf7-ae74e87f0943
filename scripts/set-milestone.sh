#!/bin/sh
# Description: Sets the milestone for merge requests based on the current active milestone
# in the top-level namespace of the GitLab project.
set -eu

# Function to print debug messages if MILESTONE_DEBUG is enabled
debug() {
  if [ -n "${MILESTONE_DEBUG:-}" ]; then
    local timestamp
    timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    echo "$timestamp - DEBUG - $1" >&2
  fi
}

# Function to print info messages
info() {
  echo "$1" >&2
}

# Function to print error messages
error() {
  echo "Error: $1" >&2
}

# Check if required environment variables are set
check_env_vars() {
  debug "Checking required environment variables"
  
  if [ -z "${CI_PROJECT_ID:-}" ] || [ -z "${CI_MERGE_REQUEST_IID:-}" ]; then
    error "Required environment variables CI_PROJECT_ID or CI_MERGE_REQUEST_IID are not set"
    exit 1
  fi

  if [ -z "${CI_PROJECT_PATH:-}" ]; then
    error "Required environment variable CI_PROJECT_PATH is not set"
    exit 1
  fi

  if [ -z "${CI_SERVER_HOST:-}" ]; then
    CI_SERVER_HOST="gitlab.com"
    info "CI_SERVER_HOST not set, using default: $CI_SERVER_HOST"
  fi
  
  debug "All required environment variables are set"
}

# Check if the MR already has a milestone
check_existing_milestone() {
  debug "Checking if MR $CI_MERGE_REQUEST_IID already has a milestone"
  
  CURRENT_MR=$(glab api "/projects/$CI_PROJECT_ID/merge_requests/$CI_MERGE_REQUEST_IID?fields=id,iid,title,milestone.id,milestone.title")
  
  # Use grep to check for milestone existence (more robust against malformed JSON)
  if echo "$CURRENT_MR" | grep -q '"milestone":{"id":[0-9]*'; then
    # Extract using grep/sed for robustness against JSON formatting issues
    EXISTING_MILESTONE_ID=$(echo "$CURRENT_MR" | grep -o '"milestone":{"id":[0-9]*' | head -1 | sed 's/"milestone":{"id"://')
    EXISTING_MILESTONE_TITLE=$(echo "$CURRENT_MR" | grep -o '"milestone":{[^}]*"title":"[^"]*"' | head -1 | grep -o '"title":"[^"]*"' | sed 's/"title":"//;s/"//')
    
    info "MR already has milestone: $EXISTING_MILESTONE_TITLE (ID: $EXISTING_MILESTONE_ID)"
    info "Skipping milestone assignment to preserve existing milestone"
    exit 0
  fi
  
  debug "MR does not have a milestone"
  return 0
}

# Get the current milestone from the top-level namespace
get_current_milestone() {
  debug "Getting current milestone from top-level namespace"
  
  # Extract the top-level namespace from CI_PROJECT_PATH
  TOP_LEVEL_NAMESPACE=$(echo "$CI_PROJECT_PATH" | cut -d '/' -f 1)
  
  info "Looking for milestone from top-level namespace: $TOP_LEVEL_NAMESPACE"
  
  # Get today's date in YYYY-MM-DD format
  TODAY=$(date '+%Y-%m-%d')
  debug "Today's date: $TODAY"
  
  # Find currently active milestones with version number pattern directly
  debug "Finding current milestone..."
  SELECTED_MILESTONE=$(glab api "/groups/$TOP_LEVEL_NAMESPACE/milestones?state=active&per_page=100" | \
    jq -r --arg today "$TODAY" '[.[] 
    | select(.title | test("^[0-9]+\\.[0-9]+$"))
    | select((.start_date <= $today) and (.due_date >= $today))
    | {id, title, start_date, due_date}]' | \
    jq 'if length > 0 then 
          if length > 1 then 
            sort_by((.title | split(".") | .[0] | tonumber), (.title | split(".") | .[1] | tonumber)) | reverse | .[0] 
          else 
            .[0] 
          end 
        else 
          empty 
        end')
  
  # Check if we found a current milestone
  if [ -n "$SELECTED_MILESTONE" ] && [ "$SELECTED_MILESTONE" != "null" ]; then
    MILESTONE_ID=$(echo "$SELECTED_MILESTONE" | jq -r '.id')
    MILESTONE_TITLE=$(echo "$SELECTED_MILESTONE" | jq -r '.title')
    START_DATE=$(echo "$SELECTED_MILESTONE" | jq -r '.start_date')
    DUE_DATE=$(echo "$SELECTED_MILESTONE" | jq -r '.due_date')
    
    info "Selected current milestone: $MILESTONE_TITLE (ID: $MILESTONE_ID)"
    debug "Date range: $START_DATE to $DUE_DATE"
    return 0
  else
    info "Could not determine appropriate milestone"
    return 0
  fi
}

# Update the merge request with the milestone
update_merge_request() {
  debug "Updating MR $CI_MERGE_REQUEST_IID to use milestone ID $MILESTONE_ID"
  
  if [ -z "$MILESTONE_ID" ] || [ "$MILESTONE_ID" = "null" ]; then
    info "Could not extract milestone ID, skipping milestone assignment"
    return 1
  fi
  
  glab api "/projects/$CI_PROJECT_ID/merge_requests/$CI_MERGE_REQUEST_IID" -X PUT -f "milestone_id=$MILESTONE_ID"
  
  info "Successfully set milestone $MILESTONE_TITLE for MR $CI_MERGE_REQUEST_IID"
  return 0
}

# Main function that orchestrates the script execution
main() {
  debug "Starting set-milestone.sh script"
  
  # Check environment variables
  check_env_vars
  
  # Check if MR already has a milestone
  check_existing_milestone
  
  # Get current milestone
  if get_current_milestone; then
    # Update MR with milestone
    update_merge_request
  else
    info "Skipping milestone assignment"
  fi
  
  debug "Script execution completed"
}

# Run the main function and exit immediately after
main; exit $?
