#!/bin/sh
set -eu

# Script to comment on a Merge Request when benchmark reports are ready.
# It checks for an existing comment from this script and updates it, or creates a new one.

# Required environment variables
if [ -z "${CI_PROJECT_ID:-}" ] || [ -z "${CI_MERGE_REQUEST_IID:-}" ] || [ -z "${CI_COMMIT_REF_SLUG:-}" ]; then
  echo "Error: Required environment variables CI_PROJECT_ID, CI_MERGE_REQUEST_IID, or CI_COMMIT_REF_SLUG are not set."
  exit 1
fi

if [ -z "${GITLAB_TOKEN:-}" ]; then
  echo "Error: GITLAB_TOKEN is not set. Please set it as a CI/CD variable."
  exit 1
fi

CI_SERVER_HOST="${CI_SERVER_HOST:-gitlab.com}"
MARKER="BENCHMARK_COMMENT_MARKER"
PROJECT_URL_BASE="https://gitlab-org.gitlab.io/rust/gitlab-code-parser" # Adjust if your Pages URL is different
UPDATED_AT=$(date -u +%Y-%m-%d' '%H:%M:%S' '%Z)

COMMENT_BODY="
## Benchmark Reports 🚀

Your benchmark reports are ready:

Linux:
- [Criterion Benchmark Report](${PROJECT_URL_BASE}/${CI_COMMIT_REF_SLUG}/criterion/report/index.html)
- [Flamegraph Benchmark Report](${PROJECT_URL_BASE}/${CI_COMMIT_REF_SLUG}/flamegraphs/index.html)

View all reports at: ${PROJECT_URL_BASE}

Please review these reports and ensure that the changes do not negatively impact performance.

> Windows & MacOS CI benchmarks are not yet available.

**Updated at**: ${UPDATED_AT}

<!-- BENCHMARK_COMMENT_MARKER -->"

create_new_comment() {
  echo "Creating a new comment..."
  glab api "/projects/${CI_PROJECT_ID}/merge_requests/${CI_MERGE_REQUEST_IID}/notes" \
    -X POST \
    -f "body=${COMMENT_BODY}"
  echo "Comment posted successfully."
}

echo "Logging into GitLab..."
glab auth login --hostname "$CI_SERVER_HOST" --token "$GITLAB_TOKEN"

echo "Fetching existing notes for MR !${CI_MERGE_REQUEST_IID} in project ${CI_PROJECT_ID}..."
NOTES=$(glab api --paginate "/projects/${CI_PROJECT_ID}/merge_requests/${CI_MERGE_REQUEST_IID}/notes?sort=asc&order_by=updated_at")

EXISTING_NOTE_ID=""
# Check if NOTES is not empty and is a valid JSON array before processing with jq
if printf "%s" "$NOTES" | jq empty > /dev/null 2>&1; then
    EXISTING_NOTE_ID=$(printf "%s" "$NOTES" | jq -r --arg MARKER "$MARKER" '.[] | select(.body | test($MARKER)) | .id' | head -n 1)
else
    echo "No notes found or failed to parse notes JSON."
fi

if [ -n "$EXISTING_NOTE_ID" ]; then
  echo "Found existing benchmark comment (Note ID: $EXISTING_NOTE_ID). Attempting to update it..."
  if glab api "/projects/${CI_PROJECT_ID}/merge_requests/${CI_MERGE_REQUEST_IID}/notes/${EXISTING_NOTE_ID}" \
    -X PUT \
    -f "body=${COMMENT_BODY}" 2>/dev/null; then
    echo "Comment updated successfully."
  else
    UPDATE_EXIT_CODE=$?
    echo "Failed to update existing comment (exit code: $UPDATE_EXIT_CODE). This might be due to token rotation or permission issues."
    echo "Creating a new comment instead..."
    create_new_comment
  fi
else
  echo "No existing benchmark comment found."
  create_new_comment
fi 
