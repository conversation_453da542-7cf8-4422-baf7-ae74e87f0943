use parser_core::{
    DefinitionLookup,
    kotlin::analyzer::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    parser::{<PERSON><PERSON><PERSON><PERSON><PERSON>, LanguageParser, SupportedLanguage},
    rules::{RuleManager, run_rules},
};

fn main() {
    let complex_kotlin_code = r#"
package com.project.access;

import java.time.Clock
import java.time.LocalDateTime
import java.util.concurrent.atomic.AtomicLong
import kotlinx.coroutines.*
import kotlinx.serialization.*

object Time {
    val utcClock = Clock.systemUTC()
    val clock = utcClock
}

@Target(AnnotationTarget.CLASS)
annotation class Disposable

@Serializable
data class Project(
    private val absolutePath: String,
    private val name: String,
    val id: Long = 0L,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val metadata: Map<String, String> = emptyMap()
) {
    companion object {
        fun default(): Project = Project("~/", "default-project")
        fun create(name: String, path: String): Project = Project(path, name)
    }
}

fun Project.display(): String {
    return "[$absolutePath] $name"
}

fun Project.withMetadata(key: String, value: String): Project {
    return copy(metadata = metadata + (key to value))
}

const val BASE_URL = "localhost:8000"
const val MAX_RETRIES = 3
const val TIMEOUT_SECONDS = 30L

val String.urlAndPort: Pair<String, String> 
    get() = split(":").let { it[0] to it[1] }

val httpClient by lazy { HttpClient() }

enum class AccessResult(val message: String, val code: Int) {
    UNKNOWN_PROJECT("Unknown project", 404),
    ACCESS_EXPIRED("Access expired", 401),
    ACCESS_OK("Access ok", 200),
    ACCESS_DENIED("Access denied", 403)
}

@Disposable
class ProjectAccessService(
    private val project: Project,
    private val config: AccessConfig = AccessConfig()
) {
    companion object {
        private val logger: Logger = logger<ProjectAccessService>()
        private val requestCounter = AtomicLong(0)
        
        fun createDefault(): ProjectAccessService {
            return ProjectAccessService(Project.default())
        }
    }

    constructor() : this(Project.default())

    private val clock = Time.clock
    private val coroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    suspend fun validateAccess(target: String): AccessResult {
        val requestId = requestCounter.incrementAndGet()
        logger.info("Validating access for target: $target, requestId: $requestId")
        
        val requestUrl = "$BASE_URL/access/${project.name}?target=$target&time=${clock.utc()}"
        
        return try {
            val response = httpClient
                .get(requestUrl)
                .timeout(Duration.ofSeconds(config.timeoutSeconds))
                .unsafeInto<AccessResult>()
            
            logger.info("Access validation completed for requestId: $requestId")
            response
        } catch (e: Exception) {
            logger.error("Access validation failed for requestId: $requestId", e)
            AccessResult.ACCESS_DENIED
        }
    }

    suspend fun revokeAccess(target: String): Boolean {
        val requestUrl = "$BASE_URL/access/${project.name}/revoke"

        val body = json {
            "target" to target
            "time" to clock.utc()
            "requestId" to requestCounter.incrementAndGet()
        }
        
        return try {
            httpClient
                .post(requestUrl, body)
                .timeout(Duration.ofSeconds(config.timeoutSeconds))
            true
        } catch (e: Exception) {
            logger.error("Failed to revoke access for target: $target", e)
            false
        }
    }

    fun validateAccessBatch(targets: List<String>): Flow<AccessResult> = flow {
        targets.forEach { target ->
            emit(validateAccess(target))
        }
    }

    suspend fun processBatch(targets: List<String>): List<AccessResult> {
        return coroutineScope {
            targets.map { target ->
                async { validateAccess(target) }
            }.awaitAll()
        }
    }

    fun shutdown() {
        coroutineScope.cancel()
    }
}

@Serializable
data class AccessConfig(
    val timeoutSeconds: Long = TIMEOUT_SECONDS,
    val maxRetries: Int = MAX_RETRIES,
    val enableLogging: Boolean = true,
    val cacheEnabled: Boolean = true
)

class AccessManager(
    private val services: Map<String, ProjectAccessService> = emptyMap()
) {
    private val cache = mutableMapOf<String, AccessResult>()
    private val cacheExpiry = mutableMapOf<String, LocalDateTime>()

    suspend fun validateAccess(projectName: String, target: String): AccessResult {
        val cacheKey = "$projectName:$target"
        
        // Check cache first
        cache[cacheKey]?.let { cachedResult ->
            cacheExpiry[cacheKey]?.let { expiry ->
                if (LocalDateTime.now().isBefore(expiry)) {
                    return cachedResult
                }
            }
        }

        val service = services[projectName] ?: return AccessResult.UNKNOWN_PROJECT
        val result = service.validateAccess(target)
        
        // Cache the result
        cache[cacheKey] = result
        cacheExpiry[cacheKey] = LocalDateTime.now().plusMinutes(5)
        
        return result
    }

    fun addService(projectName: String, service: ProjectAccessService) {
        services.toMutableMap().apply {
            put(projectName, service)
        }
    }

    fun removeService(projectName: String) {
        services.toMutableMap().apply {
            remove(projectName)
        }
    }
}

interface AuthenticationService {
    suspend fun authenticate(email: String, password: String): Boolean
    suspend fun validateToken(token: String): Boolean
}

class CredentialsChecker(
    private val user: User,
    private val password: String
) {
    private val maxAttempts = 3
    private var attempts = 0

    suspend fun isValidPassword(): Boolean {
        attempts++
        
        return try {
            val hashedPassword = hashPassword(password, user.salt)
            val isValid = hashedPassword == user.passwordHash
            
            if (isValid) {
                attempts = 0
                logger.info("Password validation successful for user: ${user.email}")
            } else {
                logger.warn("Password validation failed for user: ${user.email}, attempt: $attempts")
            }
            
            isValid
        } catch (e: Exception) {
            logger.error("Password validation error for user: ${user.email}", e)
            false
        }
    }

    private fun hashPassword(password: String, salt: String): String {
        return DigestUtils.sha256Hex(password + salt)
    }
}

class UserAuthenticationService : AuthenticationService {
    companion object {
        private val logger = LoggerFactory.getLogger(UserAuthenticationService::class.java)
        
        fun createDefault(): UserAuthenticationService {
            return UserAuthenticationService()
        }
    }
    
    override suspend fun authenticate(email: String, password: String): Boolean {
        val user = User.findByEmail(email) ?: return false
        val checker = CredentialsChecker(user, password)
        
        return if (checker.isValidPassword()) {
            logger.info("Authentication successful for $email")
            true
        } else {
            logger.warn("Authentication failed for $email")
            false
        }
    }

    override suspend fun validateToken(token: String): Boolean {
        return try {
            val decodedToken = JWT.decode(token)
            val email = decodedToken.subject
            val user = User.findByEmail(email)
            user != null
        } catch (e: Exception) {
            logger.error("Token validation failed", e)
            false
        }
    }
}

@Serializable
data class User(
    val id: Long,
    val name: String,
    val email: String,
    val passwordHash: String,
    val salt: String,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val isActive: Boolean = true
) {
    companion object {
        suspend fun findByEmail(email: String): User? {
            return withContext(Dispatchers.IO) {
                // Simulate database lookup
                if (email.contains("@")) {
                    User(
                        id = 1L,
                        name = "Test User",
                        email = email,
                        passwordHash = "hashed_password",
                        salt = "salt"
                    )
                } else null
            }
        }
    }
}

class MetricsCollector {
    private val metrics = mutableMapOf<String, MutableList<Long>>()
    private val lock = Mutex()

    suspend fun recordMetric(name: String, value: Long) {
        lock.withLock {
            metrics.getOrPut(name) { mutableListOf() }.add(value)
        }
    }

    suspend fun getAverage(name: String): Double? {
        return lock.withLock {
            metrics[name]?.let { values ->
                if (values.isNotEmpty()) values.average() else null
            }
        }
    }

    suspend fun getMetrics(): Map<String, List<Long>> {
        return lock.withLock {
            metrics.toMap()
        }
    }
}

class ConfigurationManager(
    private val environment: String = "development"
) {
    private val settings = mutableMapOf<String, Any>()
    private val changeListeners = mutableListOf<(String, Any, Any) -> Unit>()

    companion object {
        private val logger = LoggerFactory.getLogger(ConfigurationManager::class.java)
        
        val DEFAULT_SETTINGS = mapOf(
            "timeout" to 30L,
            "max_connections" to 100,
            "retry_count" to 3,
            "debug_mode" to true
        )
    }

    init {
        settings.putAll(DEFAULT_SETTINGS)
        loadEnvironmentSettings()
    }

    private fun loadEnvironmentSettings() {
        when (environment) {
            "production" -> {
                settings["debug_mode"] = false
                settings["max_connections"] = 200
            }
            "test" -> {
                settings["debug_mode"] = true
                settings["max_connections"] = 10
            }
        }
    }

    fun updateSetting(key: String, value: Any) {
        val oldValue = settings[key]
        settings[key] = value
        
        changeListeners.forEach { listener ->
            try {
                listener(key, oldValue, value)
            } catch (e: Exception) {
                logger.error("Error in configuration change listener", e)
            }
        }
    }

    fun addChangeListener(listener: (String, Any, Any) -> Unit) {
        changeListeners.add(listener)
    }

    fun getSetting(key: String): Any? = settings[key]

    fun validateConfiguration(): Boolean {
        val requiredKeys = listOf("timeout", "max_connections")
        return requiredKeys.all { settings.containsKey(it) }
    }
}

suspend fun main() {
    val project = Project("~/project", "sample")
    println("Loaded ${project.display()}.")

    val (url, port) = BASE_URL.urlAndPort
    println("Service url: $url and port $port")

    val service = ProjectAccessService(project)
    val result = service.validateAccess("test-target")
    println("Access validation result: $result")

    val authService = UserAuthenticationService()
    val authResult = authService.authenticate("<EMAIL>", "password123")
    println("Authentication result: $authResult")

    val configManager = ConfigurationManager()
    configManager.updateSetting("timeout", 60L)
    println("Configuration updated")

    service.shutdown()
}
"#;

    // This exercises all components: Rules -> FQN -> Definition Extraction -> Analysis
    let parser = GenericParser::default_for_language(SupportedLanguage::Kotlin);
    let rule_manager = RuleManager::new(SupportedLanguage::Kotlin);
    let analyzer = KotlinAnalyzer::new();

    let parse_result = parser
        .parse(complex_kotlin_code, Some("complex_kotlin_benchmark.kt"))
        .unwrap();

    println!("Starting Kotlin definition extraction flamegraph benchmark...");
    println!("Code size: {} characters", complex_kotlin_code.len());
    println!("Running 1000 iterations of the full definition extraction pipeline...");

    let matches = run_rules(
        &parse_result.ast,
        Some("complex_kotlin_benchmark.kt"),
        &rule_manager,
    );

    let analysis_result = analyzer.analyze(&matches, &parse_result).unwrap();

    let _definition_count = analysis_result.definitions.len();
    let _counts_by_type = analysis_result.count_definitions_by_type();
    let _definitions_with_fqn = analysis_result.definitions_with_fqn();
    let _fqn_strings = analysis_result.kotlin_definition_fqn_strings();
    let _definition_names = analysis_result.definition_names();

    println!(
        "Completed - Found {} definitions, {} with FQN",
        analysis_result.definitions.len(),
        analysis_result.definitions_with_fqn().len()
    );

    println!("Flamegraph benchmark completed!");
    println!("This profile should show hotspots in:");
    println!("  - Rule pattern matching");
    println!("  - FQN computation and AST traversal");
    println!("  - Definition extraction and classification");
    println!("  - Metadata computation and aggregation");
}
