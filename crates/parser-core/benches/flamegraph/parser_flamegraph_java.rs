use parser_core::{
    java::analyzer::<PERSON><PERSON><PERSON><PERSON><PERSON>,
    parser::{<PERSON><PERSON><PERSON><PERSON><PERSON>, LanguageParser, SupportedLanguage},
    rules::{RuleManager, run_rules},
};

fn main() {
    let complex_java_code = r#"
package com.example.enterprise;

import java.time.Clock;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import org.springframework.web.bind.annotation.*;
import org.springframework.http.ResponseEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@RestController
@RequestMapping("/api/v1")
public class EnterpriseUserController {
    
    private static final Logger logger = Logger.getLogger(EnterpriseUserController.class.getName());
    private final Clock clock = Clock.systemUTC();
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private AuthenticationService authService;
    
    @Autowired
    private AuditService auditService;
    
    @Autowired
    private NotificationService notificationService;
    
    @GetMapping("/users")
    public ResponseEntity<List<User>> getAllUsers() {
        logger.info("Fetching all users at " + clock.instant());
        List<User> users = userService.findAll();
        auditService.logAccess("GET /users", users.size());
        return ResponseEntity.ok(users);
    }
    
    @GetMapping("/users/{id}")
    public ResponseEntity<User> getUserById(@PathVariable Long id) {
        logger.info("Fetching user with id: " + id);
        Optional<User> user = userService.findById(id);
        
        if (user.isPresent()) {
            auditService.logAccess("GET /users/" + id, 1);
            return ResponseEntity.ok(user.get());
        } else {
            logger.warning("User not found with id: " + id);
            return ResponseEntity.notFound().build();
        }
    }
    
    @PostMapping("/users")
    @Transactional
    public ResponseEntity<User> createUser(@RequestBody UserCreateRequest request) {
        logger.info("Creating new user: " + request.getEmail());
        
        try {
            User user = userService.createUser(request);
            auditService.logCreation("POST /users", user.getId());
            notificationService.sendWelcomeEmail(user);
            return ResponseEntity.status(201).body(user);
        } catch (Exception e) {
            logger.severe("Failed to create user: " + e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }
    
    @PutMapping("/users/{id}")
    @Transactional
    public ResponseEntity<User> updateUser(@PathVariable Long id, 
                                         @RequestBody UserUpdateRequest request) {
        logger.info("Updating user with id: " + id);
        
        try {
            User updatedUser = userService.updateUser(id, request);
            auditService.logModification("PUT /users/" + id, updatedUser.getId());
            notificationService.sendUpdateNotification(updatedUser);
            return ResponseEntity.ok(updatedUser);
        } catch (Exception e) {
            logger.severe("Failed to update user: " + e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }
    
    @DeleteMapping("/users/{id}")
    @Transactional
    public ResponseEntity<Void> deleteUser(@PathVariable Long id) {
        logger.info("Deleting user with id: " + id);
        
        try {
            userService.deleteUser(id);
            auditService.logDeletion("DELETE /users/" + id, id);
            return ResponseEntity.noContent().build();
        } catch (Exception e) {
            logger.severe("Failed to delete user: " + e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }
    
    @PostMapping("/auth/login")
    public ResponseEntity<AuthResponse> login(@RequestBody LoginRequest request) {
        logger.info("Login attempt for: " + request.getEmail());
        
        try {
            User user = authService.authenticate(request.getEmail(), request.getPassword());
            if (user != null) {
                String token = authService.generateToken(user);
                auditService.logAuthentication("POST /auth/login", user.getId(), true);
                notificationService.sendLoginNotification(user);
                return ResponseEntity.ok(new AuthResponse(token, user));
            } else {
                auditService.logAuthentication("POST /auth/login", null, false);
                return ResponseEntity.status(401).build();
            }
        } catch (Exception e) {
            logger.severe("Login error: " + e.getMessage());
            return ResponseEntity.status(500).build();
        }
    }
    
    @GetMapping("/users/search")
    public ResponseEntity<List<User>> searchUsers(@RequestParam String query,
                                                 @RequestParam(defaultValue = "0") int page,
                                                 @RequestParam(defaultValue = "20") int size) {
        logger.info("Searching users with query: " + query);
        
        List<User> users = userService.searchUsers(query, page, size);
        auditService.logAccess("GET /users/search?q=" + query, users.size());
        return ResponseEntity.ok(users);
    }
    
    @PostMapping("/users/bulk")
    @Transactional
    public ResponseEntity<BulkOperationResult> bulkCreateUsers(@RequestBody List<UserCreateRequest> requests) {
        logger.info("Bulk creating " + requests.size() + " users");
        
        ExecutorService executor = Executors.newFixedThreadPool(4);
        
        try {
            List<CompletableFuture<User>> futures = requests.stream()
                .map(request -> CompletableFuture.supplyAsync(() -> {
                    try {
                        return userService.createUser(request);
                    } catch (Exception e) {
                        logger.warning("Failed to create user in bulk: " + e.getMessage());
                        return null;
                    }
                }, executor))
                .collect(Collectors.toList());
            
            List<User> createdUsers = futures.stream()
                .map(CompletableFuture::join)
                .filter(user -> user != null)
                .collect(Collectors.toList());
            
            auditService.logBulkOperation("POST /users/bulk", createdUsers.size());
            notificationService.sendBulkCreationNotification(createdUsers);
            
            return ResponseEntity.ok(new BulkOperationResult(createdUsers.size(), requests.size()));
        } finally {
            executor.shutdown();
        }
    }
}

@Service
public class UserService {
    
    private static final Logger logger = Logger.getLogger(UserService.class.getName());
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private EmailValidator emailValidator;
    
    @Autowired
    private PasswordHasher passwordHasher;
    
    public List<User> findAll() {
        return userRepository.findAll();
    }
    
    public Optional<User> findById(Long id) {
        return userRepository.findById(id);
    }
    
    @Transactional
    public User createUser(UserCreateRequest request) {
        if (!emailValidator.isValid(request.getEmail())) {
            throw new IllegalArgumentException("Invalid email format");
        }
        
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new IllegalArgumentException("Email already exists");
        }
        
        String hashedPassword = passwordHasher.hash(request.getPassword());
        User user = new User(request.getName(), request.getEmail(), hashedPassword);
        
        User savedUser = userRepository.save(user);
        logger.info("Created user with id: " + savedUser.getId());
        return savedUser;
    }
    
    @Transactional
    public User updateUser(Long id, UserUpdateRequest request) {
        User user = userRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("User not found"));
        
        if (request.getName() != null) {
            user.setName(request.getName());
        }
        
        if (request.getEmail() != null) {
            if (!emailValidator.isValid(request.getEmail())) {
                throw new IllegalArgumentException("Invalid email format");
            }
            user.setEmail(request.getEmail());
        }
        
        User updatedUser = userRepository.save(user);
        logger.info("Updated user with id: " + updatedUser.getId());
        return updatedUser;
    }
    
    @Transactional
    public void deleteUser(Long id) {
        if (!userRepository.existsById(id)) {
            throw new IllegalArgumentException("User not found");
        }
        
        userRepository.deleteById(id);
        logger.info("Deleted user with id: " + id);
    }
    
    public List<User> searchUsers(String query, int page, int size) {
        return userRepository.searchByQuery(query, page, size);
    }
}

@Service
public class AuthenticationService {
    
    private static final Logger logger = Logger.getLogger(AuthenticationService.class.getName());
    private final Clock clock = Clock.systemUTC();
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private PasswordHasher passwordHasher;
    
    @Autowired
    private JwtTokenProvider tokenProvider;
    
    public User authenticate(String email, String password) {
        Optional<User> userOpt = userRepository.findByEmail(email);
        
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            if (passwordHasher.verify(password, user.getPasswordHash())) {
                logger.info("Authentication successful for: " + email);
                return user;
            } else {
                logger.warning("Authentication failed for: " + email + " - invalid password");
            }
        } else {
            logger.warning("Authentication failed for: " + email + " - user not found");
        }
        
        return null;
    }
    
    public String generateToken(User user) {
        Instant now = clock.instant();
        Instant expiry = now.plusSeconds(3600); // 1 hour
        
        return tokenProvider.generateToken(user.getId(), user.getEmail(), expiry);
    }
    
    public boolean validateToken(String token) {
        try {
            return tokenProvider.validateToken(token);
        } catch (Exception e) {
            logger.warning("Token validation failed: " + e.getMessage());
            return false;
        }
    }
}

@Service
public class AuditService {
    
    private static final Logger logger = Logger.getLogger(AuditService.class.getName());
    private final Clock clock = Clock.systemUTC();
    
    @Autowired
    private AuditRepository auditRepository;
    
    public void logAccess(String endpoint, int count) {
        AuditEntry entry = new AuditEntry(
            "ACCESS",
            endpoint,
            count,
            clock.instant()
        );
        auditRepository.save(entry);
        logger.info("Audit: " + endpoint + " accessed " + count + " records");
    }
    
    public void logCreation(String endpoint, Long entityId) {
        AuditEntry entry = new AuditEntry(
            "CREATE",
            endpoint,
            entityId,
            clock.instant()
        );
        auditRepository.save(entry);
        logger.info("Audit: " + endpoint + " created entity " + entityId);
    }
    
    public void logModification(String endpoint, Long entityId) {
        AuditEntry entry = new AuditEntry(
            "MODIFY",
            endpoint,
            entityId,
            clock.instant()
        );
        auditRepository.save(entry);
        logger.info("Audit: " + endpoint + " modified entity " + entityId);
    }
    
    public void logDeletion(String endpoint, Long entityId) {
        AuditEntry entry = new AuditEntry(
            "DELETE",
            endpoint,
            entityId,
            clock.instant()
        );
        auditRepository.save(entry);
        logger.info("Audit: " + endpoint + " deleted entity " + entityId);
    }
    
    public void logAuthentication(String endpoint, Long userId, boolean success) {
        AuditEntry entry = new AuditEntry(
            success ? "AUTH_SUCCESS" : "AUTH_FAILURE",
            endpoint,
            userId,
            clock.instant()
        );
        auditRepository.save(entry);
        logger.info("Audit: " + endpoint + " authentication " + (success ? "succeeded" : "failed") + " for user " + userId);
    }
    
    public void logBulkOperation(String endpoint, int count) {
        AuditEntry entry = new AuditEntry(
            "BULK_OPERATION",
            endpoint,
            count,
            clock.instant()
        );
        auditRepository.save(entry);
        logger.info("Audit: " + endpoint + " bulk operation processed " + count + " items");
    }
}

@Service
public class NotificationService {
    
    private static final Logger logger = Logger.getLogger(NotificationService.class.getName());
    
    @Autowired
    private EmailService emailService;
    
    @Autowired
    private PushNotificationService pushService;
    
    public void sendWelcomeEmail(User user) {
        try {
            String subject = "Welcome to Our Platform!";
            String body = "Hello " + user.getName() + ",\n\nWelcome to our platform!";
            emailService.sendEmail(user.getEmail(), subject, body);
            logger.info("Welcome email sent to: " + user.getEmail());
        } catch (Exception e) {
            logger.warning("Failed to send welcome email to " + user.getEmail() + ": " + e.getMessage());
        }
    }
    
    public void sendUpdateNotification(User user) {
        try {
            String subject = "Your Account Has Been Updated";
            String body = "Hello " + user.getName() + ",\n\nYour account information has been updated.";
            emailService.sendEmail(user.getEmail(), subject, body);
            logger.info("Update notification sent to: " + user.getEmail());
        } catch (Exception e) {
            logger.warning("Failed to send update notification to " + user.getEmail() + ": " + e.getMessage());
        }
    }
    
    public void sendLoginNotification(User user) {
        try {
            String subject = "New Login Detected";
            String body = "Hello " + user.getName() + ",\n\nA new login was detected for your account.";
            emailService.sendEmail(user.getEmail(), subject, body);
            logger.info("Login notification sent to: " + user.getEmail());
        } catch (Exception e) {
            logger.warning("Failed to send login notification to " + user.getEmail() + ": " + e.getMessage());
        }
    }
    
    public void sendBulkCreationNotification(List<User> users) {
        for (User user : users) {
            sendWelcomeEmail(user);
        }
        logger.info("Bulk creation notifications sent to " + users.size() + " users");
    }
}

public record UserCreateRequest(String name, String email, String password) {}
public record UserUpdateRequest(String name, String email) {}
public record LoginRequest(String email, String password) {}
public record AuthResponse(String token, User user) {}
public record BulkOperationResult(int created, int total) {}

public class User {
    private Long id;
    private String name;
    private String email;
    private String passwordHash;
    private Instant createdAt;
    private Instant updatedAt;
    
    public User(String name, String email, String passwordHash) {
        this.name = name;
        this.email = email;
        this.passwordHash = passwordHash;
        this.createdAt = Instant.now();
        this.updatedAt = Instant.now();
    }
    
    // Getters and setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public String getPasswordHash() { return passwordHash; }
    public void setPasswordHash(String passwordHash) { this.passwordHash = passwordHash; }
    
    public Instant getCreatedAt() { return createdAt; }
    public void setCreatedAt(Instant createdAt) { this.createdAt = createdAt; }
    
    public Instant getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(Instant updatedAt) { this.updatedAt = updatedAt; }
}

public class AuditEntry {
    private Long id;
    private String action;
    private String endpoint;
    private Object data;
    private Instant timestamp;
    
    public AuditEntry(String action, String endpoint, Object data, Instant timestamp) {
        this.action = action;
        this.endpoint = endpoint;
        this.data = data;
        this.timestamp = timestamp;
    }
    
    // Getters and setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getAction() { return action; }
    public void setAction(String action) { this.action = action; }
    
    public String getEndpoint() { return endpoint; }
    public void setEndpoint(String endpoint) { this.endpoint = endpoint; }
    
    public Object getData() { return data; }
    public void setData(Object data) { this.data = data; }
    
    public Instant getTimestamp() { return timestamp; }
    public void setTimestamp(Instant timestamp) { this.timestamp = timestamp; }
}
"#;

    // Run the benchmark multiple times to generate flamegraph data
    for i in 0..1000 {
        let parser = GenericParser::default_for_language(SupportedLanguage::Java);
        let rule_manager = RuleManager::new(SupportedLanguage::Java);
        let analyzer = JavaAnalyzer::new();

        let parse_result = parser.parse(complex_java_code, Some("test.java")).unwrap();
        let matches = run_rules(&parse_result.ast, Some("test.java"), &rule_manager);
        let _result = analyzer.analyze(&matches, &parse_result).unwrap();

        if i % 100 == 0 {
            println!("Processed iteration {i}");
        }
    }

    println!("Java flamegraph benchmark completed!");
}
