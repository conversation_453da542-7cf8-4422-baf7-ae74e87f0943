use parser_core::{
    DefinitionLookup,
    csharp::analyzer::CSharpA<PERSON><PERSON><PERSON>,
    parser::{<PERSON><PERSON><PERSON><PERSON><PERSON>, LanguageParser, SupportedLanguage},
    rules::{RuleManager, run_rules},
};

fn main() {
    let complex_csharp_code = include_str!("../../src/csharp/fixtures/ComprehensiveCSharp.cs");

    let parser = GenericParser::default_for_language(SupportedLanguage::CSharp);
    let rule_manager = RuleManager::new(SupportedLanguage::CSharp);
    let analyzer = CSharpAnalyzer::new();

    let parse_result = parser
        .parse(complex_csharp_code, Some("complex_csharp_benchmark.cs"))
        .unwrap();

    println!("Starting C# definition extraction flamegraph benchmark...");
    println!("Code size: {} characters", complex_csharp_code.len());
    println!("Running the full definition extraction pipeline...");

    let matches = run_rules(
        &parse_result.ast,
        Some("complex_csharp_benchmark.cs"),
        &rule_manager,
    );

    let analysis_result = analyzer.analyze(&matches, &parse_result).unwrap();

    let _definition_count = analysis_result.definitions.len();
    let _counts_by_type = analysis_result.count_definitions_by_type();
    let _definitions_with_fqn = analysis_result.definitions_with_fqn();
    let _definition_names = analysis_result.definition_names();

    println!(
        "Completed - Found {} definitions, {} with FQN",
        analysis_result.definitions.len(),
        analysis_result.definitions_with_fqn().len()
    );

    println!("Flamegraph benchmark completed!");
    println!("This profile should show hotspots in:");
    println!("  - Rule pattern matching");
    println!("  - FQN computation and AST traversal");
    println!("  - Definition extraction and classification");
    println!("  - Metadata computation and aggregation");
}
