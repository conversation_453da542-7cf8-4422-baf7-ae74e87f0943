use criterion::{Batch<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>d, <PERSON>riterion, criterion_group, criterion_main};
use parser_core::{
    DefinitionLookup,
    kotlin::analyzer::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    parser::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, SupportedLanguage},
    rules::{RuleManager, run_rules},
};
use std::hint::black_box;

fn bench_e2e(c: &mut Criterion) {
    let code_samples = vec![
        (
            "comprehensive_kotlin_definitions",
            r#"
package com.project.access;

object Time {
   val utcClock = Clock.systemUTC()
}

@Target(AnnotationTarget.CLASS)
annotation class Disposable

data class Project(
   private val absolutePath: String,
   private val name: String
) {
   companion object {
       fun default(): Project = Project("~/", "default-project")
   }
}

fun Project.display(): String {
   return "[$absolutePath] $name"
}

const val BASE_URL = "localhost:8000"

val String.urlAndPort: Pair<String, String> 
  get() = split(":").let { it[0] to it[1] }

val httpClient by lazy { HttpClient() }

enum class AccessResult(val message: String) {
   UNKNOWN_PROJECT("Unknown project"),
   ACCESS_EXPIRED("Access expired"),
   ACCESS_OK("Access ok")
}

@Disposable
class ProjectAccessService(
   private val project: Project
) {
   companion object {
       private val logger: Logger = logger<ProjectAccessService>()
   }

   constructor() : this(Project.default())

   private val clock = Time.clock

   fun validateAccess(target: String): AccessResult {
       val requestUrl = "$BASE_URL/access/${project.name}?target=$target&time=${clock.utc()}"
       
       return httpClient
           .get(requestUrl)
           .unsafeInto<AccessResult>()
   }

   fun revokeAccess(target: String) {
       val requestUrl = "$BASE_URL/access/${project.name}/revoke"

       val body = json {
           "target" to target
           "time" to clock.utc()
       }
       
       httpClient
           .post(requestUrl, body, log)
   }
}

fun main() {
   val project = Project("~/project", "sample")
   println("Loaded ${project.display()}.")

   val (url, port) = BASE_URL.urlAndPort
   println("Service url: $url and port $port")

   val service = ProjectAccessService(project)
   println(service.validateAccess(Project.default()))
}
"#,
        ),
        (
            "simple_class",
            r#"
package com.example

class User(
    private val name: String,
    private val email: String
) {
    fun greeting(): String {
        return "Hello, $name!"
    }
    
    fun getEmail(): String = email
}
"#,
        ),
        (
            "service_with_companion",
            r#"
package com.example.service

interface AuthenticationService {
    fun authenticate(email: String, password: String): Boolean
}

class CredentialsChecker(
    private val user: User,
    private val password: String
) {
    fun isValidPassword(): Boolean {
        println("Checking password for user: ${user.email}")
        return password == "password123"
    }
}

class UserAuthenticationService : AuthenticationService {
    companion object {
        private val logger = LoggerFactory.getLogger(UserAuthenticationService::class.java)
        
        fun createDefault(): UserAuthenticationService {
            return UserAuthenticationService()
        }
    }
    
    override fun authenticate(email: String, password: String): Boolean {
        val user = User.findByEmail(email) ?: return false
        val checker = CredentialsChecker(user, password)
        
        return if (checker.isValidPassword()) {
            logger.info("Authentication successful for $email")
            true
        } else {
            logger.warn("Authentication failed for $email")
            false
        }
    }
}
"#,
        ),
        (
            "data_classes_and_extensions",
            r#"
package com.example.models

data class User(
    val id: Long,
    val name: String,
    val email: String,
    val createdAt: LocalDateTime = LocalDateTime.now()
) {
    companion object {
        fun create(name: String, email: String): User {
            return User(0L, name, email)
        }
    }
}

data class Project(
    val id: Long,
    val name: String,
    val owner: User,
    val members: List<User> = emptyList()
)

fun User.fullName(): String = "$name ($email)"

fun Project.addMember(user: User): Project {
    return copy(members = members + user)
}

fun Project.removeMember(userId: Long): Project {
    return copy(members = members.filter { it.id != userId })
}

val Project.memberCount: Int
    get() = members.size

val Project.isActive: Boolean
    get() = members.isNotEmpty()
"#,
        ),
    ];

    bench_different_complexity(c, &code_samples);
}

fn bench_different_complexity(c: &mut Criterion, code_samples: &Vec<(&str, &str)>) {
    let mut group = c.benchmark_group("e2e_kotlin_workflow_with_definition_extraction");
    group.sample_size(50);

    for (name, code) in code_samples {
        group.bench_with_input(BenchmarkId::new("full_workflow", name), code, |b, code| {
            b.iter_batched(
                || {
                    // Setup: fresh instances for each iteration
                    let parser = GenericParser::default_for_language(SupportedLanguage::Kotlin);
                    let rule_manager = RuleManager::new(SupportedLanguage::Kotlin);
                    let analyzer = KotlinAnalyzer::new();
                    (parser, rule_manager, analyzer)
                },
                |(parser, rule_manager, analyzer)| {
                    // Full e2e workflow: Parse -> Rules -> Analysis -> Definition Extraction
                    let parse_result = parser.parse(black_box(code), Some("test.kt")).unwrap();
                    let matches = run_rules(&parse_result.ast, Some("test.kt"), &rule_manager);
                    let analysis_result = analyzer.analyze(&matches, &parse_result).unwrap();

                    black_box((
                        analysis_result.definitions.len(),
                        analysis_result.count_definitions_by_type().len(),
                        analysis_result.kotlin_definition_fqn_strings().len(),
                    ))
                },
                BatchSize::SmallInput,
            )
        });
    }
    group.finish();
}

criterion_group!(benches, bench_e2e);
criterion_main!(benches);
