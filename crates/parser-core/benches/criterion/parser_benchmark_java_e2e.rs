use criterion::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, criterion_group, criterion_main};
use parser_core::{
    java::analyzer::<PERSON><PERSON><PERSON><PERSON><PERSON>,
    parser::{<PERSON><PERSON><PERSON><PERSON><PERSON>, LanguageParser, SupportedLanguage},
    rules::{RuleManager, run_rules},
};
use std::hint::black_box;

fn bench_e2e(c: &mut Criterion) {
    let code_samples = vec![
        (
            "simple_class",
            r#"
public class User {
    private String name;
    private String email;
    
    public User(String name, String email) {
        this.name = name;
        this.email = email;
    }
    
    public String getGreeting() {
        return "Hello, " + name + "!";
    }
}
"#,
        ),
        (
            "authentication_service",
            r#"
import java.time.Clock;
import java.util.logging.Logger;

public class AuthenticationService {
    private static final Logger logger = Logger.getLogger(AuthenticationService.class.getName());
    private final Clock clock = Clock.systemUTC();
    
    public static class CredentialsChecker {
        private final User user;
        private final String password;
        
        public CredentialsChecker(User user, String password) {
            this.user = user;
            this.password = password;
        }
        
        public boolean isValidPassword() {
            System.out.println("Checking password for user: " + user.getEmail());
            return "password123".equals(password);
        }
    }
    
    public static User authenticate(String email, String password) {
        User user = User.findByEmail(email);
        if (user == null) {
            return null;
        }
        
        CredentialsChecker checker = new CredentialsChecker(user, password);
        
        if (checker.isValidPassword()) {
            System.out.println("Authentication successful for " + user.getEmail());
            AuditLogger auditLogger = new AuditLogger(user);
            auditLogger.logEvent("Authentication successful for " + user.getEmail());
            return user;
        } else {
            System.out.println("Authentication failed for " + user.getEmail());
            return null;
        }
    }
    
    public static class AuditLogger {
        private final User user;
        
        public AuditLogger(User user) {
            this.user = user;
        }
        
        public void logEvent(String message) {
            System.out.println("[AUDIT][" + java.time.Instant.now() + "] " + message);
        }
    }
}
"#,
        ),
        (
            "spring_controller",
            r#"
import org.springframework.web.bind.annotation.*;
import org.springframework.http.ResponseEntity;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/v1")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private AuthenticationService authService;
    
    @GetMapping("/users")
    public ResponseEntity<List<User>> getAllUsers() {
        List<User> users = userService.findAll();
        return ResponseEntity.ok(users);
    }
    
    @GetMapping("/users/{id}")
    public ResponseEntity<User> getUserById(@PathVariable Long id) {
        Optional<User> user = userService.findById(id);
        return user.map(ResponseEntity::ok)
                  .orElse(ResponseEntity.notFound().build());
    }
    
    @PostMapping("/users")
    public ResponseEntity<User> createUser(@RequestBody UserCreateRequest request) {
        User user = userService.createUser(request);
        return ResponseEntity.status(201).body(user);
    }
    
    @PutMapping("/users/{id}")
    public ResponseEntity<User> updateUser(@PathVariable Long id, 
                                         @RequestBody UserUpdateRequest request) {
        User updatedUser = userService.updateUser(id, request);
        return ResponseEntity.ok(updatedUser);
    }
    
    @DeleteMapping("/users/{id}")
    public ResponseEntity<Void> deleteUser(@PathVariable Long id) {
        userService.deleteUser(id);
        return ResponseEntity.noContent().build();
    }
    
    @PostMapping("/auth/login")
    public ResponseEntity<AuthResponse> login(@RequestBody LoginRequest request) {
        User user = authService.authenticate(request.getEmail(), request.getPassword());
        if (user != null) {
            String token = authService.generateToken(user);
            return ResponseEntity.ok(new AuthResponse(token, user));
        }
        return ResponseEntity.status(401).build();
    }
}
"#,
        ),
        (
            "comprehensive_definitions",
            r#"
package com.project.access;

import java.time.Clock;
import java.net.http.HttpClient;
import java.util.logging.Logger;

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface Disposable {
    String value() default "";
    int count() default 0;
}

public class Time {
    public static final Clock utcClock = Clock.systemUTC();
}

public record Project(String absolutePath, String name) {
    public static Project default() {
        return new Project("~/", "default-project");
    }

    public String display() {
        return "[" + absolutePath + "] " + name;
    }
}

public class Constants {
    public static final String BASE_URL = "localhost:8000";
}

public enum AccessResult {
    UNKNOWN_PROJECT("Unknown project"),
    ACCESS_EXPIRED("Access expired"),
    ACCESS_OK("Access ok");

    private final String message;

    AccessResult(String message) {
        this.message = message;
    }

    public String getMessage() {
        return message;
    }
}

public interface IProjectAccessService {
    AccessResult validateAccess(String target);
    void revokeAccess(String target);
}

@Disposable
public class ProjectAccessService implements IProjectAccessService {
    private final Project project;
    private final Logger logger = Logger.getLogger(ProjectAccessService.class.getName());
    private final Clock clock = Time.utcClock;

    public ProjectAccessService(Project project) {
        this.project = project;
    }

    public ProjectAccessService() {
        this(Project.default());
    }

    @Override
    public AccessResult validateAccess(String target) {
        String requestUrl = Constants.BASE_URL + "/access/" + project.name + "?target=" + target + "&time=" + clock.instant();
        return AccessResult.ACCESS_OK;
    }

    @Override
    public void revokeAccess(String target) {
        String requestUrl = Constants.BASE_URL + "/access/" + project.name + "/revoke";
    }
}

public record Person(String name, int age) {
    public Person {
        if (age < 0) {
            throw new IllegalArgumentException("Age cannot be negative");
        }
    }

    public String getDisplayName() {
        return name + " (" + age + ")";
    }
}

public class Main {
    public static void main(String[] args) {
        Project project = new Project("~/project", "sample");
        System.out.println("Loaded " + project.display() + ".");

        String[] urlParts = Constants.BASE_URL.split(":");
        String url = urlParts[0];
        String port = urlParts[1];
        System.out.println("Service url: " + url + " and port " + port);

        ProjectAccessService service = new ProjectAccessService(project);
        System.out.println(service.validateAccess(Project.default().name));
    }
}
"#,
        ),
    ];

    let mut group = c.benchmark_group("java_e2e");
    group.sample_size(10);

    for (name, code) in &code_samples {
        group.bench_with_input(
            BenchmarkId::new("parse_and_analyze", name),
            code,
            |b, &code| {
                b.iter_batched(
                    || {
                        let parser = GenericParser::default_for_language(SupportedLanguage::Java);
                        let rule_manager = RuleManager::new(SupportedLanguage::Java);
                        let analyzer = JavaAnalyzer::new();
                        (parser, rule_manager, analyzer, code)
                    },
                    |(parser, rule_manager, analyzer, code)| {
                        let parse_result =
                            black_box(parser.parse(code, Some("test.java")).unwrap());
                        let matches = black_box(run_rules(
                            &parse_result.ast,
                            Some("test.java"),
                            &rule_manager,
                        ));
                        let _result = black_box(analyzer.analyze(&matches, &parse_result).unwrap());
                    },
                    BatchSize::SmallInput,
                )
            },
        );
    }

    group.finish();
}

fn bench_parse_only(c: &mut Criterion) {
    let code_samples = vec![
        ("simple", "public class Test { }"),
        (
            "medium",
            r#"
public class Test {
    private String name;
    public Test(String name) { this.name = name; }
    public String getName() { return name; }
}
"#,
        ),
        (
            "complex",
            r#"
package com.example;

import java.util.List;
import java.util.Optional;

@RestController
public class TestController {
    @Autowired
    private TestService service;
    
    @GetMapping("/test")
    public ResponseEntity<Test> getTest(@PathVariable Long id) {
        Optional<Test> test = service.findById(id);
        return test.map(ResponseEntity::ok)
                  .orElse(ResponseEntity.notFound().build());
    }
}
"#,
        ),
    ];

    let mut group = c.benchmark_group("java_parse_only");
    group.sample_size(10);

    for (name, code) in &code_samples {
        group.bench_with_input(BenchmarkId::new("parse", name), code, |b, &code| {
            b.iter_batched(
                || GenericParser::default_for_language(SupportedLanguage::Java),
                |parser| {
                    let _result = black_box(parser.parse(code, Some("test.java")).unwrap());
                },
                BatchSize::SmallInput,
            )
        });
    }

    group.finish();
}

criterion_group!(benches, bench_e2e, bench_parse_only);
criterion_main!(benches);
