use criterion::{BatchS<PERSON>, <PERSON><PERSON><PERSON><PERSON>d, <PERSON>riterion, criterion_group, criterion_main};
use parser_core::{
    DefinitionLookup,
    parser::{<PERSON><PERSON><PERSON><PERSON><PERSON>, LanguageParser, SupportedLanguage},
    ruby::analyzer::Ruby<PERSON><PERSON>y<PERSON>,
    rules::{RuleManager, run_rules},
};
use std::hint::black_box;

fn bench_e2e(c: &mut Criterion) {
    let code_samples = vec![
        (
            "simple_class",
            r#"
class User
  attr_reader :name, :email
  
  def initialize(name, email)
    @name = name
    @email = email
  end
  
  def greeting
    "Hello, #{@name}!"
  end
end
"#,
        ),
        (
            "authentication_service",
            r#"
require_relative '../models/user'

module AuthenticationService
  class Credentials<PERSON>he<PERSON>
    def initialize(user, password)
      @user = user
      @password = password
    end

    def valid_password?
      puts "Checking password for user: #{@user.email}"
      @password == "password123"
    end
  end

  def self.authenticate(email, password)
    user = User.find_by_email(email)
    return nil unless user

    checker = CredentialsChecker.new(user, password)

    if checker.valid_password?
      puts "Authentication successful for #{user.email}"
      audit_logger = AuthenticationService::Utilities::Audit.new(user)
      audit_logger.log_event("Authentication successful for #{user.email}")
      user
    else
      puts "Authentication failed for #{user.email}"
      nil
    end
  end

  module Utilities
    class Audit
      def self.log_event(message)
        puts "[AUDIT][#{Time.now}] #{message}"
      end
    end
  end
end
"#,
        ),
        (
            "rails_controller",
            r#"
class JwtController < ApplicationController
  skip_around_action :set_session_storage
  skip_before_action :authenticate_user!
  skip_before_action :verify_authenticity_token

  prepend_before_action :auth_user, :authenticate_project_or_user
  around_action :bypass_admin_mode!, if: :auth_user

  feature_category :container_registry
  urgency :low

  SERVICES = {
    ::Auth::ContainerRegistryAuthenticationService::AUDIENCE => ::Auth::ContainerRegistryAuthenticationService,
    ::Auth::DependencyProxyAuthenticationService::AUDIENCE => ::Auth::DependencyProxyAuthenticationService
  }.freeze

  def auth
    service = SERVICES[params[:service]]
    return head :not_found unless service

    result = service.new(@authentication_result.project, auth_user, auth_params)
      .execute(authentication_abilities: @authentication_result.authentication_abilities)

    render json: result, status: result[:http_status]
  end

  private

  def authenticate_project_or_user
    @authentication_result = Gitlab::Auth::Result.new(
      nil,
      nil,
      :none,
      Gitlab::Auth.read_only_authentication_abilities
    )

    authenticate_with_http_basic do |login, password|
      @authentication_result = Gitlab::Auth.find_for_git_client(login, password, project: nil, request: request)
      @raw_token = password if @authentication_result.type == :personal_access_token

      if @authentication_result.failed?
        log_authentication_failed(login, @authentication_result)
        render_access_denied
      end
    end
  rescue Gitlab::Auth::MissingPersonalAccessTokenError
    render_access_denied
  end

  def self.authenticate
    // Class method implementation
  end

  class << self
    def inherited(subclass)
      subclass.extend(ClassMethods)
    end
  end
end
"#,
        ),
        (
            "comprehensive_definitions",
            r#"
module DataProcessing
  VERSION = "1.0.0"
  CONFIG = { timeout: 30, retries: 3 }

  VALIDATOR = lambda do |input|
    input.is_a?(Hash) && input[:type].present?
  end

  TRANSFORMER = lambda { |data| data.transform_keys(&:to_sym) }

  class Processor
    attr_reader :name, :status
    attr_writer :config
    attr_accessor :timeout, :retries
    attr_reader "computed_field"

    DEFAULT_TIMEOUT = 30
    PRIORITY_LEVELS = %w[low medium high critical].freeze

    def initialize(name)
      @name = name
      @status = :ready
      @config = {}
      
      @formatter = lambda { |data| JSON.pretty_generate(data) }
      
      @error_handler = Proc.new do |error|
        puts "Error in #{@name}: #{error.message}"
      end
    end

    def process(data)
      return false unless VALIDATOR.call(data)
      
      data.each_with_index do |item, index|
        yield item, index if block_given?
      end
      
      @status = :complete
      true
    end

    def handle_batch(items)
      items.map.with_index do |item, index|
        transform_item(item, index)
      end
    end

    def self.create_default
      new("default_processor")
    end

    def self.bulk_process(items)
      processor = create_default
      processor.handle_batch(items)
    end

    private

    def transform_item(item, index)
      TRANSFORMER.call(item.merge(index: index))
    end
  end

  module Utilities
    logger = lambda do |level, message|
      puts "[#{level.upcase}] #{Time.now}: #{message}"
    end

    @@cache_cleaner = Proc.new { |key| Rails.cache.delete(key) }

    def self.log_info(message)
      logger.call(:info, message)
    end

    class Cache
      attr_accessor :store, :ttl
      attr_reader :hits, :misses

      CACHE_STRATEGIES = {
        memory: Proc.new { |key, value| @memory_store[key] = value },
        redis: Proc.new { |key, value| Redis.current.set(key, value) }
      }.freeze

      def initialize
        @store = {}
        @hits = 0
        @misses = 0
        
        @hit_tracker = lambda do |operation|
          case operation
          when :hit
            @hits += 1
          when :miss  
            @misses += 1
          end
        end
      end

      def get(key, &default_block)
        if @store.key?(key)
          @hit_tracker.call(:hit)
          @store[key]
        else
          @hit_tracker.call(:miss)
          default_block&.call
        end
      end
    end
  end

  $global_processor = lambda { |data| DataProcessing::Processor.new("global").process(data) }
end

class ConfigurationManager
  attr_reader :environment, :version
  attr_writer :debug_mode
  attr_accessor :log_level, :max_connections
  attr_reader :created_at, :updated_at, :last_accessed

  ENV_DEFAULTS = {
    development: Proc.new { { debug: true, log_level: :debug } },
    production: Proc.new { { debug: false, log_level: :warn } }
  }.freeze

  CONFIG_VALIDATOR = lambda do |config|
    required_keys = %i[environment version log_level]
    required_keys.all? { |key| config.key?(key) }
  end

  def initialize(env = :development)
    @environment = env
    @version = "1.0"
    @log_level = :info
    @created_at = Time.now
    
    configure_defaults do |config|
      config[:timeout] = 30
      config[:retries] = 3
    end
  end

  def configure_defaults(&block)
    config = ENV_DEFAULTS[@environment].call
    block.call(config) if block_given?
    @config = config
  end

  def self.load_from_file(path)
    new
  end
end

error_reporter = Proc.new do |exception, context|
  puts "Error: #{exception.message} in #{context}"
end
"#,
        ),
    ];

    bench_different_complexity(c, &code_samples);
}

fn bench_different_complexity(c: &mut Criterion, code_samples: &Vec<(&str, &str)>) {
    let mut group = c.benchmark_group("e2e_workflow_with_definition_extraction");
    group.sample_size(50);

    for (name, code) in code_samples {
        group.bench_with_input(BenchmarkId::new("full_workflow", name), code, |b, code| {
            b.iter_batched(
                || {
                    // Setup: fresh instances for each iteration
                    let parser = GenericParser::default_for_language(SupportedLanguage::Ruby);
                    let rule_manager = RuleManager::new(SupportedLanguage::Ruby);
                    let analyzer = RubyAnalyzer::new();
                    (parser, rule_manager, analyzer)
                },
                |(parser, rule_manager, analyzer)| {
                    // Full e2e workflow: Parse -> Rules -> Analysis -> Definition Extraction
                    let parse_result = parser.parse(black_box(code), Some("test.rb")).unwrap();
                    let matches = run_rules(&parse_result.ast, Some("test.rb"), &rule_manager);
                    let analysis_result = analyzer.analyze(&matches, &parse_result).unwrap();

                    black_box((
                        analysis_result.definitions.len(),
                        analysis_result.count_definitions_by_type().len(),
                        analysis_result.ruby_definition_fqn_strings().len(),
                    ))
                },
                BatchSize::SmallInput,
            )
        });
    }
    group.finish();
}

criterion_group!(benches, bench_e2e);
criterion_main!(benches);
