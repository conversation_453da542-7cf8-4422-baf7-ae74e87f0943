use crate::fqn::{FQNPart, Fqn};
use crate::python::definitions::PythonDefinitionType;
use crate::utils::{Range, node_to_range};
use ast_grep_core::tree_sitter::StrDoc;
use ast_grep_core::{AstGrep, Node};
use ast_grep_language::SupportLang;
use std::collections::HashMap;
use std::sync::Arc;

pub type PythonFqnPart = FQNPart<PythonDefinitionType>;
pub type PythonFqn = Fqn<PythonFqnPart>;

/// Returns the FQN with metadata as a string, joined by '.'
pub fn python_fqn_to_string(fqn: &PythonFqn) -> String {
    fqn.parts
        .iter()
        .map(|part| part.node_name.replace('.', "#"))
        .collect::<Vec<_>>()
        .join(".")
}

pub type NodeIndexMap<'a> = HashMap<Range, Node<'a, StrDoc<SupportLang>>>;
pub type PythonNodeFqnMap<'a> =
    HashMap<Range, (Node<'a, StrDoc<SupportLang>>, Arc<Vec<PythonFqnPart>>)>;

/// Builds two maps by traversing the AST:
/// - node_fqn_map: Maps byte ranges to (Node, FQN parts with metadata)
/// - node_index_map: Maps byte ranges to Nodes for efficient lookup
pub fn build_fqn_and_node_indices<'a>(
    ast: &'a AstGrep<StrDoc<SupportLang>>,
) -> (PythonNodeFqnMap<'a>, NodeIndexMap<'a>) {
    let mut node_fqn_map = HashMap::new();
    let mut node_index_map = HashMap::new();
    let mut initial_scope: Vec<PythonFqnPart> = Vec::new(); // Scope stack

    compute_fqns_and_index_recursive(
        ast.root(),
        &mut initial_scope,
        &mut node_fqn_map,
        &mut node_index_map,
    );

    (node_fqn_map, node_index_map)
}

/// Helper function that recursively traverses the AST to compute FQNs and build
/// the node index
fn compute_fqns_and_index_recursive<'a>(
    node: Node<'a, StrDoc<SupportLang>>,
    current_scope: &mut Vec<PythonFqnPart>,
    node_fqn_map: &mut PythonNodeFqnMap<'a>,
    node_index_map: &mut NodeIndexMap<'a>,
) {
    // Index all nodes by byte range
    let node_range = node_to_range(&node);
    node_index_map.insert(node_range, node.clone());

    let node_kind = node.kind();
    let mut new_scope_part: Option<PythonFqnPart> = None;

    // Handle class definitions
    if node_kind == "class_definition" {
        if let Some(name_node) = node.field("name") {
            let name = name_node.text().to_string();
            let mut fqn_parts = current_scope.clone();

            // Determine if class is decorated
            let definition_type = if has_decorators(&node) {
                PythonDefinitionType::DecoratedClass
            } else {
                PythonDefinitionType::Class
            };

            new_scope_part = Some(PythonFqnPart::new(
                definition_type,
                name.clone(),
                node_to_range(&name_node),
            ));

            // Push FQN part to scope stack, then store in the map
            fqn_parts.push(PythonFqnPart::new(
                definition_type,
                name,
                node_to_range(&name_node),
            ));
            node_fqn_map.insert(
                node_to_range(&name_node),
                (name_node.clone(), Arc::new(fqn_parts)),
            );
        }
    }
    // Handle function definitions (including async)
    else if node_kind == "function_definition" {
        if let Some(name_node) = node.field("name") {
            let name = name_node.text().to_string();
            let mut fqn_parts = current_scope.clone();

            // Determine the exact type of function/method
            let is_async = node.children().any(|child| child.kind() == "async");
            let has_decorator = has_decorators(&node);
            let is_method = is_in_class_scope(current_scope);

            let definition_type = match (is_method, is_async, has_decorator) {
                (true, true, true) => PythonDefinitionType::DecoratedAsyncMethod,
                (true, true, false) => PythonDefinitionType::AsyncMethod,
                (true, false, true) => PythonDefinitionType::DecoratedMethod,
                (true, false, false) => PythonDefinitionType::Method,
                (false, true, true) => PythonDefinitionType::DecoratedAsyncFunction,
                (false, true, false) => PythonDefinitionType::AsyncFunction,
                (false, false, true) => PythonDefinitionType::DecoratedFunction,
                (false, false, false) => PythonDefinitionType::Function,
            };

            new_scope_part = Some(PythonFqnPart::new(
                definition_type,
                name.clone(),
                node_to_range(&name_node),
            ));

            // Push FQN part to scope stack, then store in the map
            fqn_parts.push(PythonFqnPart::new(
                definition_type,
                name,
                node_to_range(&name_node),
            ));
            node_fqn_map.insert(
                node_to_range(&name_node),
                (name_node.clone(), Arc::new(fqn_parts)),
            );
        }
    }
    // Handle lambda assignments
    else if node_kind == "assignment" {
        if let (Some(left_node), Some(right_node)) = (node.field("left"), node.field("right")) {
            let left_kind = left_node.kind();
            if ["identifier", "attribute"].contains(&&*left_kind)
                && is_lambda_assignment(&right_node)
            {
                let name = if left_kind == "attribute" {
                    extract_attribute_path(&left_node)
                } else {
                    left_node.text().to_string()
                };
                let mut fqn_parts = current_scope.clone();

                // Lambda type remains the same
                let definition_type = PythonDefinitionType::Lambda;
                new_scope_part = Some(PythonFqnPart::new(
                    definition_type,
                    name.clone(),
                    node_to_range(&left_node),
                ));

                // Push FQN part to scope stack, then store in the map
                fqn_parts.push(PythonFqnPart::new(
                    definition_type,
                    name,
                    node_to_range(&left_node),
                ));
                node_fqn_map.insert(
                    node_to_range(&left_node),
                    (left_node.clone(), Arc::new(fqn_parts)),
                );
            }
        }
    } else if [
        "import_statement",
        "import_from_statement",
        "future_import_statement",
    ]
    .contains(&&*node_kind)
    {
        let fqn_parts = current_scope.clone();
        node_fqn_map.insert(node_to_range(&node), (node.clone(), Arc::new(fqn_parts)));
    }

    // If new scope is created, push to stack
    let mut did_add_scope = false;
    if let Some(scope_part) = new_scope_part {
        current_scope.push(scope_part);
        did_add_scope = true;
    }

    // Recurse into children
    for child in node.children() {
        compute_fqns_and_index_recursive(child, current_scope, node_fqn_map, node_index_map);
    }

    // Pop scope part AFTER recursing children
    if did_add_scope {
        current_scope.pop();
    }
}

/// Find Python FQN with metadata
pub fn find_python_fqn_for_node<'a>(
    range: Range,
    node_fqn_map: &PythonNodeFqnMap<'a>,
) -> Option<PythonFqn> {
    node_fqn_map
        .get(&range)
        .map(|(_name_node, fqn_parts_arc)| PythonFqn {
            parts: Arc::clone(fqn_parts_arc),
        })
}

/// Helper function to check if we're in a class scope
fn is_in_class_scope(current_scope: &[PythonFqnPart]) -> bool {
    current_scope
        .last()
        .map(|part| {
            part.node_type == PythonDefinitionType::Class
                || part.node_type == PythonDefinitionType::DecoratedClass
        })
        .unwrap_or(false)
}

/// Helper function to check if a node has decorators
fn has_decorators(node: &Node<StrDoc<SupportLang>>) -> bool {
    if let Some(parent) = node.parent() {
        if parent.kind() == "decorated_definition" {
            return true;
        }
    }

    false
}

/// Check if a node represents a lambda function assignment
fn is_lambda_assignment(node: &Node<StrDoc<SupportLang>>) -> bool {
    let node_kind = node.kind();

    if node_kind == "call" {
        // Handles cases like my_var = (lambda x: x)(1)
        false
    } else if node_kind == "lambda" {
        true
    } else if node_kind == "parenthesized_expression" {
        // Handles cases like my_fn = (lambda x: x)
        if let Some(inner) = node.child(1) {
            is_lambda_assignment(&inner)
        } else {
            false
        }
    } else {
        false
    }
}

/// Extract attribute path (used for attributes in lambda assignments)
fn extract_attribute_path(node: &Node<StrDoc<SupportLang>>) -> String {
    let node_kind = node.kind();
    if node_kind == "attribute" {
        let mut parts = Vec::new();

        // Get the object part (left side of the dot)
        if let Some(object) = node.field("object") {
            parts.push(extract_attribute_path(&object));
        }

        // Get the attribute part (right side of the dot)
        if let Some(attribute) = node.field("attribute") {
            parts.push(attribute.text().to_string());
        }

        parts.join(".")
    } else {
        node.text().to_string()
    }
}
