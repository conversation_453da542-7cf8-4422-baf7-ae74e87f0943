# Regular import
import urllib.parse.deeply.nested

# Regular imports, listed
import numpy, torch.nn

# Aliased import
import xml.etree.ElementTree as ET

# Aliased imports, listed
import numpy as np, torch as pytorch

# From import
from os.path import join

# From imports, listed
from numpy import array, matrix

# Aliased from import
from urllib.parse import quote as url_quote

# Aliased from imports, listed
from typing import List as ListType, Dict as DictType

# Mixed from imports with and without aliases
from collections import namedtuple, defaultdict as DD

# Wildcard import
from tkinter import *

# Relative import
from .. import rel_module

# Relative import, listed
from .. import rel_module1, rel_module2

# Aliased relative import
from .subpackage import something as sth

# Aliased relative import, listed
from .subpackage import one_thing as oth, another_thing as ath

# Relative wildcard import
from ..parent_module import *

# Future import
from __future__ import annotations

# Future imports, listed
from __future__ import print_function, division

# Aliased future import
from __future__ import annotations as annot

# Aliased future imports, listed
from __future__ import print_function as pf, division as div
