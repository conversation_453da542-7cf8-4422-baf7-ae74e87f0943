use crate::kotlin::kotlin_ast::KotlinMatchKind;
use crate::kotlin::types::{
    KotlinDefinitionInfo, KotlinDefinitionType, KotlinFqn, KotlinNodeFqnMap,
};
use crate::rules::{MatchWithNodes, MetaVarNode};
use once_cell::sync::Lazy;
use rustc_hash::FxHashMap;

/// Type-safe constants for capture variable names used in Kotlin rule definitions
pub mod meta_vars {
    pub const CLASS_DEF_NAME: &str = "CLASS_DEF_NAME";
    pub const FUNCTION_DEF_NAME: &str = "FUNCTION_DEF_NAME";
    pub const PROPERTY_DEF_NAME: &str = "PROPERTY_DEF_NAME";
    pub const COMPANION_OBJECT_DEF: &str = "COMPANION_OBJECT_DEF";
    pub const CONSTRUCTOR_DEF: &str = "CONSTRUCTOR_DEF";
    pub const PACKAGE_NAME: &str = "PACKAGE_NAME";
    pub const OBJECT_DEF_NAME: &str = "OBJECT_DEF_NAME";
    pub const ENUM_ENTRY_DEF_NAME: &str = "ENUM_ENTRY_DEF_NAME";
    pub const INTERFACE_DEF_NAME: &str = "INTERFACE_DEF_NAME";
    pub const ENUM_DEF_NAME: &str = "ENUM_DEF_NAME";
    pub const ANNOTATION_CLASS_DEF_NAME: &str = "ANNOTATION_CLASS_DEF_NAME";
    pub const LAMBDA_DEF_NAME: &str = "LAMBDA_DEF_NAME";
}

/// Configuration for extracting different types of definitions
/// Each extractor is configured with a specific definition type and extraction function
#[derive(Debug)]
struct DefinitionExtractor {
    /// The type of definition this extractor handles
    definition_type: KotlinDefinitionType,
    /// Function that extracts the definition name from captured meta variables
    extractor: fn(&FxHashMap<String, crate::rules::MetaVarNode>) -> Option<&MetaVarNode>,
    /// Function that extracts the definition name from captured meta variables
    extract_name: fn(&MetaVarNode) -> String,
}

/// Unified entry point for finding definitions with FQN computation
/// This function combines DefinitionExtractor functionality with FQN computation
/// and ensures all meta_vars are captured for each definition
pub fn find_definitions(
    matches: &[MatchWithNodes],
    kotlin_node_fqn_map: &KotlinNodeFqnMap,
) -> Vec<KotlinDefinitionInfo> {
    let mut definitions = Vec::new();

    for match_item in matches {
        // Only process definition matches
        if KotlinMatchKind::from_rule_id(&match_item.match_info.rule_id)
            != KotlinMatchKind::Definition
        {
            continue;
        }

        // Extract definition based on the captured variables
        if let Some(def_info) = extract_definition_info(match_item, kotlin_node_fqn_map) {
            definitions.push(def_info);
        }
    }

    definitions
}

static DEFINITIONS_EXTRACTORS: Lazy<Vec<DefinitionExtractor>> = Lazy::new(|| {
    use meta_vars::*;

    vec![
        DefinitionExtractor {
            definition_type: KotlinDefinitionType::Class,
            extractor: |env| env.get(CLASS_DEF_NAME),
            extract_name: |node| node.text.clone(),
        },
        DefinitionExtractor {
            definition_type: KotlinDefinitionType::Function,
            extractor: |env| env.get(FUNCTION_DEF_NAME),
            extract_name: |node| node.text.clone(),
        },
        DefinitionExtractor {
            definition_type: KotlinDefinitionType::Property,
            extractor: |env| env.get(PROPERTY_DEF_NAME),
            extract_name: |node| node.text.clone(),
        },
        DefinitionExtractor {
            definition_type: KotlinDefinitionType::CompanionObject,
            extractor: |env| env.get(COMPANION_OBJECT_DEF),
            extract_name: |_| "Companion".to_string(),
        },
        DefinitionExtractor {
            definition_type: KotlinDefinitionType::Constructor,
            extractor: |env| env.get(CONSTRUCTOR_DEF),
            extract_name: |_| "<init>".to_string(),
        },
        DefinitionExtractor {
            definition_type: KotlinDefinitionType::Object,
            extractor: |env| env.get(OBJECT_DEF_NAME),
            extract_name: |node| node.text.clone(),
        },
        DefinitionExtractor {
            definition_type: KotlinDefinitionType::EnumEntry,
            extractor: |env| env.get(ENUM_ENTRY_DEF_NAME),
            extract_name: |node| node.text.clone(),
        },
        DefinitionExtractor {
            definition_type: KotlinDefinitionType::Interface,
            extractor: |env| env.get(INTERFACE_DEF_NAME),
            extract_name: |node| node.text.clone(),
        },
        DefinitionExtractor {
            definition_type: KotlinDefinitionType::Enum,
            extractor: |env| env.get(ENUM_DEF_NAME),
            extract_name: |node| node.text.clone(),
        },
        DefinitionExtractor {
            definition_type: KotlinDefinitionType::AnnotationClass,
            extractor: |env| env.get(ANNOTATION_CLASS_DEF_NAME),
            extract_name: |node| node.text.clone(),
        },
        DefinitionExtractor {
            definition_type: KotlinDefinitionType::Lambda,
            extractor: |env| env.get(LAMBDA_DEF_NAME),
            extract_name: |node| node.text.clone(),
        },
    ]
});

/// Extract definition information that combines DefinitionExtractor with FQN computation
/// and captures all variables. This function handles the core logic of extracting
/// definition information from AST matches.
fn extract_definition_info(
    match_item: &MatchWithNodes,
    kotlin_node_fqn_map: &KotlinNodeFqnMap,
) -> Option<KotlinDefinitionInfo> {
    let env = &match_item.match_info.meta_var_map;

    // Try each extractor until we find one that matches
    for extractor in &*DEFINITIONS_EXTRACTORS {
        if let Some(node) = (extractor.extractor)(env) {
            // Try to find FQN for this definition
            let fqn = find_fqn_for_definition(match_item, kotlin_node_fqn_map);

            return Some(KotlinDefinitionInfo::new(
                extractor.definition_type,
                (extractor.extract_name)(node),
                fqn.cloned(),
                match_item.match_info.clone(),
            ));
        }
    }

    None
}

/// Find FQN for a definition by looking up its name node in the Java FQN map
/// Returns the Java FQN with metadata directly
fn find_fqn_for_definition<'a>(
    match_item: &MatchWithNodes,
    node_fqn_map: &'a KotlinNodeFqnMap,
) -> Option<&'a KotlinFqn> {
    let range = match_item.match_info.range;

    node_fqn_map.get(&range).map(|(_, fqn)| fqn)
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::kotlin::fqn::{build_fqn_and_node_indices, kotlin_fqn_to_string};
    use crate::parser::{GenericParser, LanguageParser, SupportedLanguage};
    use crate::rules::{RuleManager, run_rules};

    /// Helper function to test a definition type with a code snippet
    fn test_definition_extraction(
        code: &str,
        expected_definitions: Vec<(&str, KotlinDefinitionType, &str)>, // (name, type, expected_fqn)
        description: &str,
    ) {
        println!("\n=== Testing: {description} ===");
        println!("Code snippet:\n{code}");

        let parser = GenericParser::default_for_language(SupportedLanguage::Kotlin);
        let parse_result = parser.parse(code, Some("test.kt")).unwrap();
        let rule_manager = RuleManager::new(SupportedLanguage::Kotlin);
        let matches = run_rules(&parse_result.ast, Some("test.kt"), &rule_manager);

        let kotlin_node_fqn_map = build_fqn_and_node_indices(&parse_result.ast);

        // print keys
        for key in kotlin_node_fqn_map.keys() {
            println!("Kotlin node FQN map key: {key:?}");
        }

        let definitions = find_definitions(&matches, &kotlin_node_fqn_map);

        // print definitions
        for def in &definitions {
            println!("Definition: {:?}", def.match_info.matched);
        }

        println!("Found {} definitions:", definitions.len());
        for def in &definitions {
            let fqn_str = def
                .fqn
                .as_ref()
                .map(kotlin_fqn_to_string)
                .unwrap_or_else(|| "None".to_string());
            println!("  {:?}: {} -> {}", def.definition_type, def.name, fqn_str);
        }

        assert_eq!(
            definitions.len(),
            expected_definitions.len(),
            "Expected {} definitions, found {}",
            expected_definitions.len(),
            definitions.len()
        );

        for (expected_name, expected_type, expected_fqn) in expected_definitions {
            let matching_defs: Vec<_> = definitions
                .iter()
                .filter(|d| d.name == expected_name && d.definition_type == expected_type)
                .collect();

            assert!(
                !matching_defs.is_empty(),
                "Could not find definition: {expected_name} of type {expected_type:?}"
            );

            matching_defs
                .iter()
                .find(|d| {
                    let actual_fqn = d.fqn.as_ref().unwrap_or_else(|| {
                        panic!("Expected FQN for {expected_name}, but found None")
                    });
                    let actual_fqn_str = kotlin_fqn_to_string(actual_fqn);
                    actual_fqn_str == expected_fqn
                })
                .unwrap_or_else(|| {
                    panic!(
                        "Found definition {expected_name} of type {expected_type:?} but with different FQN than expected '{expected_fqn}'"
                    )
                });
        }
    }

    #[test]
    fn test_kotlin_code_with_package_definitions() {
        test_definition_extraction(
            r#"
            package com.example.test

            const val MY_CONSTANT = 1

            class MyClass {
                val myProperty = 1
            }
            "#,
            vec![
                (
                    "MyClass",
                    KotlinDefinitionType::Class,
                    "com.example.test.MyClass",
                ),
                (
                    "MY_CONSTANT",
                    KotlinDefinitionType::Property,
                    "com.example.test.MY_CONSTANT",
                ),
                (
                    "myProperty",
                    KotlinDefinitionType::Property,
                    "com.example.test.MyClass.myProperty",
                ),
            ],
            "Package with class and property",
        );
    }

    #[test]
    fn test_kotlin_code_with_class_definitions() {
        test_definition_extraction(
            r#"
            class MyClass(
                val myConstructorParameter: Int
            ) {
                val myProperty = 1

                fun myMethod() {
                    println("Hello, World!")
                }
            }
            "#,
            vec![
                ("MyClass", KotlinDefinitionType::Class, "MyClass"),
                (
                    "<init>",
                    KotlinDefinitionType::Constructor,
                    "MyClass.<init>",
                ),
                (
                    "myConstructorParameter",
                    KotlinDefinitionType::Property,
                    "MyClass.myConstructorParameter",
                ),
                (
                    "myProperty",
                    KotlinDefinitionType::Property,
                    "MyClass.myProperty",
                ),
                (
                    "myMethod",
                    KotlinDefinitionType::Function,
                    "MyClass.myMethod",
                ),
            ],
            "Basic class with property and method",
        );
    }

    #[test]
    fn test_kotlin_code_with_companion_object() {
        test_definition_extraction(
            r#"
            class MyClass {
                companion object {
                    val myProperty = 1

                    fun myMethod() {
                        println("Hello, World!")
                    }
                }
            }
            "#,
            vec![
                ("MyClass", KotlinDefinitionType::Class, "MyClass"),
                (
                    "Companion",
                    KotlinDefinitionType::CompanionObject,
                    "MyClass.Companion",
                ),
                (
                    "myProperty",
                    KotlinDefinitionType::Property,
                    "MyClass.Companion.myProperty",
                ),
                (
                    "myMethod",
                    KotlinDefinitionType::Function,
                    "MyClass.Companion.myMethod",
                ),
            ],
            "Class with companion object",
        );
    }

    #[test]
    fn test_kotlin_code_with_constructor() {
        test_definition_extraction(
            r#"
            open class MyClass {
                constructor(a: Int) {
                    println("Constructor with Int")
                }

                constructor(a: String) {
                    println("Constructor with String")
                }
            }
            "#,
            vec![
                ("MyClass", KotlinDefinitionType::Class, "MyClass"),
                (
                    "<init>",
                    KotlinDefinitionType::Constructor,
                    "MyClass.<init>",
                ),
                (
                    "<init>",
                    KotlinDefinitionType::Constructor,
                    "MyClass.<init>",
                ),
            ],
            "Class with multiple constructors",
        );
    }

    #[test]
    fn test_kotlin_code_with_nested_classes() {
        test_definition_extraction(
            r#"
            class OuterClass {
                data class InnerClass {
                    val myProperty = 1

                    fun innerMethod() {
                        println("Inner method")
                    }
                }
            }
            "#,
            vec![
                ("OuterClass", KotlinDefinitionType::Class, "OuterClass"),
                (
                    "InnerClass",
                    KotlinDefinitionType::Class,
                    "OuterClass.InnerClass",
                ),
                (
                    "myProperty",
                    KotlinDefinitionType::Property,
                    "OuterClass.InnerClass.myProperty",
                ),
                (
                    "innerMethod",
                    KotlinDefinitionType::Function,
                    "OuterClass.InnerClass.innerMethod",
                ),
            ],
            "Nested classes",
        );
    }

    #[test]
    fn test_kotlin_code_with_extension_functions() {
        test_definition_extraction(
            r#"
            fun String.display(): String {
                return "[$this]"
            }
            "#,
            vec![("display", KotlinDefinitionType::Function, "display")],
            "Extension functions",
        );
    }

    #[test]
    fn test_kotlin_code_with_interface_definitions() {
        test_definition_extraction(
            r#"
            interface Repository<T> {
                fun findById(id: String): T
                fun save(entity: T): T
            }

            class UserRepository : Repository<User> {
                override fun findById(id: String): User {
                    return User(id)
                }

                override fun save(entity: User): User {
                    return entity
                }
            }
            "#,
            vec![
                ("Repository", KotlinDefinitionType::Interface, "Repository"),
                (
                    "findById",
                    KotlinDefinitionType::Function,
                    "Repository.findById",
                ),
                ("save", KotlinDefinitionType::Function, "Repository.save"),
                (
                    "UserRepository",
                    KotlinDefinitionType::Class,
                    "UserRepository",
                ),
                (
                    "findById",
                    KotlinDefinitionType::Function,
                    "UserRepository.findById",
                ),
                (
                    "save",
                    KotlinDefinitionType::Function,
                    "UserRepository.save",
                ),
            ],
            "Interface with implementation",
        );
    }

    #[test]
    fn test_kotlin_code_with_object_declarations() {
        test_definition_extraction(
            r#"
            object MyObject {
                fun myMethod() {
                    println("Hello, World!")
                }
            }
            "#,
            vec![
                ("MyObject", KotlinDefinitionType::Object, "MyObject"),
                (
                    "myMethod",
                    KotlinDefinitionType::Function,
                    "MyObject.myMethod",
                ),
            ],
            "Object declaration",
        );
    }

    #[test]
    fn test_kotlin_code_with_annotation_classes() {
        test_definition_extraction(
            r#"
            annotation class MyAnnotation(
                val myProperty: Int
            )

            @MyAnnotation(1)
            class MyClass {
                fun myMethod() {
                    println("Hello, World!")
                }
            }
            "#,
            vec![
                (
                    "MyAnnotation",
                    KotlinDefinitionType::AnnotationClass,
                    "MyAnnotation",
                ),
                (
                    "<init>",
                    KotlinDefinitionType::Constructor,
                    "MyAnnotation.<init>",
                ),
                (
                    "myProperty",
                    KotlinDefinitionType::Property,
                    "MyAnnotation.myProperty",
                ),
                ("MyClass", KotlinDefinitionType::Class, "MyClass"),
                (
                    "myMethod",
                    KotlinDefinitionType::Function,
                    "MyClass.myMethod",
                ),
            ],
            "Annotation class",
        );
    }

    #[test]
    fn test_kotlin_code_with_enum_entries() {
        test_definition_extraction(
            r#"
            enum class MyEnum(val myProperty: Int) {
                ENTRY1(1),
                ENTRY2(2)
            }
            "#,
            vec![
                ("MyEnum", KotlinDefinitionType::Enum, "MyEnum"),
                ("<init>", KotlinDefinitionType::Constructor, "MyEnum.<init>"),
                (
                    "myProperty",
                    KotlinDefinitionType::Property,
                    "MyEnum.myProperty",
                ),
                ("ENTRY1", KotlinDefinitionType::EnumEntry, "MyEnum.ENTRY1"),
                ("ENTRY2", KotlinDefinitionType::EnumEntry, "MyEnum.ENTRY2"),
            ],
            "Enum class with entries",
        );
    }

    #[test]
    fn test_kotlin_code_with_extension_property() {
        test_definition_extraction(
            r#"
            val String.count: Int
                get() = length
            "#,
            vec![("count", KotlinDefinitionType::Property, "count")],
            "Extension property extraction",
        );
    }

    #[test]
    fn test_kotlin_code_with_nested_functions() {
        test_definition_extraction(
            r#"
            fun main() {
                val myProperty = 1

                fun myMethod() {
                    val myMethodProperty = 1
                }

                class MyClass {
                    val myClassProperty = 1
                }

                println(myProperty)
            }
            "#,
            vec![
                ("main", KotlinDefinitionType::Function, "main"),
                (
                    "myProperty",
                    KotlinDefinitionType::Property,
                    "main.myProperty",
                ),
                ("myMethod", KotlinDefinitionType::Function, "main.myMethod"),
                (
                    "myMethodProperty",
                    KotlinDefinitionType::Property,
                    "main.myMethod.myMethodProperty",
                ),
                ("MyClass", KotlinDefinitionType::Class, "main.MyClass"),
                (
                    "myClassProperty",
                    KotlinDefinitionType::Property,
                    "main.MyClass.myClassProperty",
                ),
            ],
            "Nested functions",
        );
    }

    #[test]
    fn test_kotlin_code_with_destructuring_declarations() {
        test_definition_extraction(
            r#"
            class MyClass {
                val (a, b) = Pair(1, 2)
            }
            "#,
            vec![
                ("MyClass", KotlinDefinitionType::Class, "MyClass"),
                ("a", KotlinDefinitionType::Property, "MyClass.a"),
                ("b", KotlinDefinitionType::Property, "MyClass.b"),
            ],
            "Destructuring declarations",
        );
    }

    #[test]
    fn test_kotlin_code_with_lambda_declarations() {
        test_definition_extraction(
            r#"
             val declaredLambda = { a, b -> 
                val result = a + b
                println(result)
            }
            val referencedLambda = ::println
            val anonymousFunction = fun (a: Int) { println(a) }

            data class LambdaAsClassParameter(
                val anonymousClassFunction: Int = {}
            )
            "#,
            vec![
                (
                    "declaredLambda",
                    KotlinDefinitionType::Lambda,
                    "declaredLambda",
                ),
                (
                    "result",
                    KotlinDefinitionType::Property,
                    "declaredLambda.result",
                ),
                (
                    "referencedLambda",
                    KotlinDefinitionType::Lambda,
                    "referencedLambda",
                ),
                (
                    "anonymousFunction",
                    KotlinDefinitionType::Lambda,
                    "anonymousFunction",
                ),
                (
                    "LambdaAsClassParameter",
                    KotlinDefinitionType::Class,
                    "LambdaAsClassParameter",
                ),
                (
                    "<init>",
                    KotlinDefinitionType::Constructor,
                    "LambdaAsClassParameter.<init>",
                ),
                (
                    "anonymousClassFunction",
                    KotlinDefinitionType::Lambda,
                    "LambdaAsClassParameter.anonymousClassFunction",
                ),
            ],
            "Lambda declarations",
        );
    }
}
