id: kotlin-definitions
language: Kotlin
rule:
  any:
    - all:
      - kind: class_declaration
        regex: ((internal|public|private) )?enum class .*
        has:
          kind: type_identifier
          pattern: $ENUM_DEF_NAME
     
    - all:
      - kind: class_declaration
        regex: ((internal|public|private) )?((sealed|open) )?interface .*
        has:
          kind: type_identifier
          pattern: $INTERFACE_DEF_NAME

    - all:
      - kind: class_declaration
        all:
          - has:
              kind: type_identifier
              pattern: $ANNOTATION_CLASS_DEF_NAME
          - has:
              kind: modifiers
              has:
                kind: class_modifier
                regex: annotation

    - all: 
      - kind: class_declaration
        has:
          kind: type_identifier
          pattern: $CLASS_DEF_NAME  
      
    - all:
        - kind: object_declaration
          has:
            kind: type_identifier
            pattern: $OBJECT_DEF_NAME
    
    - all:
        - kind: function_declaration
          has:
            kind: simple_identifier
            pattern: $FUNCTION_DEF_NAME

    - all:
        - kind: property_declaration
          all:
            - has:
                kind: variable_declaration
                has:
                  kind: simple_identifier
                  pattern: $LAMBDA_DEF_NAME
            - any: 
                - has: 
                    kind: anonymous_function
                - has: 
                    kind: callable_reference  
                - has:
                    kind: lambda_literal

    - all:
        - kind: property_declaration
          has:
            kind: variable_declaration
            has: 
              kind: simple_identifier
              pattern: $PROPERTY_DEF_NAME
    - all:
        - kind: variable_declaration
          has:
            kind: simple_identifier
            pattern: $PROPERTY_DEF_NAME
          inside: 
            kind: multi_variable_declaration

    - all:
        - kind: companion_object
          pattern: $COMPANION_OBJECT_DEF

    - all:
        - kind: class_parameter
          all:
            - has:
                kind: simple_identifier
                pattern: $LAMBDA_DEF_NAME
            - any: 
                - has: 
                    kind: anonymous_function
                - has: 
                    kind: callable_reference  
                - has:
                    kind: lambda_literal

    - all:
        - kind: class_parameter
          has:
            kind: simple_identifier
            pattern: $PROPERTY_DEF_NAME

    - all:
        - kind: secondary_constructor
          pattern: $CONSTRUCTOR_DEF

    - all:
        - kind: primary_constructor
          pattern: $CONSTRUCTOR_DEF
    
    - all:
        - kind: enum_entry
          has:
            kind: simple_identifier
            pattern: $ENUM_ENTRY_DEF_NAME
