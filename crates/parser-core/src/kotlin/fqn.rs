use std::sync::Arc;

use ast_grep_core::{AstGrep, Node, tree_sitter::StrDoc};
use ast_grep_language::SupportLang;
use rustc_hash::FxHashMap;
use smallvec::{SmallVec, smallvec};

use crate::kotlin::types::{
    KotlinFqn, KotlinFqnMetadata, KotlinFqnPart, KotlinFqnPartType, KotlinNodeFqnMap,
};
use crate::utils::{Range, node_to_range};

mod node_types {
    pub const CLASS: &str = "class_declaration";
    pub const OBJECT: &str = "object_declaration";
    pub const FUNCTION: &str = "function_declaration";
    pub const PROPERTY: &str = "property_declaration";
    pub const MULTI_VARIABLE_DECLARATION: &str = "multi_variable_declaration";
    pub const COMPANION_OBJECT: &str = "companion_object";
    pub const PACKAGE: &str = "package_header";
    pub const ENUM_ENTRY: &str = "enum_entry";
    pub const CLASS_PARAMETER: &str = "class_parameter";
    pub const PRIMARY_CONSTRUCTOR: &str = "primary_constructor";
    pub const SECONDARY_CONSTRUCTOR: &str = "secondary_constructor";
    pub const VARIABLE_DECLARATION: &str = "variable_declaration";
    pub const SIMPLE_IDENTIFIER: &str = "simple_identifier";
    pub const TYPE_IDENTIFIER: &str = "type_identifier";
    pub const IDENTIFIER: &str = "identifier";
    pub const USER_TYPE: &str = "user_type";
    pub const LAMBDA_LITERAL: &str = "lambda_literal";
    pub const CALLABLE_REFERENCE: &str = "callable_reference";
    pub const ANONYMOUS_FUNCTION: &str = "anonymous_function";
}

const LAMBDA_TYPES: &[&str] = &[
    node_types::LAMBDA_LITERAL,
    node_types::ANONYMOUS_FUNCTION,
    node_types::CALLABLE_REFERENCE,
];

pub mod node_names {
    pub const COMPANION_OBJECT: &str = "Companion";
    pub const CONSTRUCTOR: &str = "<init>";
}

/// Use SmallVec for scope stack since Kotlin nesting is typically shallow
type ScopeStack = SmallVec<[KotlinFqnPart; 8]>;

/// Returns the metadata for a node based on its type and children
fn get_node_metadata<'a>(
    node: Node<'a, StrDoc<SupportLang>>,
    node_type: KotlinFqnPartType,
) -> KotlinFqnMetadata {
    if node_type != KotlinFqnPartType::Function && node_type != KotlinFqnPartType::Property {
        return KotlinFqnMetadata::default();
    }

    if let Some(extension_receiver_type) = get_child_by_kind(node, node_types::USER_TYPE) {
        return KotlinFqnMetadata::with_extension_receiver_type(
            extension_receiver_type.text().to_string(),
        );
    }

    KotlinFqnMetadata::default()
}

fn get_node_name<'a>(
    node: Node<'a, StrDoc<SupportLang>>,
    node_type: &str,
    path_to_name_node: &str,
) -> String {
    match node_type {
        node_types::COMPANION_OBJECT => node_names::COMPANION_OBJECT.to_string(),
        node_types::PRIMARY_CONSTRUCTOR | node_types::SECONDARY_CONSTRUCTOR => {
            node_names::CONSTRUCTOR.to_string()
        }
        _ => get_child_by_kind(node, path_to_name_node)
            .map(|n| n.text().to_string())
            .unwrap_or_default(),
    }
}

fn process_simple_node<'a>(
    node: Node<'a, StrDoc<SupportLang>>,
    path_to_name_node: &str,
    node_type: KotlinFqnPartType,
    current_scope: &mut ScopeStack,
    node_fqn_map: &mut FxHashMap<Range, (Node<'a, StrDoc<SupportLang>>, Arc<ScopeStack>)>,
) -> Option<KotlinFqnPart> {
    let metadata = get_node_metadata(node.clone(), node_type.clone());
    let identifier_node = get_child_by_kind(node.clone(), path_to_name_node).unwrap();
    let range = node_to_range(&node);

    let mut fqn_parts = current_scope.clone();
    let fqn_part = KotlinFqnPart::with_metadata(
        node_type,
        identifier_node.text().to_string(),
        range,
        metadata,
    );

    fqn_parts.push(fqn_part.clone());
    node_fqn_map.insert(range, (node, Arc::new(fqn_parts)));

    Some(fqn_part)
}

fn process_multi_variable_declaration_node<'a>(
    property_node: Node<'a, StrDoc<SupportLang>>,
    multi_declaration_node: Node<'a, StrDoc<SupportLang>>,
    current_scope: &mut ScopeStack,
    node_fqn_map: &mut FxHashMap<Range, (Node<'a, StrDoc<SupportLang>>, Arc<ScopeStack>)>,
) {
    let children = get_children_by_kind(multi_declaration_node, node_types::VARIABLE_DECLARATION);

    for child in children {
        let metadata = get_node_metadata(property_node.clone(), KotlinFqnPartType::Property);
        let node_name = get_node_name(
            child.clone(),
            node_types::PROPERTY,
            node_types::SIMPLE_IDENTIFIER,
        );

        let fqn_part = KotlinFqnPart::with_metadata(
            KotlinFqnPartType::Property,
            node_name,
            node_to_range(&property_node),
            metadata,
        );

        let mut fqn_parts = current_scope.clone();
        fqn_parts.push(fqn_part);
        node_fqn_map.insert(node_to_range(&child), (child, Arc::new(fqn_parts.clone())));
    }
}

fn process_property_declaration_node<'a>(
    node: Node<'a, StrDoc<SupportLang>>,
    current_scope: &mut ScopeStack,
    node_fqn_map: &mut FxHashMap<Range, (Node<'a, StrDoc<SupportLang>>, Arc<ScopeStack>)>,
) -> Option<KotlinFqnPart> {
    if let Some(multi_variable_declaration_node) =
        get_child_by_kind(node.clone(), node_types::MULTI_VARIABLE_DECLARATION)
    {
        process_multi_variable_declaration_node(
            node,
            multi_variable_declaration_node,
            current_scope,
            node_fqn_map,
        );

        return None;
    }

    if get_child_by_any_kind(node.clone(), LAMBDA_TYPES).is_some() {
        return process_simple_node(
            node,
            &format!(
                "{}, {}",
                node_types::VARIABLE_DECLARATION,
                node_types::SIMPLE_IDENTIFIER
            ),
            KotlinFqnPartType::Lambda,
            current_scope,
            node_fqn_map,
        );
    }

    process_simple_node(
        node,
        &format!(
            "{}, {}",
            node_types::VARIABLE_DECLARATION,
            node_types::SIMPLE_IDENTIFIER
        ),
        KotlinFqnPartType::Property,
        current_scope,
        node_fqn_map,
    );

    None
}

fn process_class_parameter_node<'a>(
    node: Node<'a, StrDoc<SupportLang>>,
    current_scope: &mut ScopeStack,
    node_fqn_map: &mut FxHashMap<Range, (Node<'a, StrDoc<SupportLang>>, Arc<ScopeStack>)>,
) -> Option<KotlinFqnPart> {
    if get_child_by_any_kind(node.clone(), LAMBDA_TYPES).is_some() {
        return process_simple_node(
            node,
            node_types::SIMPLE_IDENTIFIER,
            KotlinFqnPartType::Lambda,
            current_scope,
            node_fqn_map,
        );
    }

    process_simple_node(
        node,
        node_types::SIMPLE_IDENTIFIER,
        KotlinFqnPartType::Property,
        current_scope,
        node_fqn_map,
    );

    None
}

fn process_named_node<'a>(
    node: Node<'a, StrDoc<SupportLang>>,
    node_name: &str,
    node_type: KotlinFqnPartType,
    current_scope: &mut ScopeStack,
    node_fqn_map: &mut FxHashMap<Range, (Node<'a, StrDoc<SupportLang>>, Arc<ScopeStack>)>,
) -> Option<KotlinFqnPart> {
    let mut fqn_parts = current_scope.clone();
    let fqn_part = KotlinFqnPart::with_metadata(
        node_type,
        node_name.to_string(),
        node_to_range(&node),
        KotlinFqnMetadata::default(),
    );

    fqn_parts.push(fqn_part.clone());
    node_fqn_map.insert(node_to_range(&node), (node, Arc::new(fqn_parts)));

    Some(fqn_part)
}

// Process a node and returns the FQN part if the node creates a scope
// Passes the current scope and the node_fqn_map because some node may create multiple fqn parts (ex: destructuring declarations)
fn process_node<'a>(
    node: Node<'a, StrDoc<SupportLang>>,
    current_scope: &mut ScopeStack,
    node_fqn_map: &mut FxHashMap<Range, (Node<'a, StrDoc<SupportLang>>, Arc<ScopeStack>)>,
) -> Option<KotlinFqnPart> {
    match node.kind().as_ref() {
        node_types::CLASS => process_simple_node(
            node,
            node_types::TYPE_IDENTIFIER,
            KotlinFqnPartType::Class,
            current_scope,
            node_fqn_map,
        ),
        node_types::OBJECT => process_simple_node(
            node,
            node_types::TYPE_IDENTIFIER,
            KotlinFqnPartType::Object,
            current_scope,
            node_fqn_map,
        ),
        node_types::FUNCTION => process_simple_node(
            node,
            node_types::SIMPLE_IDENTIFIER,
            KotlinFqnPartType::Function,
            current_scope,
            node_fqn_map,
        ),
        node_types::CLASS_PARAMETER => {
            process_class_parameter_node(node, current_scope, node_fqn_map)
        }
        node_types::ENUM_ENTRY => {
            process_simple_node(
                node,
                node_types::SIMPLE_IDENTIFIER,
                KotlinFqnPartType::EnumEntry,
                current_scope,
                node_fqn_map,
            );
            None
        }
        node_types::PROPERTY => {
            process_property_declaration_node(node, current_scope, node_fqn_map)
        }
        node_types::COMPANION_OBJECT => process_named_node(
            node,
            node_names::COMPANION_OBJECT,
            KotlinFqnPartType::CompanionObject,
            current_scope,
            node_fqn_map,
        ),
        node_types::PRIMARY_CONSTRUCTOR => {
            process_named_node(
                node,
                node_names::CONSTRUCTOR,
                KotlinFqnPartType::Constructor,
                current_scope,
                node_fqn_map,
            );
            None
        }
        node_types::SECONDARY_CONSTRUCTOR => process_named_node(
            node,
            node_names::CONSTRUCTOR,
            KotlinFqnPartType::Constructor,
            current_scope,
            node_fqn_map,
        ),
        _ => None,
    }
}

/// Get a child node by following a path of node kinds
/// The path is specified as a comma-separated string of node kinds
fn get_child_by_kind<'a>(
    node: Node<'a, StrDoc<SupportLang>>,
    path_to_name_node: &str,
) -> Option<Node<'a, StrDoc<SupportLang>>> {
    let path_parts: Vec<&str> = path_to_name_node
        .split(',')
        .map(|part| part.trim())
        .collect();

    let mut current_node = Some(node);

    for part in path_parts {
        current_node = current_node?
            .children()
            .find(|child| child.kind().as_ref() == part);
    }

    current_node
}

fn get_child_by_any_kind<'a>(
    node: Node<'a, StrDoc<SupportLang>>,
    node_kinds: &[&str],
) -> Option<Node<'a, StrDoc<SupportLang>>> {
    node.children()
        .find(|child| node_kinds.contains(&child.kind().as_ref()))
}

fn get_children_by_kind<'a>(
    node: Node<'a, StrDoc<SupportLang>>,
    node_name: &str,
) -> Vec<Node<'a, StrDoc<SupportLang>>> {
    node.children()
        .filter(|child| child.kind().as_ref() == node_name)
        .collect()
}

/// Build FQN and node indices by traversing the AST
/// This function handles the core logic of building FQNs for all nodes in the AST
pub fn build_fqn_and_node_indices<'a>(
    ast: &'a AstGrep<StrDoc<SupportLang>>,
) -> KotlinNodeFqnMap<'a> {
    // a map of ranges to nodes with their fqns
    let mut node_fqn_map = FxHashMap::with_capacity_and_hasher(128, Default::default());

    // a stack of fqn parts for the current scope
    let mut current_scope: ScopeStack = smallvec![];

    if let Some(package_declaration) = get_child_by_kind(ast.root(), node_types::PACKAGE) {
        if let Some(package_name) = get_child_by_kind(package_declaration, node_types::IDENTIFIER) {
            current_scope.push(KotlinFqnPart::new(
                KotlinFqnPartType::Package,
                package_name.text().to_string(),
                node_to_range(&package_name),
            ));
        }
    }

    // a stack of nodes to process and their known fqn parts
    let mut stack: Vec<Option<Node<StrDoc<SupportLang>>>> = Vec::with_capacity(128);

    stack.push(Some(ast.root()));

    while let Some(node_option) = stack.pop() {
        if let Some(node) = node_option {
            let children: Vec<_> = node.children().collect();

            if let Some(new_scope) = process_node(node, &mut current_scope, &mut node_fqn_map) {
                current_scope.push(new_scope);
                stack.push(None);
            }

            push_children_reverse(children, &mut stack);
        } else {
            // None indicates the end of a scope
            current_scope.pop();
        }
    }

    node_fqn_map
}

/// Helper function to add children to stack in reverse order
fn push_children_reverse<'a>(
    children: Vec<Node<'a, StrDoc<SupportLang>>>,
    stack: &mut Vec<Option<Node<'a, StrDoc<SupportLang>>>>,
) {
    for child in children.into_iter().rev() {
        stack.push(Some(child));
    }
}

/// Find FQN for a node by looking up its range in the FQN map
pub fn find_kotlin_fqn_for_node<'a>(
    range: Range,
    node_fqn_map: &KotlinNodeFqnMap<'a>,
) -> Option<KotlinFqn> {
    node_fqn_map.get(&range).map(|(_, fqn)| fqn.clone())
}

/// Convert a Kotlin FQN to its string representation
/// The parts are joined by '.' to form the full FQN string
pub fn kotlin_fqn_to_string(fqn: &KotlinFqn) -> String {
    fqn.iter()
        .map(|part| part.node_name.clone())
        .collect::<Vec<_>>()
        .join(".")
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::parser::{GenericParser, LanguageParser, SupportedLanguage};

    #[test]
    fn test_kotlin_code_outside_a_package() {
        let kotlin_code = r#"
        class MyClass {
            val myProperty = 1;

            fun myMethod() {
                println("Hello, World!")
            }
        }

        fun main() {
            MyClass().myMethod()
        }
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Kotlin);
        let parse_result = parser.parse(kotlin_code, Some("test.kt")).unwrap();

        let kotlin_node_fqn_map = build_fqn_and_node_indices(&parse_result.ast);

        let found_fqns = kotlin_fqn_map_to_string(&kotlin_node_fqn_map);
        assert_eq!(found_fqns.len(), 4);

        assert!(found_fqns.contains(&"MyClass".to_string()));
        assert!(found_fqns.contains(&"MyClass.myProperty".to_string()));
        assert!(found_fqns.contains(&"MyClass.myMethod".to_string()));
        assert!(found_fqns.contains(&"main".to_string()));
    }

    #[test]
    fn test_kotlin_code_in_a_package() {
        let kotlin_code = r#"
        package com.example.test;

        const val MY_CONSTANT = 1;
        val myFileProperty = 2;

        class MyClass(
            val myConstructorProperty1: Int = 1,
            val myConstructorProperty2: Int
        ) {
            val myProperty = 1;

            fun myMethod() {
                println("Hello, World!")
            }
        }

        fun main() {
            MyClass().myMethod()
        }
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Kotlin);
        let parse_result = parser.parse(kotlin_code, Some("test.kt")).unwrap();

        let kotlin_node_fqn_map = build_fqn_and_node_indices(&parse_result.ast);

        let found_fqns = kotlin_fqn_map_to_string(&kotlin_node_fqn_map);
        assert_eq!(found_fqns.len(), 9);

        assert!(found_fqns.contains(&"com.example.test.MyClass".to_string()));
        assert!(found_fqns.contains(&"com.example.test.MyClass.<init>".to_string()));
        assert!(
            found_fqns.contains(&"com.example.test.MyClass.myConstructorProperty1".to_string())
        );
        assert!(
            found_fqns.contains(&"com.example.test.MyClass.myConstructorProperty2".to_string())
        );
        assert!(found_fqns.contains(&"com.example.test.MyClass.myProperty".to_string()));
        assert!(found_fqns.contains(&"com.example.test.MyClass.myMethod".to_string()));
        assert!(found_fqns.contains(&"com.example.test.main".to_string()));
        assert!(found_fqns.contains(&"com.example.test.MY_CONSTANT".to_string()));
        assert!(found_fqns.contains(&"com.example.test.myFileProperty".to_string()));
    }

    #[test]
    fn test_includes_declarations_inside_functions() {
        let kotlin_code = r#"
        fun main() {
            val myProperty = 1;

            fun myMethod() {
                val myMethodProperty = 1;
            }

            class MyClass {
                val myClassProperty = 1;
            }

            println(myProperty)
        }
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Kotlin);
        let parse_result = parser.parse(kotlin_code, Some("test.kt")).unwrap();

        let kotlin_node_fqn_map = build_fqn_and_node_indices(&parse_result.ast);

        let found_fqns = kotlin_fqn_map_to_string(&kotlin_node_fqn_map);
        assert_eq!(found_fqns.len(), 6);

        assert!(found_fqns.contains(&"main".to_string()));
        assert!(found_fqns.contains(&"main.myProperty".to_string()));
        assert!(found_fqns.contains(&"main.myMethod".to_string()));
        assert!(found_fqns.contains(&"main.myMethod.myMethodProperty".to_string()));
        assert!(found_fqns.contains(&"main.MyClass".to_string()));
        assert!(found_fqns.contains(&"main.MyClass.myClassProperty".to_string()));
    }

    #[test]
    fn test_extension_receiver_type_is_included_in_fqn() {
        let kotlin_code = r#"
        fun String.myExtensionMethod() {
            println("Hello, World!")
        }
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Kotlin);
        let parse_result = parser.parse(kotlin_code, Some("test.kt")).unwrap();

        let kotlin_node_fqn_map = build_fqn_and_node_indices(&parse_result.ast);

        let fqn_part = &kotlin_node_fqn_map
            .iter()
            .next()
            .unwrap()
            .1
            .1
            .first()
            .unwrap();

        assert_eq!(fqn_part.node_name, "myExtensionMethod");
        assert_eq!(
            fqn_part.metadata.as_ref().unwrap().receiver_type_reference,
            Some("String".to_string())
        );
    }

    #[test]
    fn test_nested_classes_are_included_in_fqn() {
        let kotlin_code = r#"
        class MyClass {
            class MyNestedClass {
                fun myMethod() {
                    println("Hello, World!")
                }
            }
        }
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Kotlin);
        let parse_result = parser.parse(kotlin_code, Some("test.kt")).unwrap();

        let kotlin_node_fqn_map = build_fqn_and_node_indices(&parse_result.ast);

        let found_fqns = kotlin_fqn_map_to_string(&kotlin_node_fqn_map);
        assert_eq!(found_fqns.len(), 3);

        assert!(found_fqns.contains(&"MyClass".to_string()));
        assert!(found_fqns.contains(&"MyClass.MyNestedClass".to_string()));
        assert!(found_fqns.contains(&"MyClass.MyNestedClass.myMethod".to_string()));
    }

    #[test]
    fn test_companion_objects_are_included_in_fqn() {
        let kotlin_code = r#"
        class MyClass {
            companion object {
                fun myMethod() {
                    println("Hello, World!")
                }
            }
        }
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Kotlin);
        let parse_result = parser.parse(kotlin_code, Some("test.kt")).unwrap();

        let kotlin_node_fqn_map = build_fqn_and_node_indices(&parse_result.ast);

        let found_fqns = kotlin_fqn_map_to_string(&kotlin_node_fqn_map);
        assert_eq!(found_fqns.len(), 3);

        assert!(found_fqns.contains(&"MyClass".to_string()));
        assert!(found_fqns.contains(&"MyClass.Companion".to_string()));
        assert!(found_fqns.contains(&"MyClass.Companion.myMethod".to_string()));
    }

    #[test]
    fn test_operator_functions_are_included_in_fqn() {
        let kotlin_code = r#"
        class MyClass {
            operator fun plus(other: MyClass) = MyClass()
        }
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Kotlin);
        let parse_result = parser.parse(kotlin_code, Some("test.kt")).unwrap();

        let kotlin_node_fqn_map = build_fqn_and_node_indices(&parse_result.ast);

        let found_fqns = kotlin_fqn_map_to_string(&kotlin_node_fqn_map);
        assert_eq!(found_fqns.len(), 2);

        assert!(found_fqns.contains(&"MyClass".to_string()));
        assert!(found_fqns.contains(&"MyClass.plus".to_string()));
    }

    #[test]
    fn test_function_overloading_have_the_same_fqn() {
        let kotlin_code = r#"
        class MyClass {
            fun myMethod(a: Int) = 1
            fun myMethod(a: String) = 2
        }
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Kotlin);
        let parse_result = parser.parse(kotlin_code, Some("test.kt")).unwrap();

        let kotlin_node_fqn_map = build_fqn_and_node_indices(&parse_result.ast);

        let found_fqns = kotlin_fqn_map_to_string(&kotlin_node_fqn_map);
        assert_eq!(found_fqns.len(), 3);

        assert!(found_fqns.contains(&"MyClass".to_string()));
        assert!(found_fqns.contains(&"MyClass.myMethod".to_string()));
    }

    #[test]
    fn test_secondary_constructors_are_included_in_fqn() {
        let kotlin_code = r#"
        class MyClass {
            constructor(a: Int) = This()
            constructor(a: String) = This()
        }
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Kotlin);
        let parse_result = parser.parse(kotlin_code, Some("test.kt")).unwrap();

        let kotlin_node_fqn_map = build_fqn_and_node_indices(&parse_result.ast);

        let found_fqns = kotlin_fqn_map_to_string(&kotlin_node_fqn_map);
        assert_eq!(found_fqns.len(), 3);

        assert!(found_fqns.contains(&"MyClass".to_string()));
        assert!(found_fqns.contains(&"MyClass.<init>".to_string()));
    }

    #[test]
    fn test_handles_class_with_modifiers() {
        let kotlin_code = r#"
        data class MyClass(
            val myProperty: Int
        )
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Kotlin);
        let parse_result = parser.parse(kotlin_code, Some("test.kt")).unwrap();

        let kotlin_node_fqn_map = build_fqn_and_node_indices(&parse_result.ast);

        let found_fqns = kotlin_fqn_map_to_string(&kotlin_node_fqn_map);
        assert_eq!(found_fqns.len(), 3);

        assert!(found_fqns.contains(&"MyClass".to_string()));
        assert!(found_fqns.contains(&"MyClass.myProperty".to_string()));
        assert!(found_fqns.contains(&"MyClass.<init>".to_string()));
    }

    #[test]
    fn test_interface_definitions_are_included_in_fqn() {
        let kotlin_code = r#"
        interface Repository<T> {
            fun findById(id: String): T
            fun save(entity: T): T
        }

        class UserRepository : Repository<User> {
            override fun findById(id: String): User {
                return User(id)
            }

            override fun save(entity: User): User {
                return entity
            }
        }
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Kotlin);
        let parse_result = parser.parse(kotlin_code, Some("test.kt")).unwrap();

        let kotlin_node_fqn_map = build_fqn_and_node_indices(&parse_result.ast);

        let found_fqns = kotlin_fqn_map_to_string(&kotlin_node_fqn_map);
        assert_eq!(found_fqns.len(), 6);

        assert!(found_fqns.contains(&"Repository".to_string()));
        assert!(found_fqns.contains(&"Repository.findById".to_string()));
        assert!(found_fqns.contains(&"Repository.save".to_string()));
        assert!(found_fqns.contains(&"UserRepository".to_string()));
        assert!(found_fqns.contains(&"UserRepository.findById".to_string()));
        assert!(found_fqns.contains(&"UserRepository.save".to_string()));
    }

    #[test]
    fn object_declarations_are_included_in_fqn() {
        let kotlin_code = r#"
        object MyObject {
            fun myMethod() {
                println("Hello, World!")
            }
        }
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Kotlin);
        let parse_result = parser.parse(kotlin_code, Some("test.kt")).unwrap();

        let kotlin_node_fqn_map = build_fqn_and_node_indices(&parse_result.ast);

        let found_fqns = kotlin_fqn_map_to_string(&kotlin_node_fqn_map);
        assert_eq!(found_fqns.len(), 2);

        assert!(found_fqns.contains(&"MyObject".to_string()));
        assert!(found_fqns.contains(&"MyObject.myMethod".to_string()));
    }

    #[test]
    fn annotation_classes_are_included_in_fqn() {
        let kotlin_code = r#"
        @Target(AnnotationTarget.CLASS)
        annotation class MyAnnotation(
            val myProperty: Int
        )

        @MyAnnotation(1)
        class MyClass {
            fun myMethod() {
                println("Hello, World!")
            }
        }
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Kotlin);
        let parse_result = parser.parse(kotlin_code, Some("test.kt")).unwrap();

        let kotlin_node_fqn_map = build_fqn_and_node_indices(&parse_result.ast);

        let found_fqns = kotlin_fqn_map_to_string(&kotlin_node_fqn_map);
        assert_eq!(found_fqns.len(), 5);

        assert!(found_fqns.contains(&"MyAnnotation".to_string()));
        assert!(found_fqns.contains(&"MyAnnotation.myProperty".to_string()));
        assert!(found_fqns.contains(&"MyAnnotation.<init>".to_string()));
        assert!(found_fqns.contains(&"MyClass".to_string()));
        assert!(found_fqns.contains(&"MyClass.myMethod".to_string()));
    }

    #[test]
    fn enum_entries_are_included_in_fqn() {
        let kotlin_code = r#"
        enum class MyEnum(val myProperty: Int) {
            ENTRY1(1),
            ENTRY2(2)
        }
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Kotlin);
        let parse_result = parser.parse(kotlin_code, Some("test.kt")).unwrap();

        let kotlin_node_fqn_map = build_fqn_and_node_indices(&parse_result.ast);

        let found_fqns = kotlin_fqn_map_to_string(&kotlin_node_fqn_map);
        assert_eq!(found_fqns.len(), 5);

        assert!(found_fqns.contains(&"MyEnum".to_string()));
        assert!(found_fqns.contains(&"MyEnum.<init>".to_string()));
        assert!(found_fqns.contains(&"MyEnum.myProperty".to_string()));
        assert!(found_fqns.contains(&"MyEnum.ENTRY1".to_string()));
        assert!(found_fqns.contains(&"MyEnum.ENTRY2".to_string()));
    }

    #[test]
    fn extension_properties_are_included_in_fqn() {
        let kotlin_code = r#"
        val String.count: Int
            get() = length
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Kotlin);
        let parse_result = parser.parse(kotlin_code, Some("test.kt")).unwrap();

        let kotlin_node_fqn_map = build_fqn_and_node_indices(&parse_result.ast);

        let fqn_part = &kotlin_node_fqn_map
            .iter()
            .next()
            .unwrap()
            .1
            .1
            .first()
            .unwrap();

        assert_eq!(fqn_part.node_name, "count");
        assert_eq!(
            fqn_part.metadata.as_ref().unwrap().receiver_type_reference,
            Some("String".to_string())
        );
    }

    #[test]
    fn destructuring_declarations_are_included_in_fqn() {
        let kotlin_code = r#"
        class MyClass {
            val (a, b) = Pair(1, 2)
        }
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Kotlin);
        let parse_result = parser.parse(kotlin_code, Some("test.kt")).unwrap();

        let kotlin_node_fqn_map = build_fqn_and_node_indices(&parse_result.ast);

        let found_fqns = kotlin_fqn_map_to_string(&kotlin_node_fqn_map);
        assert_eq!(found_fqns.len(), 3);

        assert!(found_fqns.contains(&"MyClass".to_string()));
        assert!(found_fqns.contains(&"MyClass.a".to_string()));
        assert!(found_fqns.contains(&"MyClass.b".to_string()));
    }

    #[test]
    fn test_lambda_declarations_are_included_in_fqn() {
        let kotlin_code = r#"
        val declaredLambda = { a, b -> 
            val result = a + b
            println(result)
        }
        val referencedLambda = ::println
        val anonymousFunction = fun (a: Int) { println(a) }

        data class LambdaAsClassParameter(
            val anonymousClassFunction: Int = {}
        )
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Kotlin);
        let parse_result = parser.parse(kotlin_code, Some("test.kt")).unwrap();

        let kotlin_node_fqn_map = build_fqn_and_node_indices(&parse_result.ast);

        let found_fqns = kotlin_fqn_map_to_string(&kotlin_node_fqn_map);
        assert_eq!(found_fqns.len(), 7);

        assert!(found_fqns.contains(&"declaredLambda".to_string()));
        assert!(found_fqns.contains(&"declaredLambda.result".to_string()));
        assert!(found_fqns.contains(&"referencedLambda".to_string()));
        assert!(found_fqns.contains(&"anonymousFunction".to_string()));
        assert!(found_fqns.contains(&"LambdaAsClassParameter".to_string()));
        assert!(found_fqns.contains(&"LambdaAsClassParameter.<init>".to_string()));
        assert!(found_fqns.contains(&"LambdaAsClassParameter.anonymousClassFunction".to_string()));
    }

    fn kotlin_fqn_map_to_string(node_fqn_map: &KotlinNodeFqnMap) -> Vec<String> {
        node_fqn_map
            .iter()
            .map(|(_, (_, fqn_part))| {
                fqn_part
                    .iter()
                    .map(|part| part.node_name.clone())
                    .collect::<Vec<_>>()
                    .join(".")
            })
            .collect()
    }
}
