use crate::{
    <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Result,
    kotlin::{
        definitions::find_definitions,
        fqn::{build_fqn_and_node_indices, kotlin_fqn_to_string},
        types::{KotlinDefinitionType, KotlinFqn},
    },
    rules::MatchWithNodes,
};

pub type KotlinAnalyzer = Analyzer<KotlinFqn, KotlinDefinitionType>;
pub type KotlinAnalyzerResult = AnalysisResult<KotlinFqn, KotlinDefinitionType>;

impl KotlinAnalyzer {
    pub fn analyze(
        &self,
        matches: &[MatchWithNodes],
        parser_result: &ParseResult,
    ) -> Result<KotlinAnalyzerResult> {
        let node_fqn_map = build_fqn_and_node_indices(&parser_result.ast);
        let definitions = find_definitions(matches, &node_fqn_map);
        let imports = Vec::new();

        Ok(KotlinAnalyzerResult {
            definitions,
            imports,
        })
    }
}

impl KotlinAnalyzerResult {
    pub fn kotlin_definition_fqn_strings(&self) -> Vec<String> {
        self.definition_fqn_strings(kotlin_fqn_to_string)
    }
}

#[cfg(test)]
mod tests {
    use crate::{
        DefinitionLookup, LanguageParser, RuleManager, SupportedLanguage, parser::GenericParser,
        rules::run_rules,
    };

    use super::*;

    #[test]
    fn test_definition_grouping_and_filtering() {
        let analyzer = KotlinAnalyzer::new();
        let fixture_path = "src/kotlin/fixtures/ComprehensiveKotlinDefinitions.kt";
        let kotlin_code = std::fs::read_to_string(fixture_path)
            .expect("Should be able to read ComprehensiveKotlinDefinitions.kt fixture");

        let parser = GenericParser::default_for_language(SupportedLanguage::Kotlin);
        let parse_result = parser.parse(&kotlin_code, Some(fixture_path)).unwrap();

        let rule_manager = RuleManager::new(SupportedLanguage::Kotlin);
        let matches = run_rules(&parse_result.ast, Some(fixture_path), &rule_manager);

        let result = analyzer.analyze(&matches, &parse_result).unwrap();

        assert!(
            !result
                .definitions_of_type(&KotlinDefinitionType::Class)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&KotlinDefinitionType::Function)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&KotlinDefinitionType::Property)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&KotlinDefinitionType::Constructor)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&KotlinDefinitionType::CompanionObject)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&KotlinDefinitionType::Object)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&KotlinDefinitionType::EnumEntry)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&KotlinDefinitionType::CompanionObject)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&KotlinDefinitionType::Object)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&KotlinDefinitionType::EnumEntry)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&KotlinDefinitionType::Interface)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&KotlinDefinitionType::Enum)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&KotlinDefinitionType::AnnotationClass)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&KotlinDefinitionType::Lambda)
                .is_empty()
        );
    }

    #[test]
    fn test_comprehensive_definitions_fixture() {
        let analyzer = KotlinAnalyzer::new();
        let fixture_path = "src/kotlin/fixtures/ComprehensiveKotlinDefinitions.kt";
        let kotlin_code = std::fs::read_to_string(fixture_path)
            .expect("Should be able to read ComprehensiveKotlinDefinitions.kt fixture");

        let parser = GenericParser::default_for_language(SupportedLanguage::Kotlin);
        let parse_result = parser.parse(&kotlin_code, Some(fixture_path)).unwrap();
        let rule_manager = RuleManager::new(SupportedLanguage::Kotlin);
        let matches = run_rules(&parse_result.ast, Some(fixture_path), &rule_manager);

        let result = analyzer.analyze(&matches, &parse_result).unwrap();

        println!(
            "ComprehensiveKotlinDefinitions.kt analyzer counts: {:?}",
            result.count_definitions_by_type()
        );

        validate_definition_exists(&result, "Disposable", KotlinDefinitionType::AnnotationClass);

        validate_definition_exists(&result, "Time", KotlinDefinitionType::Object);
        validate_definition_exists(&result, "utcClock", KotlinDefinitionType::Property);

        validate_definition_exists(&result, "Project", KotlinDefinitionType::Class);
        validate_definition_exists(&result, "absolutePath", KotlinDefinitionType::Property);
        validate_definition_exists(&result, "name", KotlinDefinitionType::Property);
        validate_definition_exists(&result, "default", KotlinDefinitionType::Function);
        validate_definition_exists(&result, "display", KotlinDefinitionType::Function);

        validate_definition_exists(&result, "BASE_URL", KotlinDefinitionType::Property);
        validate_definition_exists(&result, "urlAndPort", KotlinDefinitionType::Property);
        validate_definition_exists(&result, "httpClient", KotlinDefinitionType::Property);

        validate_definition_exists(&result, "AccessResult", KotlinDefinitionType::Enum);
        validate_definition_exists(&result, "message", KotlinDefinitionType::Property);
        validate_definition_exists(&result, "UNKNOWN_PROJECT", KotlinDefinitionType::EnumEntry);
        validate_definition_exists(&result, "ACCESS_EXPIRED", KotlinDefinitionType::EnumEntry);
        validate_definition_exists(&result, "ACCESS_OK", KotlinDefinitionType::EnumEntry);

        validate_definition_exists(
            &result,
            "IProjectAccessService",
            KotlinDefinitionType::Interface,
        );
        validate_definition_exists(&result, "ProjectAccessService", KotlinDefinitionType::Class);
        validate_definition_exists(&result, "logger", KotlinDefinitionType::Property);
        validate_definition_exists(&result, "clock", KotlinDefinitionType::Property);
        validate_definition_exists(&result, "validateAccess", KotlinDefinitionType::Function);
        validate_definition_exists(&result, "body", KotlinDefinitionType::Property);
        validate_definition_exists(&result, "revokeAccess", KotlinDefinitionType::Function);

        validate_definition_exists_with_count(
            &result,
            "project",
            KotlinDefinitionType::Property,
            2,
        );

        validate_definition_exists_with_count(
            &result,
            "requestUrl",
            KotlinDefinitionType::Property,
            2,
        );

        validate_definition_exists_with_count(
            &result,
            "<init>",
            KotlinDefinitionType::Constructor,
            4,
        );
        validate_definition_exists_with_count(
            &result,
            "Companion",
            KotlinDefinitionType::CompanionObject,
            2,
        );

        validate_definition_exists(&result, "printUrlAndPort", KotlinDefinitionType::Lambda);
        validate_definition_exists(&result, "main", KotlinDefinitionType::Function);
        validate_definition_exists(&result, "url", KotlinDefinitionType::Property);
        validate_definition_exists(&result, "port", KotlinDefinitionType::Property);
    }

    fn validate_definition_exists(
        result: &KotlinAnalyzerResult,
        name: &str,
        expected_type: KotlinDefinitionType,
    ) {
        let defs = result.definitions_by_name(name);

        assert!(!defs.is_empty(), "Should find {name} definition");
        assert!(
            defs[0].definition_type == expected_type,
            "Definition type mismatch for {}, expected {:?}, got {:?}",
            name,
            expected_type,
            defs[0].definition_type
        );
        assert_eq!(defs.len(), 1, "Should find exactly 1 {name} definition");
    }

    fn validate_definition_exists_with_count(
        result: &KotlinAnalyzerResult,
        name: &str,
        expected_type: KotlinDefinitionType,
        expected_count: usize,
    ) {
        let defs = result.definitions_by_name(name);

        assert!(!defs.is_empty(), "Should find {name} definition");
        assert!(
            defs[0].definition_type == expected_type,
            "Definition type mismatch for {}, expected {:?}, got {:?}",
            name,
            expected_type,
            defs[0].definition_type
        );
        assert_eq!(
            defs.len(),
            expected_count,
            "Should find exactly {expected_count} {name} definitions"
        );
    }
}
