use crate::parser::{SupportedLanguage, load_rules_from_yaml};
use ast_grep_config::RuleConfig;
use ast_grep_language::SupportLang;
use rustc_hash::FxHashMap;
use std::sync::LazyLock;

pub const DEFINITIONS_YAML: &str = include_str!("./rules/definitions.yaml");

pub static KOTLIN_RULES: LazyLock<String> = LazyLock::new(|| DEFINITIONS_YAML.to_string());

pub static RULES_CONFIG: LazyLock<Vec<RuleConfig<SupportLang>>> =
    LazyLock::new(|| load_rules_from_yaml(&KOTLIN_RULES, SupportedLanguage::Kotlin));

#[derive(Debug, PartialEq, Eq, Clone)]
pub enum KotlinMatchKind {
    Definition,
    Other(String),
}

pub static RULE_ID_KIND_MAP: LazyLock<FxHashMap<&'static str, KotlinMatchKind>> =
    LazyLock::new(|| {
        let mut m = FxHashMap::default();
        m.insert("kotlin-definitions", KotlinMatchKind::Definition);
        m
    });

impl KotlinMatchKind {
    pub fn from_rule_id(rule_id: &str) -> Self {
        RULE_ID_KIND_MAP
            .get(rule_id)
            .cloned()
            .unwrap_or_else(|| KotlinMatchKind::Other(rule_id.to_string()))
    }
}
