use rustc_hash::FxHashMap;
use std::sync::Arc;

use ast_grep_core::{Node, tree_sitter::StrDoc};
use ast_grep_language::SupportLang;
use smallvec::SmallVec;

use crate::definitions::{DefinitionInfo, DefinitionTypeInfo};
use crate::fqn::FQNPart;
use crate::utils::Range;

// FQN types

#[derive(Debug, Clone, PartialEq, Eq, Hash, serde::Serialize, serde::Deserialize)]
pub enum KotlinFqnPartType {
    Package,
    Class,
    Interface,
    Enum,
    EnumEntry,
    AnnotationClass,
    Function,
    Property,
    CompanionObject,
    Constructor,
    Object,
    Lambda,
}

/// Kotlin-specific metadata for FQN parts
/// Contains information about the AST node that created this FQN part
#[derive(Clone, Default, Debug, PartialEq, Eq, Hash)]
pub struct KotlinFqnMetadata {
    /// Optional receiver type reference for extension functions
    pub receiver_type_reference: Option<String>,
}

impl KotlinFqnMetadata {
    pub fn with_extension_receiver_type(extension_receiver_type: String) -> Self {
        Self {
            receiver_type_reference: Some(extension_receiver_type),
        }
    }
}

/// Kotlin-specific FQN part with metadata
pub type KotlinFqnPart = FQNPart<KotlinFqnPartType, KotlinFqnMetadata>;

/// Kotlin-specific FQN with rich metadata
pub type KotlinFqn = Arc<SmallVec<[KotlinFqnPart; 8]>>;

/// Maps node ranges to their corresponding AST nodes and FQN parts
pub type KotlinNodeFqnMap<'a> = FxHashMap<Range, (Node<'a, StrDoc<SupportLang>>, KotlinFqn)>;

// Definition types

/// Represents a Kotlin definition found in the code
/// This is now a type alias using the generic DefinitionInfo with Kotlin-specific types
pub type KotlinDefinitionInfo = DefinitionInfo<KotlinDefinitionType, KotlinFqn>;

/// Types of definitions that can be found in Kotlin code
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, serde::Serialize, serde::Deserialize)]
pub enum KotlinDefinitionType {
    Class,
    Interface,
    Enum,
    EnumEntry,
    AnnotationClass,
    Function,
    Property,
    CompanionObject,
    Constructor,
    Object,
    Lambda,
}

impl DefinitionTypeInfo for KotlinDefinitionType {
    /// Convert KotlinDefinitionType to its string representation
    fn as_str(&self) -> &str {
        match self {
            KotlinDefinitionType::Class => "Class",
            KotlinDefinitionType::Interface => "Interface",
            KotlinDefinitionType::Enum => "Enum",
            KotlinDefinitionType::EnumEntry => "EnumEntry",
            KotlinDefinitionType::AnnotationClass => "AnnotationClass",
            KotlinDefinitionType::Function => "Function",
            KotlinDefinitionType::Property => "Property",
            KotlinDefinitionType::CompanionObject => "CompanionObject",
            KotlinDefinitionType::Constructor => "Constructor",
            KotlinDefinitionType::Object => "Object",
            KotlinDefinitionType::Lambda => "Lambda",
        }
    }
}
