use std::sync::Arc;

use crate::utils::Range;

/// Generic FQN Part that can be language-specific
/// This allows for future extensibility where different languages
/// might need different metadata for their FQN parts
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct FQNPart<T = String, M = ()> {
    /// The node type (e.g., "Module", "Class", "Function", "Method", "Variable")
    pub node_type: T,
    /// The simple name of the segment, e.g., "MyClass", "calculate_sum"
    pub node_name: String,
    /// The range of the node in the source code
    pub range: Range,
    /// Optional language-specific metadata
    pub metadata: Option<M>,
}

impl<T, M> FQNPart<T, M> {
    /// Create a new FQN part with just node type and name
    pub fn new(node_type: T, node_name: String, range: Range) -> Self {
        Self {
            node_type,
            node_name,
            range,
            metadata: None,
        }
    }

    /// Create a new FQN part with metadata
    pub fn with_metadata(node_type: T, node_name: String, range: Range, metadata: M) -> Self {
        Self {
            node_type,
            node_name,
            range,
            metadata: Some(metadata),
        }
    }
}

impl Fqn<FQNPart<String>> {
    pub fn to_string(&self, separator: &str) -> String {
        self.parts
            .iter()
            .map(|part| part.node_name.clone())
            .collect::<Vec<_>>()
            .join(separator)
    }
}

/// Generic FQN (Fully Qualified Name) data structure
/// Currently optimized for performance using Arc<Vec<String>> pattern from old MR
/// but designed to be extensible for future FQNPart<T> usage
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct Fqn<T = String> {
    pub parts: Arc<Vec<T>>,
}

impl<T> Fqn<T> {
    /// Creates a new FQN from a vector of parts
    pub fn new(parts: Vec<T>) -> Self {
        Self {
            parts: Arc::new(parts),
        }
    }

    /// Returns true if this is a top-level definition (no namespace)
    pub fn is_top_level(&self) -> bool {
        self.parts.len() == 1
    }

    /// Get the number of parts in this FQN
    pub fn len(&self) -> usize {
        self.parts.len()
    }

    /// Check if the FQN is empty
    pub fn is_empty(&self) -> bool {
        self.parts.is_empty()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::utils::{Position, Range};

    #[test]
    fn test_fqn_creation_and_basic_operations() {
        // Test basic FQN creation
        let fqn = Fqn::new(vec![
            "Module".to_string(),
            "Class".to_string(),
            "method".to_string(),
        ]);

        assert_eq!(fqn.len(), 3);
        assert!(!fqn.is_empty());
        assert!(!fqn.is_top_level());

        let top_level_fqn = Fqn::new(vec!["TopLevelMethod".to_string()]);
        assert!(top_level_fqn.is_top_level());
    }

    #[test]
    fn test_fqn_part_creation() {
        let part: FQNPart<String, ()> = FQNPart::new(
            "Class".to_string(),
            "MyClass".to_string(),
            Range::new(Position::new(10, 20), Position::new(10, 20), (10, 20)),
        );

        assert_eq!(part.node_type, "Class");
        assert_eq!(part.node_name, "MyClass");
        assert!(part.metadata.is_none());

        // Test with String metadata
        let part_with_metadata = FQNPart::with_metadata(
            "Method".to_string(),
            "my_method".to_string(),
            Range::new(Position::new(10, 20), Position::new(10, 20), (10, 20)),
            "public".to_string(),
        );

        assert_eq!(part_with_metadata.node_type, "Method");
        assert_eq!(part_with_metadata.node_name, "my_method");
        assert_eq!(part_with_metadata.metadata, Some("public".to_string()));
    }

    #[test]
    fn test_generic_fqn_with_fqn_parts() {
        let parts = vec![
            FQNPart::new(
                "Module".to_string(),
                "MyModule".to_string(),
                Range::new(Position::new(10, 20), Position::new(10, 20), (10, 20)),
            ),
            FQNPart::new(
                "Class".to_string(),
                "MyClass".to_string(),
                Range::new(Position::new(10, 20), Position::new(10, 20), (10, 20)),
            ),
            FQNPart::new(
                "Method".to_string(),
                "my_method".to_string(),
                Range::new(Position::new(10, 20), Position::new(10, 20), (10, 20)),
            ),
        ];

        let fqn: Fqn<FQNPart> = Fqn::new(parts);

        assert_eq!(fqn.len(), 3);
        assert!(!fqn.is_top_level());
        assert_eq!(fqn.parts[0].node_type, "Module");
        assert_eq!(fqn.parts[1].node_name, "MyClass");
        assert_eq!(fqn.parts[2].node_type, "Method");
    }

    #[test]
    fn test_fqn_part_with_custom_metadata() {
        // Test with custom metadata types
        #[derive(Debug, Clone, PartialEq, Eq, Hash)]
        struct CustomMetadata {
            visibility: String,
            is_static: bool,
        }

        let part_with_custom_metadata = FQNPart::with_metadata(
            "Method".to_string(),
            "my_method".to_string(),
            Range::new(Position::new(10, 20), Position::new(10, 20), (10, 20)),
            CustomMetadata {
                visibility: "public".to_string(),
                is_static: false,
            },
        );

        assert_eq!(part_with_custom_metadata.node_type, "Method");
        assert_eq!(part_with_custom_metadata.node_name, "my_method");
        assert!(part_with_custom_metadata.metadata.is_some());
        assert_eq!(
            part_with_custom_metadata.metadata.unwrap().visibility,
            "public"
        );
    }
}
