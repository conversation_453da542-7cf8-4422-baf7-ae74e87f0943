use crate::fqn::{FQ<PERSON><PERSON>, Fqn};
use crate::rules::MatchWithNodes;
use crate::typescript::types::TypeScriptNodeFqnMap;
use crate::typescript::types::{TypeScriptDefinitionInfo, TypeScriptDefinitionType};
use crate::typescript::typescript_ast::TypeScriptMatchKind;
use crate::utils::{Position, Range};

use rustc_hash::FxHashMap;
use std::sync::Arc;
use std::sync::LazyLock;

/// Configuration for extracting different types of definitions
#[derive(Debug)]
struct DefinitionExtractor {
    definition_type: TypeScriptDefinitionType,
    extractor: fn(&FxHashMap<String, crate::rules::MetaVarNode>) -> Option<String>,
}

static DEFINITION_EXTRACTORS: LazyLock<Vec<DefinitionExtractor>> = LazyLock::new(|| {
    use crate::typescript::types::ts_meta_vars::*;
    vec![
        DefinitionExtractor {
            definition_type: TypeScriptDefinitionType::Function,
            extractor: |env| env.get(FUNCTION_DEF_NAME).map(|node| node.text.clone()),
        },
        DefinitionExtractor {
            definition_type: TypeScriptDefinitionType::Class,
            extractor: |env| env.get(CLASS_DEF_NAME).map(|node| node.text.clone()),
        },
        DefinitionExtractor {
            definition_type: TypeScriptDefinitionType::NamedClassExpression,
            extractor: |env| env.get(CLASS_EXPR_NAME).map(|node| node.text.clone()),
        },
        DefinitionExtractor {
            definition_type: TypeScriptDefinitionType::Method,
            extractor: |env| env.get(METHOD_DEF_NAME).map(|node| node.text.clone()),
        },
        DefinitionExtractor {
            definition_type: TypeScriptDefinitionType::NamedArrowFunction,
            extractor: |env| {
                env.get(NAMED_ARROW_FUNC_DEF_NAME)
                    .map(|node| node.text.clone())
            },
        },
        DefinitionExtractor {
            definition_type: TypeScriptDefinitionType::NamedFunctionExpression,
            extractor: |env| env.get(NAMED_FUNC_EXP_NAME).map(|node| node.text.clone()),
        },
        DefinitionExtractor {
            definition_type: TypeScriptDefinitionType::NamedGeneratorFunctionExpression,
            extractor: |env| {
                env.get(NAMED_GENERATOR_FUNC_DEF_NAME)
                    .map(|node| node.text.clone())
            },
        },
        DefinitionExtractor {
            definition_type: TypeScriptDefinitionType::Interface,
            extractor: |env| env.get(INTERFACE_DEF_NAME).map(|node| node.text.clone()),
        },
        DefinitionExtractor {
            definition_type: TypeScriptDefinitionType::Namespace,
            extractor: |env| env.get(NAMESPACE_DEF_NAME).map(|node| node.text.clone()),
        },
        DefinitionExtractor {
            definition_type: TypeScriptDefinitionType::Type,
            extractor: |env| env.get(TYPE_DEF_NAME).map(|node| node.text.clone()),
        },
        DefinitionExtractor {
            definition_type: TypeScriptDefinitionType::Enum,
            extractor: |env| env.get(ENUM_DEF_NAME).map(|node| node.text.clone()),
        },
    ]
});

/// Unified entry point for finding definitions with FQN computation
/// This function combines DefinitionExtractor functionality with FQN computation
/// and ensures all meta_vars are captured for each definition
pub fn find_definitions(
    matches: &[MatchWithNodes],
    js_node_fqn_map: &TypeScriptNodeFqnMap,
) -> Vec<TypeScriptDefinitionInfo> {
    let mut definitions = Vec::new();
    for match_item in matches {
        // Only process definition matches
        let match_kind = TypeScriptMatchKind::from_rule_id(&match_item.match_info.rule_id);
        if match_kind == TypeScriptMatchKind::Definition {
            // Extract definition based on the captured variables
            if let Some(def_info) = extract_definition_info(match_item, js_node_fqn_map) {
                definitions.push(def_info);
            }
        }
    }

    definitions
}

/// Create a FQN from a byte range and a NodeFqnMap (lookup)
fn from_node_fqn_map<'a>(range: Range, fqn_map: &TypeScriptNodeFqnMap<'a>) -> Option<Fqn<FQNPart>> {
    fqn_map.get(&range).map(|(_name_node, fqn_parts_arc)| Fqn {
        parts: Arc::new((*fqn_parts_arc).iter().cloned().collect()),
    })
}

/// Extract definition information that combines DefinitionExtractor with FQN computation
/// and captures all variables
fn extract_definition_info(
    match_item: &MatchWithNodes,
    js_node_fqn_map: &TypeScriptNodeFqnMap,
) -> Option<TypeScriptDefinitionInfo> {
    let env = &match_item.match_info.meta_var_map;

    // Try each extractor until we find one that matches
    for extractor in DEFINITION_EXTRACTORS.iter() {
        if let Some(name) = (extractor.extractor)(env) {
            // Try to find FQN for this definition
            let env_var_name = extractor.definition_type.as_env_var();
            let fqn = if let Some(env_node) = env.get(env_var_name) {
                let range = Range::new(
                    Position::new(env_node.range.0, env_node.range.1),
                    Position::new(env_node.range.2, env_node.range.3),
                    env_node.byte_offset,
                );
                from_node_fqn_map(range, js_node_fqn_map)
            } else {
                None
            };

            return Some(TypeScriptDefinitionInfo {
                definition_type: extractor.definition_type,
                name,
                fqn,
                match_info: match_item.match_info.clone(),
            });
        }
    }

    None
}
