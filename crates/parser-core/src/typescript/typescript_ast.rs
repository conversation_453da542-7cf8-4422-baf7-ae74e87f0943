use crate::parser::{SupportedLanguage, load_rules_from_yaml};
use ast_grep_config::RuleConfig;
use ast_grep_language::SupportLang;
use std::{collections::HashMap, sync::LazyLock};

pub const DEFINITIONS_YAML: &str = include_str!("./rules/definitions.yaml");

pub static TYPESCRIPT_RULES: LazyLock<String> = LazyLock::new(|| format!("{DEFINITIONS_YAML}\n"));

pub static RULES_CONFIG: LazyLock<Vec<RuleConfig<SupportLang>>> =
    LazyLock::new(|| load_rules_from_yaml(&TYPESCRIPT_RULES, SupportedLanguage::TypeScript));

#[derive(Debug, PartialEq, Eq, Clone)]
pub enum TypeScriptMatchKind {
    Assignment,
    Definition,
    Reference,
    Import,
    Other(String),
}

pub static RULE_ID_KIND_MAP: LazyLock<HashMap<&'static str, TypeScriptMatchKind>> =
    LazyLock::new(|| {
        let mut m = HashMap::new();
        m.insert("typescript-definitions", TypeScriptMatchKind::Definition);
        m
    });

impl TypeScriptMatchKind {
    pub fn from_rule_id(rule_id: &str) -> Self {
        RULE_ID_KIND_MAP
            .get(rule_id)
            .cloned()
            .unwrap_or_else(|| TypeScriptMatchKind::Other(rule_id.to_string()))
    }
}
