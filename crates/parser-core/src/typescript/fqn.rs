use crate::fqn::FQNPart;
use crate::typescript::types::TypeScriptDefinitionType;
use crate::typescript::types::TypeScriptNodeFqnMap;
use crate::utils::node_to_range;

use ast_grep_core::tree_sitter::StrDoc;
use ast_grep_core::{AstGrep, Node};
use ast_grep_language::SupportLang;
use std::collections::HashMap;
use std::sync::Arc;

/// FQN indexing and computation functionality for JS/TS
/// This handles the core logic for building FQN maps from AST traversal
/// Returns a tuple of (js_node_fqn_map) where:
/// - js_node_fqn_map: Maps byte ranges to (Node, FQN parts with metadata)
pub fn build_fqn_and_node_indices<'a>(
    ast: &'a AstGrep<StrDoc<SupportLang>>,
) -> TypeScriptNodeFqnMap<'a> {
    let mut node_fqn_map = HashMap::new();
    let mut initial_scope: Vec<FQNPart> = Vec::new(); // Start with empty scope

    compute_fqns_and_index_recursive(&ast.root(), &mut initial_scope, &mut node_fqn_map);

    node_fqn_map
}

/// Recursive function to compute FQNs with metadata and build node index
/// This is the core traversal algorithm that builds the FQN map by walking the AST
fn compute_fqns_and_index_recursive<'a>(
    node: &Node<'a, StrDoc<SupportLang>>,
    current_scope: &mut Vec<FQNPart>,
    node_fqn_map: &mut TypeScriptNodeFqnMap<'a>,
) {
    // 1. Index all nodes by byte range
    // let node_range = (node.range().start, node.range().end);

    let node_kind = node.kind();
    let mut is_new_scope = false;
    let mut new_scope_part: Option<FQNPart> = None;

    // 2. Check if node is a definition and compute its FQN with metadata
    match node_kind.as_ref() {
        // Handle anonymous classes with a name assignment E.g with `const foo = class {}`
        "class" => {
            if let Some(parent) = node.parent() {
                let parent_kind = parent.kind();
                let name_node = if parent_kind == "variable_declarator"
                    || parent_kind == "public_field_definition"
                {
                    parent.field("name")
                } else {
                    None
                };

                if let Some(name_node) = name_node {
                    is_new_scope = true;
                    let def_type =
                        TypeScriptDefinitionType::from_node_kind(node_kind.as_ref()).unwrap();
                    new_scope_part = handle_named_declaration(
                        &name_node,
                        node,
                        def_type,
                        current_scope,
                        node_fqn_map,
                    );
                }
            }
        }

        // Handle named declarations E.g with `* {}`
        "class_declaration"
        | "method_definition"
        | "function_declaration"
        | "generator_function_declaration"
        | "enum_declaration" => {
            if let Some(name_node) = node.field("name") {
                let def_type =
                    TypeScriptDefinitionType::from_node_kind(node_kind.as_ref()).unwrap();
                is_new_scope = true;
                new_scope_part = handle_named_declaration(
                    &name_node,
                    node,
                    def_type,
                    current_scope,
                    node_fqn_map,
                );
            }
        }

        // Handle anonymous declarations with a name assignment E.g with `() => {}`
        "arrow_function" | "function_expression" | "generator_function" => {
            if let Some(parent) = node.parent() {
                let parent_kind = parent.kind();
                let name_node = if parent_kind == "variable_declarator"
                    || parent_kind == "public_field_definition"
                {
                    parent.field("name")
                } else {
                    None
                };
                if let Some(name_node) = name_node {
                    let def_type =
                        TypeScriptDefinitionType::from_node_kind(node_kind.as_ref()).unwrap();
                    is_new_scope = true;
                    new_scope_part = handle_named_declaration(
                        &name_node,
                        node,
                        def_type,
                        current_scope,
                        node_fqn_map,
                    );
                }
            }
        }

        // Capture type declarations
        "type_alias_declaration" | "interface_declaration" | "internal_module" => {
            if let Some(name_node) = node.field("name") {
                let def_type =
                    TypeScriptDefinitionType::from_node_kind(node_kind.as_ref()).unwrap();
                is_new_scope = true;
                new_scope_part = handle_named_declaration(
                    &name_node,
                    node,
                    def_type,
                    current_scope,
                    node_fqn_map,
                );
            }
        }
        _ => {}
    }

    // 3. Push new scope part BEFORE recursing children
    if is_new_scope {
        if let Some(scope_part) = new_scope_part {
            current_scope.push(scope_part);
        }
    }

    // 4. Recurse into children
    for child in node.children() {
        compute_fqns_and_index_recursive(&child, current_scope, node_fqn_map);
    }

    // 5. Pop scope part AFTER recursing children
    if is_new_scope {
        current_scope.pop();
    }
}

fn compute_metadata<'a>(
    name_node: &Node<'a, StrDoc<SupportLang>>,
    definition_type: TypeScriptDefinitionType,
) -> Option<&'a str> {
    if definition_type == TypeScriptDefinitionType::Method {
        let parent = name_node.parent().unwrap();
        if parent.text().starts_with("get ") {
            // println!("Parent text: {:?}", parent.text());
            return Some("METHOD_GETTER");
        }
        if parent.text().starts_with("set ") {
            // println!("Parent text: {:?}", parent.text());
            return Some("METHOD_SETTER");
        }
    }
    None
}

/// Helper function to create and insert FQN parts for a named node
fn handle_named_declaration<'a>(
    name_node: &Node<'a, StrDoc<SupportLang>>,
    definition_node: &Node<'a, StrDoc<SupportLang>>,
    definition_type: TypeScriptDefinitionType,
    current_scope: &mut [FQNPart],
    node_fqn_map: &mut TypeScriptNodeFqnMap<'a>,
) -> Option<FQNPart> {
    let name = name_node.text();
    let mut fqn_parts = current_scope.to_owned();

    // TODO: Add metadata to FQNPart
    let _metadata = compute_metadata(name_node, definition_type);
    let definition_range = node_to_range(definition_node);

    let new_part = FQNPart::new(
        definition_type.as_str().to_string(),
        name.to_string(),
        definition_range,
    );

    fqn_parts.push(new_part.clone());

    let name_range = node_to_range(name_node);
    node_fqn_map.insert(name_range, (name_node.clone(), Arc::new(fqn_parts)));

    // Always return true to indicate a new scope part
    Some(new_part)
}
