// TypeScript Features Parser Stress Test - Comprehensive Coverage

// =============================================================================
// TYPE ANNOTATIONS - PRIMITIVES AND BASICS
// =============================================================================

let str: string = "hello";
let num: number = 42;
let bool: boolean = true;
let arr: number[] = [1, 2, 3];
let tuple: [string, number] = ["hello", 42];
let any: any = "anything";
let unknown: unknown = "unknown";
let never: never = (() => { throw new Error(); })();
let nullish: null | undefined = null;
let optional: string | undefined = undefined;

// =============================================================================
// COMPLEX TYPE ANNOTATIONS
// =============================================================================

// Union and intersection types
type AdminPermissions = {
  canEdit: boolean;
  canDelete: boolean;
};

type ApiResponse<T> = {
  data: T;
  error?: string;
};

type Processable<T> = {
  process(): void;
};

let union: string | number | boolean;
let intersection: User & AdminPermissions & { extra: string };

// Generic types
let genericArray: Array<User>;
let map: Map<string, User[]>;
let promise: Promise<ApiResponse<User>>;

// Function types
let fn: (x: number, y: string) => boolean;
let complexFn: <T>(items: T[], predicate: (item: T) => boolean) => T[];

// Object types
let obj: { name: string; age?: number; [key: string]: any };
let readonly: Readonly<{ prop: string }>;

// =============================================================================
// INTERFACES
// =============================================================================

interface BasicInterface {
  name: string;
  age: number;
}

interface ExtendedInterface extends BasicInterface {
  email: string;
}

interface GenericInterface<T, U = string> {
  data: T;
  meta: U;
  process<V>(input: V): Promise<T>;
}

interface ComplexInterface {
  // Method signatures
  method(param: string): void;
  asyncMethod(param: number): Promise<string>;
  
  // Overloaded methods
  overloaded(x: string): string;
  overloaded(x: number): number;
  overloaded(x: boolean): boolean;
  
  // Generic methods
  generic<T>(param: T): T;
  
  // Optional and readonly
  optional?: string;
  readonly readOnly: number;
  
  // Index signatures
  [key: string]: any;
  [index: number]: string;
  
  // Call signatures
  (param: string): void;
  
  // Construct signatures
  new (param: string): BasicInterface;
}

// Merged interfaces
interface MergedInterface {
  prop1: string;
}
interface MergedInterface {
  prop2: number;
}

// =============================================================================
// TYPE ALIASES
// =============================================================================

type StringOrNumber = string | number;
type UserID = string;
type EventHandler<T> = (event: T) => void;
type StringProcessor = (input: string) => string;
type NumberProcessor = (input: number) => number;
type DefaultProcessor = (input: any) => any;

// Complex type aliases
type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

type ConditionalType<T> = T extends string 
  ? StringProcessor 
  : T extends number 
    ? NumberProcessor 
    : DefaultProcessor;

type MappedType<T> = {
  [K in keyof T]: T[K] extends string ? T[K] : never;
};

// =============================================================================
// GENERICS COMPLEXITY
// =============================================================================

// Generic functions
function identity<T>(arg: T): T { return arg; }

type Constrainable = {
  id: string;
};

type DefaultType = {
  name: string;
};

type ProcessOptions<T> = {
  [K in keyof T]?: T[K];
};

type ProcessResult<T> = {
  processed: T;
  metadata: {
    timestamp: number;
  };
};

function process<T>(data: T, processor: (item: T) => T, options?: Partial<ProcessOptions<T>>): ProcessResult<T> {
  return {
    processed: processor(data),
    metadata: {
      timestamp: Date.now()
    }
  };
}

function complexGeneric<T extends Constrainable, U = DefaultType>(
  data: T,
  processor: (item: T) => U,
  options?: Partial<ProcessOptions<T>>
): ProcessResult<U> {
  return {
    processed: processor(data),
    metadata: {
      timestamp: Date.now()
    }
  };
}

type Serializable = {
  serialize(): string;
};

type DefaultSerializable = {
  serialize(): string;
};

// Generic classes
class GenericClass<T, U extends Serializable = DefaultSerializable> {
  private data: T;
  private serializer: U;
  
  constructor(data: T, serializer: U) {
    this.data = data;
    this.serializer = serializer;
  }
  
  process<V>(transformer: (item: T) => V): GenericClass<V, U> {
    return new GenericClass(transformer(this.data), this.serializer);
  }
}

// Generic constraints
interface Lengthwise {
  length: number;
}

function constrainedGeneric<T extends Lengthwise>(arg: T): T {
  console.log(arg.length);
  return arg;
}

// =============================================================================
// ADVANCED TYPE FEATURES
// =============================================================================

// Conditional types
type MyNonNullable<T> = T extends null | undefined ? never : T;
type MyReturnType<T> = T extends (...args: any[]) => infer R ? R : any;
type MyParameters<T> = T extends (...args: infer P) => any ? P : never;

// Mapped types
type MyPartial<T> = {
  [P in keyof T]?: T[P];
};

type MyPick<T, K extends keyof T> = {
  [P in K]: T[P];
};

type MyRecord<K extends keyof any, T> = {
  [P in K]: T;
};

// Template literal types
type EventName<T extends string> = `on${Capitalize<T>}`;
type CSSProperty = `--${string}`;
type ColorVariant = `${string}-${'light' | 'dark'}`;

// Key remapping
type Getters<T> = {
  [K in keyof T as `get${Capitalize<string & K>}`]: () => T[K];
};

// =============================================================================
// UTILITY TYPES USAGE
// =============================================================================

type User = {
  id: string;
  name: string;
  email: string;
};

type PartialUser = Partial<User>;
type RequiredUser = Required<User>;
type UserEmail = Pick<User, 'email'>;
type UserWithoutId = Omit<User, 'id'>;
type UserRecord = Record<string, User>;
type NonNullableUser = NonNullable<User | null>;

// =============================================================================
// CLASSES WITH TYPESCRIPT FEATURES
// =============================================================================

abstract class AbstractClass<T> {
  protected abstract data: T;
  
  abstract process(): void;
  
  public concrete(): string {
    return "concrete";
  }
}

type ProcessMetadata = {
  created: number;
};

class TypedClass<T extends Serializable> extends AbstractClass<T> implements Processable<T> {
  public readonly id: string;
  private _data: T;
  protected metadata: ProcessMetadata;
  
  constructor(data: T, id: string, metadata: ProcessMetadata) {
    super();
    this._data = data;
    this.id = id;
    this.metadata = { created: Date.now() };
  }
  
  get data(): T {
    return this._data;
  }
  
  set data(value: T) {
    this._data = value;
  }
  
  process(): void {
    // Implementation
  }
  
//   // Parameter properties
//   constructor(
//     public name: string,
//     private readonly age: number,
//     protected optional?: string
//   ) {}
  
  // Method overloads
  method(x: string): string;
  method(x: number): number;
  method(x: string | number): string | number {
    return x;
  }
}

// =============================================================================
// FUNCTION SIGNATURES AND OVERLOADS
// =============================================================================

// Function overloads
function overloadedFunction(x: string): string;
function overloadedFunction(x: number): number;
function overloadedFunction(x: boolean): boolean;
function overloadedFunction(x: any): any {
  return x;
}

type ProcessedItem<T> = {
  processed: T;
  metadata: {
    timestamp: number;
  };
};

// Generic function constraints
function processItems<T extends { id: string }>(
  items: T[],
  processor: <U>(item: T) => U
): ProcessedItem<T>[] {
  return items.map(item => ({
    ...item,
    processed: processor(item),
    metadata: {
      timestamp: Date.now()
    }
  }));
}

// Complex function signatures
// type EventListener<T extends Event> = (event: T) => void;
type AsyncProcessor<T, U> = (input: T) => Promise<U>;

function complexFunction<
  T extends Record<string, any>,
  K extends keyof T,
  U = T[K]
>(
  obj: T,
  key: K,
  transformer?: (value: T[K]) => U
): U {
  const value = obj[key];
  return transformer ? transformer(value) : (value as unknown as U);
}

// =============================================================================
// MODULES AND NAMESPACES
// =============================================================================

namespace MyNamespace {
  export interface Config {
    setting: string;
  }
  
  export class Service {
    constructor(private config: Config) {}
  }
  
  export namespace Nested {
    export type NestedType = string;
  }
}

// Module augmentation
declare module "existing-module" {
  interface ExistingInterface {
    newProperty: string;
  }
}

// // Global augmentation
// declare global {
//   interface Window {
//     customProperty: string;
//   }
// }

// =============================================================================
// DECORATORS
// =============================================================================

// function classDecorator<T extends { new(...args: any[]): {} }>(constructor: T) {
//   return class extends constructor {
//     decorated = true;
//   };
// }

// function methodDecorator(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
//   const originalMethod = descriptor.value;
//   descriptor.value = function(...args: any[]) {
//     console.log(`Calling ${propertyKey}`);
//     return originalMethod.apply(this, args);
//   };
// }

// function propertyDecorator(value: string, context: ClassFieldDecoratorContext) {
//   return value;
// }

// @classDecorator
// class DecoratedClass {
//   @propertyDecorator
//   property: string = "decorated";
  
//   @methodDecorator
//   method(): void {
//     console.log("method called");
//   }
// }

// =============================================================================
// ASSERTION AND TYPE GUARDS
// =============================================================================

// Type assertions
let someValue: unknown = "hello";
let strLength: number = (someValue as string).length;
let altSyntax: number = (<string>someValue).length;

// Type guards
function isString(value: unknown): value is string {
  return typeof value === "string";
}

function isUser(obj: any): obj is User {
  return obj && typeof obj.name === "string" && typeof obj.age === "number";
}

// Assertion functions
function assertIsString(value: unknown): asserts value is string {
  if (typeof value !== "string") {
    throw new Error("Expected string");
  }
}

// =============================================================================
// COMPLEX TYPE MANIPULATIONS
// =============================================================================

// Recursive types
type Json = string | number | boolean | null | Json[] | { [key: string]: Json };

// Recursive conditional types
type Flatten<T> = T extends any[] ? Flatten<T[number]> : T;

// Complex mapped types with conditions
type OptionalByType<T, U> = {
  [K in keyof T]: T[K] extends U ? T[K] | undefined : T[K];
};

// Variadic tuple types
type Prepend<T, U extends readonly unknown[]> = [T, ...U];
type Tail<T extends readonly unknown[]> = T extends readonly [unknown, ...infer R] ? R : [];

// =============================================================================
// AMBIENT DECLARATIONS
// =============================================================================

declare const GLOBAL_CONSTANT: string;
declare function globalFunction(param: string): void;

declare class ExternalClass {
  property: string;
  method(): void;
}

// declare module "*.json" {
//   const value: any;
//   export default value;
// }

// =============================================================================
// TRIPLE-SLASH DIRECTIVES
// =============================================================================

/// <reference path="./types.d.ts" />
/// <reference types="node" />

// =============================================================================
// COMPLEX REAL-WORLD PATTERNS
// =============================================================================

// Event system with typed events
type EventMap = {
  'user:login': { userId: string; timestamp: number };
  'user:logout': { userId: string };
  'data:update': { id: string; data: any };
};

class TypedEventEmitter<T extends Record<string, any>> {
  private listeners: {
    [K in keyof T]?: Array<(event: T[K]) => void>;
  } = {};
  
  on<K extends keyof T>(eventName: K, listener: (event: T[K]) => void): void {
    if (!this.listeners[eventName]) {
      this.listeners[eventName] = [];
    }
    this.listeners[eventName]!.push(listener);
  }
  
  emit<K extends keyof T>(eventName: K, event: T[K]): void {
    this.listeners[eventName]?.forEach(listener => listener(event));
  }
}

// Builder pattern with fluent typing
class TypedBuilder<T = {}> {
  private data: T = {} as T;
  
  set<K extends string, V>(key: K, value: V): TypedBuilder<T & Record<K, V>> {
    (this.data as any)[key] = value;
    return this as any;
  }
  
  build(): T {
    return this.data;
  }
}