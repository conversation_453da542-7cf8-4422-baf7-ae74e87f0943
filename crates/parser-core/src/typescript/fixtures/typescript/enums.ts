// TypeScript Enum Stress Test - All enum variants

// Basic enums
enum Basic { A, B, C }
enum Numeric { First = 1, Second, Third }
enum String { Red = "red", Green = "green", Blue = "blue" }
enum Mixed { A = 1, B = "two", C = 3, D = "four" }

// Computed enums
const getValue = () => 1;
enum Computed { A = getValue(), B = A * 2, C = "computed".length }
enum Bitwise { Read = 1 << 0, Write = 1 << 1, Execute = 1 << 2, All = Read | Write | Execute }

// Const enums
const enum ConstEnum { Flag1 = 1, Flag2 = 2 }
const enum ConstString { X = "x", Y = "y" }

// Ambient enums
declare enum AmbientEnum { Unknown }
declare const enum AmbientConst { Value }

// Complex patterns
enum Status {
  Pending = "PENDING",
  Approved = "APPROVED", 
  Rejected = "REJECTED"
}

enum HttpCode {
  OK = 200,
  NotFound = 404,
  ServerError = 500,
  // Computed member after literal
  Custom = OK + 1
}

// Enum with namespace
enum Direction { Up, Down, Left, Right }
namespace Direction {
  export function opposite(dir: Direction): Direction {
    return (dir + 2) % 4;
  }
}

// Heterogeneous with all types
enum Heterogeneous {
  Zero = 0,
  One = 1,
  Two = "two",
  Three = 3,
  Four = getValue() + 1,
  Five = "five"
}

// Reverse mapping test
enum Reverse { A = "value", B = 42 }
const reverseA: string = Reverse.A;
const reverseB: number = Reverse.B;

// Template literal enum
enum Template {
  Prefix = "prefix",
  Suffix = "suffix"
}

// Union with enum
type StatusUnion = Status.Pending | Status.Approved;
type MixedUnion = Basic | String;

// Generic constraints with enums
function processEnum<T extends Record<string, string | number>>(enumObj: T): keyof T {
  return Object.keys(enumObj)[0] as keyof T;
}

// Enum as object keys
type EnumKeys = {
  [K in keyof typeof Status]: boolean;
};

export { Basic, String, Mixed, Computed, Status, Direction, Heterogeneous };
