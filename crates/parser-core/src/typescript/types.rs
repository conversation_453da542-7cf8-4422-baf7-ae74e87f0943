use std::collections::HashMap;
use std::sync::Arc;

use ast_grep_core::Node;
use ast_grep_core::tree_sitter::StrDoc;
use ast_grep_language::SupportLang;

use crate::definitions::DefinitionInfo;
use crate::fqn::{FQNPart, Fqn};
use crate::utils::Range;

// typescript/fqn.rs

pub type TypeScriptNodeFqnMap<'a> =
    HashMap<Range, (Node<'a, StrDoc<SupportLang>>, Arc<Vec<FQNPart>>)>;

// typescript/definitions.rs
pub type TypeScriptDefinitionInfo = DefinitionInfo<TypeScriptDefinitionType, Fqn<FQNPart>>;

/// Type-safe constants for capture variable names used in JS rule definitions
pub mod ts_meta_vars {
    pub const CLASS_DEF_NAME: &str = "CLASS_DEF_NAME";
    pub const CLASS_EXPR_NAME: &str = "CLASS_EXPR_NAME";
    pub const CLASS_EXPR_BODY: &str = "CLASS_EXPR_BODY";
    pub const METHOD_DEF_NAME: &str = "METHOD_DEF_NAME";
    pub const FUNCTION_DEF_NAME: &str = "FUNCTION_DEF_NAME";
    pub const NAMED_ARROW_FUNC_DEF_NAME: &str = "NAMED_ARROW_FUNC_DEF_NAME";
    pub const NAMED_ARROW_FUNC_BODY: &str = "NAMED_ARROW_FUNC_BODY";
    pub const NAMED_FUNC_EXP_NAME: &str = "NAMED_FUNC_EXP_NAME";
    pub const NAMED_FUNC_EXP_BODY: &str = "NAMED_FUNC_EXP_BODY";
    pub const NAMED_GENERATOR_FUNC_DEF_NAME: &str = "NAMED_GENERATOR_FUNC_DEF_NAME";
    pub const NAMED_GENERATOR_FUNC_BODY: &str = "NAMED_GENERATOR_FUNC_BODY";
    pub const INTERFACE_DEF_NAME: &str = "INTERFACE_DEF_NAME";
    pub const NAMESPACE_DEF_NAME: &str = "NAMESPACE_DEF_NAME";
    pub const TYPE_DEF_NAME: &str = "TYPE_DEF_NAME";
    pub const ENUM_DEF_NAME: &str = "ENUM_DEF_NAME";
}

/// Types of definitions that can be found in TS/JS code
#[derive(Debug, Clone, PartialEq, Eq, Hash, Copy)]
pub enum TypeScriptDefinitionType {
    Class,
    NamedClassExpression,
    Method,
    Function,
    NamedFunctionExpression,
    NamedArrowFunction,
    NamedGeneratorFunctionExpression,
    Interface,
    Namespace,
    Type,
    Enum,
}

impl TypeScriptDefinitionType {
    pub fn as_str(&self) -> &'static str {
        match self {
            TypeScriptDefinitionType::Class => "Class",
            TypeScriptDefinitionType::NamedClassExpression => "NamedClassExpression",
            TypeScriptDefinitionType::Method => "Method",
            TypeScriptDefinitionType::Function => "Function",
            TypeScriptDefinitionType::NamedArrowFunction => "NamedArrowFunction",
            TypeScriptDefinitionType::NamedFunctionExpression => "NamedFunctionExpression",
            TypeScriptDefinitionType::NamedGeneratorFunctionExpression => {
                "NamedGeneratorFunctionExpression"
            }
            TypeScriptDefinitionType::Interface => "Interface",
            TypeScriptDefinitionType::Namespace => "Namespace",
            TypeScriptDefinitionType::Type => "Type",
            TypeScriptDefinitionType::Enum => "Enum",
        }
    }

    pub fn as_env_var(&self) -> &'static str {
        match &self {
            TypeScriptDefinitionType::Class => ts_meta_vars::CLASS_DEF_NAME,
            TypeScriptDefinitionType::NamedClassExpression => ts_meta_vars::CLASS_EXPR_NAME,
            TypeScriptDefinitionType::Method => ts_meta_vars::METHOD_DEF_NAME,
            TypeScriptDefinitionType::Function => ts_meta_vars::FUNCTION_DEF_NAME,
            TypeScriptDefinitionType::NamedArrowFunction => ts_meta_vars::NAMED_ARROW_FUNC_DEF_NAME,
            TypeScriptDefinitionType::NamedFunctionExpression => ts_meta_vars::NAMED_FUNC_EXP_NAME,
            TypeScriptDefinitionType::NamedGeneratorFunctionExpression => {
                ts_meta_vars::NAMED_GENERATOR_FUNC_DEF_NAME
            }
            TypeScriptDefinitionType::Interface => ts_meta_vars::INTERFACE_DEF_NAME,
            TypeScriptDefinitionType::Namespace => ts_meta_vars::NAMESPACE_DEF_NAME,
            TypeScriptDefinitionType::Type => ts_meta_vars::TYPE_DEF_NAME,
            TypeScriptDefinitionType::Enum => ts_meta_vars::ENUM_DEF_NAME,
        }
    }

    pub fn from_node_kind(node_kind: &str) -> Option<TypeScriptDefinitionType> {
        match node_kind {
            "class" => Some(TypeScriptDefinitionType::NamedClassExpression),
            "class_declaration" => Some(TypeScriptDefinitionType::Class),
            "method_definition" => Some(TypeScriptDefinitionType::Method),
            "function_declaration" => Some(TypeScriptDefinitionType::Function),
            "generator_function_declaration" => Some(TypeScriptDefinitionType::Function),
            "arrow_function" => Some(TypeScriptDefinitionType::NamedArrowFunction),
            "function_expression" => Some(TypeScriptDefinitionType::NamedFunctionExpression),
            "generator_function" => {
                Some(TypeScriptDefinitionType::NamedGeneratorFunctionExpression)
            }
            "interface_declaration" => Some(TypeScriptDefinitionType::Interface),
            "type_alias_declaration" => Some(TypeScriptDefinitionType::Type),
            "internal_module" => Some(TypeScriptDefinitionType::Namespace),
            "enum_declaration" => Some(TypeScriptDefinitionType::Enum),
            _ => None,
        }
    }
}
