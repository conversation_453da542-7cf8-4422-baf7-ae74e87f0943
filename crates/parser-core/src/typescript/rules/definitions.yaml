id: typescript-definitions
language: typescript
# This rule captures definitions and their signatures.

rule:
  any:
    ####### JAVASCRIPT AND TYPESCRIPT COMMON #######

    # Class definition: capture class name and optional superclass
    - all:
        - kind: class_declaration
        - has:
            field: name
            kind: type_identifier
            pattern: $CLASS_DEF_NAME

    # Class expression: const ClassExpression = class {} or const NamedClassExpression = class NamedClass {}
    - all:
        - kind: variable_declarator
        - has:
            field: name
            kind: identifier
            pattern: $CLASS_EXPR_NAME
        - has:
            field: value
            all:
                - kind: class
                  pattern: $CLASS_EXPR_BODY

    # Static class field with class expression: static InnerStatic = class {}
    - all:
        - kind: public_field_definition
        - has:
            field: name
            kind: property_identifier
            pattern: $CLASS_EXPR_NAME
        - has:
            field: value
            all:
                - kind: class
                  pattern: $CLASS_EXPR_BODY

    # Function declaration: function func() {}
    - kind: function_declaration
      has:
        field: name
        kind: identifier
        pattern: $FUNCTION_DEF_NAME

    # Generator function declaration: async function* asyncGenerator()
    - kind: generator_function_declaration
      has:
        field: name
        kind: identifier
        pattern: $FUNCTION_DEF_NAME

    # Method definition in class: capture method name and parameters
    - kind: method_definition
      has:
        field: name  
        kind: property_identifier
        pattern: $METHOD_DEF_NAME

    # Private Method definition in class: static #validateConfig(config) {}
    - kind: method_definition
      has:
        field: name 
        kind: private_property_identifier
        pattern: $METHOD_DEF_NAME

    # Arrow function assigned to variable: const func = () => {}
    - all:
        - kind: variable_declarator
        - has:
            field: name
            kind: identifier
            pattern: $NAMED_ARROW_FUNC_DEF_NAME
        - has:
            field: value
            all:
                - kind: arrow_function
                  pattern: $NAMED_ARROW_FUNC_BODY

    # Named function expression: const func = function() {}
    - all:
        - kind: variable_declarator
        - has:
            field: name
            kind: identifier
            pattern: $NAMED_FUNC_EXP_NAME
        - has:
            field: value
            all:
                - kind: function_expression
                  pattern: $NAMED_FUNC_EXP_BODY

    # Generator function assigned to variable: const func = function*() {}
    - all:
        - kind: variable_declarator
        - has:
            field: name
            kind: identifier
            pattern: $NAMED_GENERATOR_FUNC_DEF_NAME
        - has:
            field: value
            all:
                - kind: generator_function
                  pattern: $NAMED_GENERATOR_FUNC_BODY

    # Class field: arrow function: class Test { arrowFunc = () => {} }
    - all:
        - kind: public_field_definition
        - has:
            field: name
            kind: property_identifier
            pattern: $NAMED_ARROW_FUNC_DEF_NAME
        - has:
            field: value
            all:
                - kind: arrow_function
                  pattern: $NAMED_ARROW_FUNC_BODY

    # Class field: function expression: class Test { func = function() {} }
    - all:
        - kind: public_field_definition
        - has:
            field: name
            kind: property_identifier
            pattern: $NAMED_FUNC_EXP_NAME
        - has:
            field: value
            all:
                - kind: function_expression
                  pattern: $NAMED_FUNC_EXP_BODY

    # Class field: generator function: class Test { func = function*() {} }
    - all:
        - kind: public_field_definition
        - has:
            field: name
            kind: property_identifier
            pattern: $NAMED_GENERATOR_FUNC_DEF_NAME
        - has:
            field: value
            all:
                - kind: generator_function
                  pattern: $NAMED_GENERATOR_FUNC_BODY

    ####### TYPESCRIPT SPECIFIC #######

    # Interface declaration: interface Name {}
    - all:
        - kind: interface_declaration
        - has:
            field: name
            kind: type_identifier
            pattern: $INTERFACE_DEF_NAME

    # # Interface method declaration: interface Name { methodName(): void }
    # - all:
    #     - kind: method_signature
    #     - has:
    #         field: name
    #         kind: property_identifier
    #         pattern: $INTERFACE_METHOD_DEF_NAME

    # Type declaration: type Name = {}
    - all:
        - kind: type_alias_declaration
        - has:
            field: name
            kind: type_identifier
            pattern: $TYPE_DEF_NAME

    # namespace declaration: namespace Name {}
    - all:
        - kind: internal_module
        - has:
            field: name
            kind: identifier
            pattern: $NAMESPACE_DEF_NAME

    # enum declaration: enum Numeric { First = 1, Second, Third }
    - all:
        - kind: enum_declaration
        - has:
            field: name
            kind: identifier
            pattern: $ENUM_DEF_NAME
