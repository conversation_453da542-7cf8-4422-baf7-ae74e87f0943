use crate::java::java_ast::JavaMatchKind;
use crate::java::types::{JavaDefinitionInfo, JavaDefinitionType, JavaFqn, JavaNodeFqnMap};
use crate::rules::{MatchWithNodes, MetaVarNode};
use once_cell::sync::Lazy;
use rustc_hash::FxHashMap;

/// Type-safe constants for capture variable names used in Java rule definitions
pub mod meta_vars {
    pub const CLASS_DEF_NAME: &str = "CLASS_DEF_NAME";
    pub const INTERFACE_DEF_NAME: &str = "INTERFACE_DEF_NAME";
    pub const ENUM_DEF_NAME: &str = "ENUM_DEF_NAME";
    pub const ENUM_CONSTANT_DEF_NAME: &str = "ENUM_CONSTANT_DEF_NAME";
    pub const RECORD_DEF_NAME: &str = "RECORD_DEF_NAME";
    pub const ANNOTATION_DEF_NAME: &str = "ANNOTATION_DEF_NAME";
    pub const ANNOTATION_DECLARATION_DEF_NAME: &str = "ANNOTATION_DECLARATION_DEF_NAME";
    pub const METHOD_DEF_NAME: &str = "METHOD_DEF_NAME";
    pub const CONSTRUCTOR_DEF_NAME: &str = "CONSTRUCTOR_DEF_NAME";
    pub const LAMBDA_DEF_NAME: &str = "LAMBDA_DEF_NAME";
}

/// Configuration for extracting different types of definitions
/// Each extractor is configured with a specific definition type and extraction function
#[derive(Debug)]
struct DefinitionExtractor {
    /// The type of definition this extractor handles
    definition_type: JavaDefinitionType,
    /// Function that extracts the definition name from captured meta variables
    extractor: fn(&FxHashMap<String, crate::rules::MetaVarNode>) -> Option<&MetaVarNode>,
}

/// Unified entry point for finding definitions with FQN computation
/// This function combines DefinitionExtractor functionality with FQN computation
/// and ensures all meta_vars are captured for each definition
pub fn find_definitions(
    matches: &[MatchWithNodes],
    java_node_fqn_map: &JavaNodeFqnMap,
) -> Vec<JavaDefinitionInfo> {
    let mut definitions = Vec::new();

    for match_item in matches {
        // Only process definition matches
        if JavaMatchKind::from_rule_id(&match_item.match_info.rule_id) != JavaMatchKind::Definition
        {
            continue;
        }

        // Extract definition based on the captured variables
        if let Some(def_info) = extract_definition_info(match_item, java_node_fqn_map) {
            definitions.push(def_info);
        }
    }

    definitions
}

static DEFINITIONS_EXTRACTORS: Lazy<Vec<DefinitionExtractor>> = Lazy::new(|| {
    use meta_vars::*;

    vec![
        DefinitionExtractor {
            definition_type: JavaDefinitionType::Class,
            extractor: |env| env.get(CLASS_DEF_NAME),
        },
        DefinitionExtractor {
            definition_type: JavaDefinitionType::Interface,
            extractor: |env| env.get(INTERFACE_DEF_NAME),
        },
        DefinitionExtractor {
            definition_type: JavaDefinitionType::Enum,
            extractor: |env| env.get(ENUM_DEF_NAME),
        },
        DefinitionExtractor {
            definition_type: JavaDefinitionType::EnumConstant,
            extractor: |env| env.get(ENUM_CONSTANT_DEF_NAME),
        },
        DefinitionExtractor {
            definition_type: JavaDefinitionType::Record,
            extractor: |env| env.get(RECORD_DEF_NAME),
        },
        DefinitionExtractor {
            definition_type: JavaDefinitionType::Annotation,
            extractor: |env| env.get(ANNOTATION_DEF_NAME),
        },
        DefinitionExtractor {
            definition_type: JavaDefinitionType::AnnotationDeclaration,
            extractor: |env| env.get(ANNOTATION_DECLARATION_DEF_NAME),
        },
        DefinitionExtractor {
            definition_type: JavaDefinitionType::Method,
            extractor: |env| env.get(METHOD_DEF_NAME),
        },
        DefinitionExtractor {
            definition_type: JavaDefinitionType::Constructor,
            extractor: |env| env.get(CONSTRUCTOR_DEF_NAME),
        },
        DefinitionExtractor {
            definition_type: JavaDefinitionType::Lambda,
            extractor: |env| env.get(LAMBDA_DEF_NAME),
        },
    ]
});

/// Extract definition information that combines DefinitionExtractor with FQN computation
/// and captures all variables. This function handles the core logic of extracting
/// definition information from AST matches.
fn extract_definition_info(
    match_item: &MatchWithNodes,
    java_node_fqn_map: &JavaNodeFqnMap,
) -> Option<JavaDefinitionInfo> {
    let env = &match_item.match_info.meta_var_map;

    // Try each extractor until we find one that matches
    for extractor in &*DEFINITIONS_EXTRACTORS {
        if let Some(meta_var_node) = (extractor.extractor)(env) {
            // Try to find FQN for this definition
            let fqn = find_fqn_for_definition(match_item, java_node_fqn_map);

            return Some(JavaDefinitionInfo::new(
                extractor.definition_type,
                meta_var_node.text.clone(),
                fqn.cloned(),
                match_item.match_info.clone(),
            ));
        }
    }

    None
}

/// Find FQN for a definition by looking up its name node in the Java FQN map
/// Returns the Java FQN with metadata directly
fn find_fqn_for_definition<'a>(
    match_item: &MatchWithNodes,
    node_fqn_map: &'a JavaNodeFqnMap,
) -> Option<&'a JavaFqn> {
    let range = match_item.match_info.range;

    node_fqn_map.get(&range).map(|(_, fqn)| fqn)
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::java::fqn::build_fqn_index;
    use crate::parser::{GenericParser, LanguageParser, SupportedLanguage};
    use crate::rules::{RuleManager, run_rules};

    /// Helper function to test a definition type with a code snippet
    fn test_definition_extraction(
        code: &str,
        expected_definitions: Vec<(&str, JavaDefinitionType, &str)>, // (name, type, expected_fqn)
        description: &str,
    ) {
        println!("\n=== Testing: {description} ===");
        println!("Code snippet:\n{code}");

        let parser = GenericParser::default_for_language(SupportedLanguage::Java);
        let parse_result = parser.parse(code, Some("test.java")).unwrap();
        let rule_manager = RuleManager::new(SupportedLanguage::Java);
        let matches = run_rules(&parse_result.ast, Some("test.java"), &rule_manager);

        let java_node_fqn_map = build_fqn_index(&parse_result.ast);
        let definitions = find_definitions(&matches, &java_node_fqn_map);

        println!("Found {} definitions:", definitions.len());
        for def in &definitions {
            let fqn_str = def
                .fqn
                .as_ref()
                .map(|fqn| {
                    fqn.iter()
                        .map(|part| part.node_name.clone())
                        .collect::<Vec<_>>()
                        .join(".")
                })
                .unwrap_or_else(|| "None".to_string());
            println!("  {:?}: {} -> {}", def.definition_type, def.name, fqn_str);
        }

        assert_eq!(
            definitions.len(),
            expected_definitions.len(),
            "Expected {} definitions, found {}",
            expected_definitions.len(),
            definitions.len()
        );

        for (expected_name, expected_type, expected_fqn) in expected_definitions {
            let matching_defs: Vec<_> = definitions
                .iter()
                .filter(|d| d.name == expected_name && d.definition_type == expected_type)
                .collect();

            assert!(
                !matching_defs.is_empty(),
                "Could not find definition: {expected_name} of type {expected_type:?}"
            );

            matching_defs
                .iter()
                .find(|d| {
                    let actual_fqn = d.fqn.as_ref().unwrap_or_else(|| {
                        panic!("Expected FQN for {expected_name}, but found None")
                    });
                    let actual_fqn_str = actual_fqn
                        .iter()
                        .map(|part| part.node_name.clone())
                        .collect::<Vec<_>>()
                        .join(".");
                    actual_fqn_str == expected_fqn
                })
                .unwrap_or_else(|| {
                    panic!(
                        "Found definition {expected_name} of type {expected_type:?} but with different FQN than expected '{expected_fqn}'"
                    )
                });
        }
    }

    #[test]
    fn test_java_code_with_package_definitions() {
        test_definition_extraction(
            r#"
            package com.example.test;

            public class MyClass {
                private int myField = 1;

                public void myMethod() {
                    System.out.println("Hello, World!");
                }
            }
            "#,
            vec![
                (
                    "MyClass",
                    JavaDefinitionType::Class,
                    "com.example.test.MyClass",
                ),
                (
                    "myMethod",
                    JavaDefinitionType::Method,
                    "com.example.test.MyClass.myMethod",
                ),
            ],
            "Package with class and method",
        );
    }

    #[test]
    fn test_java_code_with_class_definitions() {
        test_definition_extraction(
            r#"
            public class MyClass {
                private int myField = 1;

                public MyClass() {
                    // constructor
                }

                public void myMethod() {
                    System.out.println("Hello, World!");
                }
            }
            "#,
            vec![
                ("MyClass", JavaDefinitionType::Class, "MyClass"),
                (
                    "MyClass",
                    JavaDefinitionType::Constructor,
                    "MyClass.MyClass",
                ),
                ("myMethod", JavaDefinitionType::Method, "MyClass.myMethod"),
            ],
            "Basic class with constructor and method",
        );
    }

    #[test]
    fn test_java_code_with_interface_definitions() {
        test_definition_extraction(
            r#"
            public interface Repository<T> {
                T findById(String id);
                T save(T entity);
            }

            public class UserRepository implements Repository<User> {
                @Override
                public User findById(String id) {
                    return new User(id);
                }

                @Override
                public User save(User entity) {
                    return entity;
                }
            }
            "#,
            vec![
                ("Repository", JavaDefinitionType::Interface, "Repository"),
                (
                    "findById",
                    JavaDefinitionType::Method,
                    "Repository.findById",
                ),
                ("save", JavaDefinitionType::Method, "Repository.save"),
                (
                    "UserRepository",
                    JavaDefinitionType::Class,
                    "UserRepository",
                ),
                (
                    "findById",
                    JavaDefinitionType::Method,
                    "UserRepository.findById",
                ),
                ("save", JavaDefinitionType::Method, "UserRepository.save"),
            ],
            "Interface with implementation",
        );
    }

    #[test]
    fn test_java_code_with_enum_definitions() {
        test_definition_extraction(
            r#"
            public enum Status {
                ACTIVE("active"),
                INACTIVE("inactive");

                private final String value;

                Status(String value) {
                    this.value = value;
                }

                public String getValue() {
                    return value;
                }
            }
            "#,
            vec![
                ("Status", JavaDefinitionType::Enum, "Status"),
                ("ACTIVE", JavaDefinitionType::EnumConstant, "Status.ACTIVE"),
                (
                    "INACTIVE",
                    JavaDefinitionType::EnumConstant,
                    "Status.INACTIVE",
                ),
                ("Status", JavaDefinitionType::Constructor, "Status.Status"),
                ("getValue", JavaDefinitionType::Method, "Status.getValue"),
            ],
            "Enum class with entries",
        );
    }

    #[test]
    fn test_java_code_with_record_definitions() {
        test_definition_extraction(
            r#"
            public record Person(String name, int age) {
                public Person {
                    if (age < 0) {
                        throw new IllegalArgumentException("Age cannot be negative");
                    }
                }

                public String getDisplayName() {
                    return name + " (" + age + ")";
                }
            }
            "#,
            vec![
                ("Person", JavaDefinitionType::Record, "Person"),
                ("Person", JavaDefinitionType::Constructor, "Person.Person"),
                (
                    "getDisplayName",
                    JavaDefinitionType::Method,
                    "Person.getDisplayName",
                ),
            ],
            "Record class with compact constructor",
        );
    }

    #[test]
    fn test_java_code_with_annotation_definitions() {
        test_definition_extraction(
            r#"
            @Target(ElementType.TYPE)
            @Retention(RetentionPolicy.RUNTIME)
            public @interface MyAnnotation {
                String value() default "";
                int count() default 0;
            }

            @MyAnnotation(value = "test", count = 5)
            public class AnnotatedClass {
                public void myMethod() {
                    System.out.println("Hello, World!");
                }
            }
            "#,
            vec![
                (
                    "MyAnnotation",
                    JavaDefinitionType::Annotation,
                    "MyAnnotation",
                ),
                (
                    "value",
                    JavaDefinitionType::AnnotationDeclaration,
                    "MyAnnotation.value",
                ),
                (
                    "count",
                    JavaDefinitionType::AnnotationDeclaration,
                    "MyAnnotation.count",
                ),
                (
                    "AnnotatedClass",
                    JavaDefinitionType::Class,
                    "AnnotatedClass",
                ),
                (
                    "myMethod",
                    JavaDefinitionType::Method,
                    "AnnotatedClass.myMethod",
                ),
            ],
            "Annotation class",
        );
    }

    #[test]
    fn test_java_code_with_nested_classes() {
        test_definition_extraction(
            r#"
            public class OuterClass {
                public class InnerClass {
                    public void innerMethod() {
                        System.out.println("Hello from inner class!");
                    }
                }
            }
            "#,
            vec![
                ("OuterClass", JavaDefinitionType::Class, "OuterClass"),
                (
                    "InnerClass",
                    JavaDefinitionType::Class,
                    "OuterClass.InnerClass",
                ),
                (
                    "innerMethod",
                    JavaDefinitionType::Method,
                    "OuterClass.InnerClass.innerMethod",
                ),
            ],
            "Nested classes",
        );
    }

    #[test]
    fn test_java_code_with_lambda_definitions() {
        test_definition_extraction(
            r#"
             public class Main {
                static Function<Void> STATIC_CALLABLE = () -> {};
                Runnable fieldCallable = () -> {};
            
                public void main() {
                    BiFunction<Integer, String, Void> mainCallable = (i, s) -> {};
                }
            }
            "#,
            vec![
                ("Main", JavaDefinitionType::Class, "Main"),
                (
                    "STATIC_CALLABLE",
                    JavaDefinitionType::Lambda,
                    "Main.STATIC_CALLABLE",
                ),
                (
                    "fieldCallable",
                    JavaDefinitionType::Lambda,
                    "Main.fieldCallable",
                ),
                ("main", JavaDefinitionType::Method, "Main.main"),
                (
                    "mainCallable",
                    JavaDefinitionType::Lambda,
                    "Main.main.mainCallable",
                ),
            ],
            "Lambda definitions",
        );
    }
}
