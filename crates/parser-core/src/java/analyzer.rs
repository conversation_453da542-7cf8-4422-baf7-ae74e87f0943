use crate::{
    <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Result,
    java::{
        definitions::find_definitions,
        fqn::{build_fqn_index, java_fqn_to_string},
        types::{JavaDefinitionType, JavaFqn},
    },
    rules::MatchWithNodes,
};

pub type JavaAnalyzer = Analyzer<JavaFqn, JavaDefinitionType>;

pub type JavaAnalyzerResult = AnalysisResult<JavaFqn, JavaDefinitionType>;

impl JavaAnalyzer {
    pub fn analyze(
        &self,
        matches: &[MatchWithNodes],
        parser_result: &ParseResult,
    ) -> Result<JavaAnalyzerResult> {
        let node_fqn_map = build_fqn_index(&parser_result.ast);
        let definitions = find_definitions(matches, &node_fqn_map);
        let imports = Vec::new();

        Ok(JavaAnalyzerResult::new(definitions, imports))
    }
}

impl JavaAnalyzerResult {
    pub fn java_definition_fqn_strings(&self) -> Vec<String> {
        self.definition_fqn_strings(java_fqn_to_string)
    }
}

#[cfg(test)]
mod tests {
    use crate::{
        Defin<PERSON><PERSON><PERSON><PERSON>, LanguageParser, RuleManager, SupportedLanguage, parser::GenericParser,
        rules::run_rules,
    };

    use super::*;

    #[test]
    fn test_definition_grouping_and_filtering() {
        let analyzer = JavaAnalyzer::new();
        let fixture_path = "src/java/fixtures/ComprehensiveJavaDefinitions.java";
        let java_code = std::fs::read_to_string(fixture_path)
            .expect("Should be able to read ComprehensiveJavaDefinitions.java fixture");

        let parser = GenericParser::default_for_language(SupportedLanguage::Java);
        let parse_result = parser.parse(&java_code, Some(fixture_path)).unwrap();

        let rule_manager = RuleManager::new(SupportedLanguage::Java);
        let matches = run_rules(&parse_result.ast, Some(fixture_path), &rule_manager);

        let result = analyzer.analyze(&matches, &parse_result).unwrap();

        assert!(
            !result
                .definitions_of_type(&JavaDefinitionType::Class)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&JavaDefinitionType::Method)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&JavaDefinitionType::Constructor)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&JavaDefinitionType::Interface)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&JavaDefinitionType::Enum)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&JavaDefinitionType::EnumConstant)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&JavaDefinitionType::Record)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&JavaDefinitionType::Annotation)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&JavaDefinitionType::AnnotationDeclaration)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&JavaDefinitionType::Lambda)
                .is_empty()
        );
    }

    #[test]
    fn test_comprehensive_definitions_fixture() {
        let analyzer = JavaAnalyzer::new();
        let fixture_path = "src/java/fixtures/ComprehensiveJavaDefinitions.java";
        let java_code = std::fs::read_to_string(fixture_path)
            .expect("Should be able to read ComprehensiveJavaDefinitions.java fixture");

        let parser = GenericParser::default_for_language(SupportedLanguage::Java);
        let parse_result = parser.parse(&java_code, Some(fixture_path)).unwrap();
        let rule_manager = RuleManager::new(SupportedLanguage::Java);
        let matches = run_rules(&parse_result.ast, Some(fixture_path), &rule_manager);

        let result = analyzer.analyze(&matches, &parse_result).unwrap();

        println!(
            "ComprehensiveJavaDefinitions.java analyzer counts: {:?}",
            result.count_definitions_by_type()
        );

        validate_definition_exists(&result, "Disposable", JavaDefinitionType::Annotation);
        validate_definition_exists(&result, "value", JavaDefinitionType::AnnotationDeclaration);
        validate_definition_exists(&result, "count", JavaDefinitionType::AnnotationDeclaration);

        validate_definition_exists(&result, "Time", JavaDefinitionType::Class);

        validate_definition_exists(&result, "Project", JavaDefinitionType::Record);
        validate_definition_exists(&result, "Project", JavaDefinitionType::Constructor);
        validate_definition_exists(&result, "default", JavaDefinitionType::Method);
        validate_definition_exists(&result, "display", JavaDefinitionType::Method);

        validate_definition_exists(&result, "Constants", JavaDefinitionType::Class);

        validate_definition_exists(&result, "AccessResult", JavaDefinitionType::Enum);
        validate_definition_exists(&result, "UNKNOWN_PROJECT", JavaDefinitionType::EnumConstant);
        validate_definition_exists(&result, "ACCESS_EXPIRED", JavaDefinitionType::EnumConstant);
        validate_definition_exists(&result, "ACCESS_OK", JavaDefinitionType::EnumConstant);

        validate_definition_exists(
            &result,
            "IProjectAccessService",
            JavaDefinitionType::Interface,
        );
        validate_definition_exists(&result, "ProjectAccessService", JavaDefinitionType::Class);
        validate_definition_exists(&result, "validateAccess", JavaDefinitionType::Method);
        validate_definition_exists(&result, "revokeAccess", JavaDefinitionType::Method);

        validate_definition_exists(&result, "Person", JavaDefinitionType::Record);
        validate_definition_exists(&result, "getDisplayName", JavaDefinitionType::Method);

        validate_definition_exists(&result, "Main", JavaDefinitionType::Class);
        validate_definition_exists(&result, "printServiceUrl", JavaDefinitionType::Lambda);
        validate_definition_exists(&result, "main", JavaDefinitionType::Method);

        // Test for multiple definitions with the same name (constructors)
        validate_definition_exists_with_count(
            &result,
            "ProjectAccessService",
            JavaDefinitionType::Constructor,
            2,
        );
    }

    fn validate_definition_exists(
        result: &JavaAnalyzerResult,
        name: &str,
        expected_type: JavaDefinitionType,
    ) {
        let defs = result.definitions_by_name(name);

        assert!(!defs.is_empty(), "Should find {name} definition");
        let matching_defs: Vec<_> = defs
            .iter()
            .filter(|def| def.definition_type == expected_type)
            .collect();
        assert!(
            !matching_defs.is_empty(),
            "Definition type mismatch for {}, expected {:?}, but found types: {:?}",
            name,
            expected_type,
            defs.iter()
                .map(|def| def.definition_type)
                .collect::<Vec<_>>()
        );
    }

    fn validate_definition_exists_with_count(
        result: &JavaAnalyzerResult,
        name: &str,
        expected_type: JavaDefinitionType,
        expected_count: usize,
    ) {
        let defs = result.definitions_by_name(name);

        assert!(!defs.is_empty(), "Should find {name} definition");
        let matching_defs: Vec<_> = defs
            .iter()
            .filter(|def| def.definition_type == expected_type)
            .collect();
        assert!(
            !matching_defs.is_empty(),
            "Definition type mismatch for {}, expected {:?}, but found types: {:?}",
            name,
            expected_type,
            defs.iter()
                .map(|def| def.definition_type)
                .collect::<Vec<_>>()
        );
        assert_eq!(
            matching_defs.len(),
            expected_count,
            "Should find exactly {expected_count} {name} definitions of type {expected_type:?}"
        );
    }
}
