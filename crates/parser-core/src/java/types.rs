use rustc_hash::FxHashMap;
use std::sync::Arc;

use ast_grep_core::{Node, tree_sitter::StrDoc};
use ast_grep_language::SupportLang;
use smallvec::SmallVec;

use crate::definitions::{DefinitionInfo, DefinitionTypeInfo};
use crate::fqn::FQNPart;
use crate::utils::Range;

// FQN types

#[derive(Debug, Clone, PartialEq, Eq, Hash, serde::Serialize, serde::Deserialize)]
pub enum JavaFqnPartType {
    Class,
    Interface,
    Enum,
    EnumConstant,
    Record,
    Annotation,
    AnnotationDeclaration,
    Method,
    Constructor,
    Lambda,
    Package,
}

/// Java-specific metadata for FQN parts (currently empty, placeholder for future use)
#[derive(Clone, Default, Debug, PartialEq, Eq, Hash)]
pub struct JavaFqnMetadata;

/// Java-specific FQN part with metadata
pub type JavaFqnPart = FQNPart<JavaFqnPartType, JavaFqnMetadata>;

/// Java-specific FQN with rich metadata
pub type JavaFqn = Arc<SmallVec<[JavaFqnPart; 8]>>;

/// Maps node ranges to their corresponding AST nodes and FQN parts
pub type JavaNodeFqnMap<'a> = FxHashMap<Range, (Node<'a, StrDoc<SupportLang>>, JavaFqn)>;

// Definition types

/// Represents a Java definition found in the code
/// This is now a type alias using the generic DefinitionInfo with Java-specific types
pub type JavaDefinitionInfo = DefinitionInfo<JavaDefinitionType, JavaFqn>;

/// Types of definitions that can be found in Java code
/// Limited to what is actually supported in the Java FQN implementation
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, serde::Serialize, serde::Deserialize)]
pub enum JavaDefinitionType {
    Class,
    Interface,
    Enum,
    EnumConstant,
    Record,
    Annotation,
    AnnotationDeclaration,
    Method,
    Constructor,
    Lambda,
}

impl DefinitionTypeInfo for JavaDefinitionType {
    /// Convert JavaDefinitionType to its string representation
    fn as_str(&self) -> &str {
        match self {
            JavaDefinitionType::Class => "Class",
            JavaDefinitionType::Interface => "Interface",
            JavaDefinitionType::Enum => "Enum",
            JavaDefinitionType::EnumConstant => "EnumConstant",
            JavaDefinitionType::Record => "Record",
            JavaDefinitionType::Annotation => "Annotation",
            JavaDefinitionType::AnnotationDeclaration => "AnnotationDeclaration",
            JavaDefinitionType::Method => "Method",
            JavaDefinitionType::Constructor => "Constructor",
            JavaDefinitionType::Lambda => "Lambda",
        }
    }
}
