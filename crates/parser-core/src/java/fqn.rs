use crate::{
    java::types::{JavaFqn, JavaFqnPart, JavaFqnPartType, JavaNodeFqnMap},
    utils::node_to_range,
};
use ast_grep_core::{AstGrep, Node, tree_sitter::StrDoc};
use ast_grep_language::SupportLang;
use rustc_hash::FxHashMap;
use smallvec::{SmallVec, smallvec};
use std::sync::Arc;

mod node_types {
    pub const CLASS: &str = "class_declaration";
    pub const INTERFACE: &str = "interface_declaration";
    pub const ENUM: &str = "enum_declaration";
    pub const ENUM_CONSTANT: &str = "enum_constant";
    pub const RECORD: &str = "record_declaration";
    pub const ANNOTATION: &str = "annotation_type_declaration";
    pub const ANNOTATION_DECLARATION: &str = "annotation_type_element_declaration";
    pub const METHOD: &str = "method_declaration";
    pub const CONSTRUCTOR: &str = "constructor_declaration";
    pub const FORMAL_PARAMETER_LIST: &str = "formal_parameters";
    pub const PACKAGE: &str = "package_declaration";
    pub const SCOPED_IDENTIFIER: &str = "scoped_identifier";
    pub const FIELD_DECLARATION: &str = "field_declaration";
    pub const LOCAL_VARIABLE_DECLARATION: &str = "local_variable_declaration";
}

type ScopeStack = SmallVec<[JavaFqnPart; 8]>;

fn process_simple_node(
    node: &Node<StrDoc<SupportLang>>,
    node_type: JavaFqnPartType,
) -> Option<JavaFqnPart> {
    let identifier_node = node.field("name")?;
    let name = identifier_node.text().into_owned();

    Some(JavaFqnPart {
        node_type,
        node_name: name,
        range: node_to_range(node),
        metadata: None,
    })
}

fn process_formal_parameter_list_node(node: &Node<StrDoc<SupportLang>>) -> Option<JavaFqnPart> {
    let record_declaration = node.parent()?;

    if record_declaration.kind().as_ref() != node_types::RECORD {
        return None;
    }

    let record_name = record_declaration.field("name")?;
    let record_name_text = record_name.text().into_owned();

    Some(JavaFqnPart {
        node_type: JavaFqnPartType::Constructor,
        node_name: record_name_text,
        range: node_to_range(node),
        metadata: None,
    })
}

fn process_field_declaration_node(node: &Node<StrDoc<SupportLang>>) -> Option<JavaFqnPart> {
    let variable_declarator = node.field("declarator")?;
    let is_lambda = get_child_by_kind(variable_declarator.clone(), "lambda_expression").is_some();

    if !is_lambda {
        return None;
    }

    let lambda_expression = variable_declarator.field("name")?;
    let lambda_expression_text = lambda_expression.text().into_owned();

    Some(JavaFqnPart {
        node_type: JavaFqnPartType::Lambda,
        node_name: lambda_expression_text,
        range: node_to_range(node),
        metadata: None,
    })
}

fn process_local_variable_declaration_node(
    node: &Node<StrDoc<SupportLang>>,
) -> Option<JavaFqnPart> {
    let variable_declarator = node.field("declarator")?;
    let is_lambda = get_child_by_kind(variable_declarator.clone(), "lambda_expression").is_some();

    if !is_lambda {
        return None;
    }

    let lambda_expression = variable_declarator.field("name")?;
    let lambda_expression_text = lambda_expression.text().into_owned();

    Some(JavaFqnPart {
        node_type: JavaFqnPartType::Lambda,
        node_name: lambda_expression_text,
        range: node_to_range(node),
        metadata: None,
    })
}

fn process_node(node: &Node<StrDoc<SupportLang>>) -> Option<JavaFqnPart> {
    match node.kind().as_ref() {
        node_types::CLASS => process_simple_node(node, JavaFqnPartType::Class),
        node_types::INTERFACE => process_simple_node(node, JavaFqnPartType::Interface),
        node_types::ENUM => process_simple_node(node, JavaFqnPartType::Enum),
        node_types::ENUM_CONSTANT => process_simple_node(node, JavaFqnPartType::EnumConstant),
        node_types::RECORD => process_simple_node(node, JavaFqnPartType::Record),
        node_types::ANNOTATION => process_simple_node(node, JavaFqnPartType::Annotation),
        node_types::ANNOTATION_DECLARATION => {
            process_simple_node(node, JavaFqnPartType::Annotation)
        }
        node_types::METHOD => process_simple_node(node, JavaFqnPartType::Method),
        node_types::CONSTRUCTOR => process_simple_node(node, JavaFqnPartType::Constructor),
        node_types::FORMAL_PARAMETER_LIST => process_formal_parameter_list_node(node),
        node_types::FIELD_DECLARATION => process_field_declaration_node(node),
        node_types::LOCAL_VARIABLE_DECLARATION => process_local_variable_declaration_node(node),
        _ => None,
    }
}

pub fn build_fqn_index(ast: &AstGrep<StrDoc<SupportLang>>) -> JavaNodeFqnMap {
    let mut node_fqn_map: JavaNodeFqnMap =
        FxHashMap::with_capacity_and_hasher(128, Default::default());

    let mut current_scope: ScopeStack = smallvec![];

    if let Some(package_declaration) = get_child_by_kind(ast.root(), node_types::PACKAGE) {
        if let Some(package_name) =
            get_child_by_kind(package_declaration, node_types::SCOPED_IDENTIFIER)
        {
            current_scope.push(JavaFqnPart {
                node_type: JavaFqnPartType::Package,
                node_name: package_name.text().to_string(),
                range: node_to_range(&package_name),
                metadata: None,
            });
        }
    }

    let mut stack: Vec<Option<Node<StrDoc<SupportLang>>>> = Vec::with_capacity(128);
    stack.push(Some(ast.root()));

    while let Some(node_option) = stack.pop() {
        if let Some(node) = node_option {
            if let Some(fqn_part) = process_node(&node) {
                current_scope.push(fqn_part);
                stack.push(None);

                // add all children to the stack in reverse order
                push_children_reverse(&node, &mut stack);

                let range = node_to_range(&node);
                node_fqn_map.insert(range, (node, Arc::new(current_scope.clone())));
            } else {
                // add all children to the stack in reverse order
                push_children_reverse(&node, &mut stack);
            }
        } else {
            // pop the current scope
            current_scope.pop();
        }
    }

    node_fqn_map
}

// Helper function to get a child node by kind
fn get_child_by_kind<'a>(
    node: Node<'a, StrDoc<SupportLang>>,
    kind_name: &str,
) -> Option<Node<'a, StrDoc<SupportLang>>> {
    node.children()
        .find(|child| child.kind().as_ref() == kind_name)
}

/// Helper function to add children to stack in reverse order
fn push_children_reverse<'a>(
    node: &Node<'a, StrDoc<SupportLang>>,
    stack: &mut Vec<Option<Node<'a, StrDoc<SupportLang>>>>,
) {
    let children: Vec<_> = node.children().collect();
    stack.reserve(children.len());
    for child in children.into_iter().rev() {
        stack.push(Some(child));
    }
}

/// Convert a Java FQN to its string representation
/// The parts are joined by '.' to form the full FQN string
pub fn java_fqn_to_string(fqn: &JavaFqn) -> String {
    fqn.iter()
        .map(|part| part.node_name.clone())
        .collect::<Vec<_>>()
        .join(".")
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::parser::{GenericParser, LanguageParser, SupportedLanguage};

    #[test]
    fn test_java_code_outside_a_package() {
        let java_code = r#"
        public class MyClass {
            private int myField = 1;

            public void myMethod() {
                System.out.println("Hello, World!");
            }
        }

        public class Main {
            public static void main(String[] args) {
                MyClass obj = new MyClass();
                obj.myMethod();
            }
        }
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Java);
        let parse_result = parser.parse(java_code, Some("test.java")).unwrap();

        let java_node_fqn_map = build_fqn_index(&parse_result.ast);

        let found_fqns = java_fqn_map_to_string(&java_node_fqn_map);
        assert_eq!(found_fqns.len(), 4);

        assert!(found_fqns.contains(&"MyClass".to_string()));
        assert!(found_fqns.contains(&"MyClass.myMethod".to_string()));
        assert!(found_fqns.contains(&"Main".to_string()));
        assert!(found_fqns.contains(&"Main.main".to_string()));
    }

    #[test]
    fn test_java_code_in_a_package() {
        let java_code = r#"
        package com.example.test;

        public class MyClass {
            private int myField = 1;

            public MyClass() {
                // constructor
            }

            public void myMethod() {
                System.out.println("Hello, World!");
            }
        }

        public class Main {
            public static void main(String[] args) {
                MyClass obj = new MyClass();
                obj.myMethod();
            }
        }
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Java);
        let parse_result = parser.parse(java_code, Some("test.java")).unwrap();

        let java_node_fqn_map = build_fqn_index(&parse_result.ast);

        let found_fqns = java_fqn_map_to_string(&java_node_fqn_map);
        assert_eq!(found_fqns.len(), 5);

        assert!(found_fqns.contains(&"com.example.test.MyClass".to_string()));
        assert!(found_fqns.contains(&"com.example.test.MyClass.MyClass".to_string()));
        assert!(found_fqns.contains(&"com.example.test.MyClass.myMethod".to_string()));
        assert!(found_fqns.contains(&"com.example.test.Main".to_string()));
        assert!(found_fqns.contains(&"com.example.test.Main.main".to_string()));
    }

    #[test]
    fn test_includes_declarations_inside_methods() {
        let java_code = r#"
        public class Main {
            public static void main(String[] args) {
                int myLocalVariable = 1;

                class LocalClass {
                    private int localClassField = 1;
                }

                System.out.println(myLocalVariable);
            }
        }
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Java);
        let parse_result = parser.parse(java_code, Some("test.java")).unwrap();

        let java_node_fqn_map = build_fqn_index(&parse_result.ast);

        let found_fqns = java_fqn_map_to_string(&java_node_fqn_map);
        assert_eq!(found_fqns.len(), 3);

        assert!(found_fqns.contains(&"Main".to_string()));
        assert!(found_fqns.contains(&"Main.main".to_string()));
        assert!(found_fqns.contains(&"Main.main.LocalClass".to_string()));
    }

    #[test]
    fn test_nested_classes_are_included_in_fqn() {
        let java_code = r#"
        public class OuterClass {
            public class InnerClass {
                public void innerMethod() {
                    System.out.println("Hello from inner class!");
                }
            }
        }
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Java);
        let parse_result = parser.parse(java_code, Some("test.java")).unwrap();

        let java_node_fqn_map = build_fqn_index(&parse_result.ast);

        let found_fqns = java_fqn_map_to_string(&java_node_fqn_map);
        assert_eq!(found_fqns.len(), 3);

        assert!(found_fqns.contains(&"OuterClass".to_string()));
        assert!(found_fqns.contains(&"OuterClass.InnerClass".to_string()));
        assert!(found_fqns.contains(&"OuterClass.InnerClass.innerMethod".to_string()));
    }

    #[test]
    fn test_interface_definitions_are_included_in_fqn() {
        let java_code = r#"
        public interface Repository<T> {
            T findById(String id);
            T save(T entity);
        }

        public class UserRepository implements Repository<User> {
            @Override
            public User findById(String id) {
                return new User(id);
            }

            @Override
            public User save(User entity) {
                return entity;
            }
        }
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Java);
        let parse_result = parser.parse(java_code, Some("test.java")).unwrap();

        let java_node_fqn_map = build_fqn_index(&parse_result.ast);

        let found_fqns = java_fqn_map_to_string(&java_node_fqn_map);
        assert_eq!(found_fqns.len(), 6);

        assert!(found_fqns.contains(&"Repository".to_string()));
        assert!(found_fqns.contains(&"Repository.findById".to_string()));
        assert!(found_fqns.contains(&"Repository.save".to_string()));
        assert!(found_fqns.contains(&"UserRepository".to_string()));
        assert!(found_fqns.contains(&"UserRepository.findById".to_string()));
        assert!(found_fqns.contains(&"UserRepository.save".to_string()));
    }

    #[test]
    fn test_enum_definitions_are_included_in_fqn() {
        let java_code = r#"
        public enum Status {
            ACTIVE("active"),
            INACTIVE("inactive");

            private final String value;

            Status(String value) {
                this.value = value;
            }

            public String getValue() {
                return value;
            }
        }
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Java);
        let parse_result = parser.parse(java_code, Some("test.java")).unwrap();

        let java_node_fqn_map = build_fqn_index(&parse_result.ast);

        let found_fqns = java_fqn_map_to_string(&java_node_fqn_map);
        assert_eq!(found_fqns.len(), 5);

        assert!(found_fqns.contains(&"Status".to_string()));
        assert!(found_fqns.contains(&"Status.ACTIVE".to_string()));
        assert!(found_fqns.contains(&"Status.INACTIVE".to_string()));
        assert!(found_fqns.contains(&"Status.Status".to_string()));
        assert!(found_fqns.contains(&"Status.getValue".to_string()));
    }

    #[test]
    fn test_record_definitions_are_included_in_fqn() {
        let java_code = r#"
        public record Person(String name, int age) {
            public Person {
                if (age < 0) {
                    throw new IllegalArgumentException("Age cannot be negative");
                }
            }

            public String getDisplayName() {
                return name + " (" + age + ")";
            }
        }
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Java);
        let parse_result = parser.parse(java_code, Some("test.java")).unwrap();

        let java_node_fqn_map = build_fqn_index(&parse_result.ast);

        let found_fqns = java_fqn_map_to_string(&java_node_fqn_map);
        assert_eq!(found_fqns.len(), 3);

        assert!(found_fqns.contains(&"Person".to_string()));
        assert!(found_fqns.contains(&"Person.Person".to_string()));
        assert!(found_fqns.contains(&"Person.getDisplayName".to_string()));
    }

    #[test]
    fn test_annotation_definitions_are_included_in_fqn() {
        let java_code = r#"
        @Target(ElementType.TYPE)
        @Retention(RetentionPolicy.RUNTIME)
        public @interface MyAnnotation {
            String value() default "";
            int count() default 0;
        }

        @MyAnnotation(value = "test", count = 5)
        public class AnnotatedClass {
            public void myMethod() {
                System.out.println("Hello, World!");
            }
        }
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Java);
        let parse_result = parser.parse(java_code, Some("test.java")).unwrap();

        let java_node_fqn_map = build_fqn_index(&parse_result.ast);

        let found_fqns = java_fqn_map_to_string(&java_node_fqn_map);
        assert_eq!(found_fqns.len(), 5);

        assert!(found_fqns.contains(&"MyAnnotation".to_string()));
        assert!(found_fqns.contains(&"MyAnnotation.value".to_string()));
        assert!(found_fqns.contains(&"MyAnnotation.count".to_string()));
        assert!(found_fqns.contains(&"AnnotatedClass".to_string()));
        assert!(found_fqns.contains(&"AnnotatedClass.myMethod".to_string()));
    }

    #[test]
    fn test_method_overloading_have_the_same_fqn() {
        let java_code = r#"
        public class MyClass {
            public void myMethod(int a) {
                System.out.println("int: " + a);
            }

            public void myMethod(String a) {
                System.out.println("String: " + a);
            }
        }
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Java);
        let parse_result = parser.parse(java_code, Some("test.java")).unwrap();

        let java_node_fqn_map = build_fqn_index(&parse_result.ast);

        let found_fqns = java_fqn_map_to_string(&java_node_fqn_map);
        assert_eq!(found_fqns.len(), 3);

        assert!(found_fqns.contains(&"MyClass".to_string()));
        assert!(found_fqns.contains(&"MyClass.myMethod".to_string()));
    }

    #[test]
    fn test_constructors_are_included_in_fqn() {
        let java_code = r#"
        public class MyClass {
            private int value;

            public MyClass() {
                this.value = 0;
            }

            public MyClass(int value) {
                this.value = value;
            }
        }
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Java);
        let parse_result = parser.parse(java_code, Some("test.java")).unwrap();

        let java_node_fqn_map = build_fqn_index(&parse_result.ast);

        let found_fqns = java_fqn_map_to_string(&java_node_fqn_map);
        assert_eq!(found_fqns.len(), 3);

        assert!(found_fqns.contains(&"MyClass".to_string()));
        assert!(found_fqns.contains(&"MyClass.MyClass".to_string()));
    }

    #[test]
    fn test_handles_class_with_modifiers() {
        let java_code = r#"
        public final class MyClass {
            private static final int MY_CONSTANT = 42;
            public String myField = "test";
        }
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Java);
        let parse_result = parser.parse(java_code, Some("test.java")).unwrap();

        let java_node_fqn_map = build_fqn_index(&parse_result.ast);

        let found_fqns = java_fqn_map_to_string(&java_node_fqn_map);
        assert_eq!(found_fqns.len(), 1);

        assert!(found_fqns.contains(&"MyClass".to_string()));
    }

    #[test]
    fn test_static_nested_classes_are_included_in_fqn() {
        let java_code = r#"
        public class OuterClass {
            public static class StaticInnerClass {
                public void staticInnerMethod() {
                    System.out.println("Hello from static inner class!");
                }
            }
        }
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Java);
        let parse_result = parser.parse(java_code, Some("test.java")).unwrap();

        let java_node_fqn_map = build_fqn_index(&parse_result.ast);

        let found_fqns = java_fqn_map_to_string(&java_node_fqn_map);
        assert_eq!(found_fqns.len(), 3);

        assert!(found_fqns.contains(&"OuterClass".to_string()));
        assert!(found_fqns.contains(&"OuterClass.StaticInnerClass".to_string()));
        assert!(found_fqns.contains(&"OuterClass.StaticInnerClass.staticInnerMethod".to_string()));
    }

    #[test]
    fn test_abstract_classes_are_included_in_fqn() {
        let java_code = r#"
        public abstract class AbstractClass {
            protected abstract void abstractMethod();
            
            public void concreteMethod() {
                System.out.println("Concrete method");
            }
        }
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Java);
        let parse_result = parser.parse(java_code, Some("test.java")).unwrap();

        let java_node_fqn_map = build_fqn_index(&parse_result.ast);

        let found_fqns = java_fqn_map_to_string(&java_node_fqn_map);
        assert_eq!(found_fqns.len(), 3);

        assert!(found_fqns.contains(&"AbstractClass".to_string()));
        assert!(found_fqns.contains(&"AbstractClass.abstractMethod".to_string()));
        assert!(found_fqns.contains(&"AbstractClass.concreteMethod".to_string()));
    }

    #[test]
    fn test_lambda_expressions_are_included_in_fqn() {
        let java_code = r#"
        public class Main {
            static Function<Void> STATIC_CALLABLE = () -> {};
            Runnable fieldCallable = () -> {};
            
            public void main() {
                BiFunction<Integer, String, Void> mainCallable = (i, s) -> {};
            }
        }
        "#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Java);
        let parse_result = parser.parse(java_code, Some("test.java")).unwrap();

        let java_node_fqn_map = build_fqn_index(&parse_result.ast);

        let found_fqns = java_fqn_map_to_string(&java_node_fqn_map);
        assert_eq!(found_fqns.len(), 5);

        assert!(found_fqns.contains(&"Main".to_string()));
        assert!(found_fqns.contains(&"Main.fieldCallable".to_string()));
        assert!(found_fqns.contains(&"Main.STATIC_CALLABLE".to_string()));
        assert!(found_fqns.contains(&"Main.main".to_string()));
        assert!(found_fqns.contains(&"Main.main.mainCallable".to_string()));
    }

    fn java_fqn_map_to_string(node_fqn_map: &JavaNodeFqnMap) -> Vec<String> {
        node_fqn_map
            .iter()
            .map(|(_, (_, fqn_part))| {
                fqn_part
                    .iter()
                    .map(|part| part.node_name.clone())
                    .collect::<Vec<_>>()
                    .join(".")
            })
            .collect()
    }
}
