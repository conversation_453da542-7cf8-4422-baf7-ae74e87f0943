use crate::parser::{SupportedLanguage, load_rules_from_yaml};
use ast_grep_config::RuleConfig;
use ast_grep_language::SupportLang;
use rustc_hash::FxHashMap;
use std::sync::LazyLock;

pub const DEFINITIONS_YAML: &str = include_str!("./rules/definitions.yaml");

pub static JAVA_RULES: LazyLock<String> = LazyLock::new(|| DEFINITIONS_YAML.to_string());

pub static RULES_CONFIG: LazyLock<Vec<RuleConfig<SupportLang>>> =
    LazyLock::new(|| load_rules_from_yaml(&JAVA_RULES, SupportedLanguage::Java));

#[derive(Debug, PartialEq, Eq, Clone)]
pub enum JavaMatchKind {
    Definition,
    Other(String),
}

pub static RULE_ID_KIND_MAP: LazyLock<FxHashMap<&'static str, JavaMatchKind>> =
    LazyLock::new(|| {
        let mut m = FxHashMap::default();
        m.insert("java-definitions", JavaMatchKind::Definition);
        m
    });

impl JavaMatchKind {
    pub fn from_rule_id(rule_id: &str) -> Self {
        RULE_ID_KIND_MAP
            .get(rule_id)
            .cloned()
            .unwrap_or_else(|| JavaMatchKind::Other(rule_id.to_string()))
    }
}
