id: java-definitions
language: java
rule:
  any:
    # Class declaration: capture class name
    - kind: class_declaration
      has:
        field: name
        kind: identifier
        pattern: $CLASS_DEF_NAME

    # Interface declaration: capture interface name
    - kind: interface_declaration
      has:
        field: name
        kind: identifier
        pattern: $INTERFACE_DEF_NAME

    # Enum declaration: capture enum name
    - kind: enum_declaration
      has:
        field: name
        kind: identifier
        pattern: $ENUM_DEF_NAME

    # Enum constant: capture enum constant name
    - kind: enum_constant
      has:
        field: name
        kind: identifier
        pattern: $ENUM_CONSTANT_DEF_NAME

    # Record declaration: capture record name
    - kind: record_declaration
      has:
        field: name
        kind: identifier
        pattern: $RECORD_DEF_NAME

    # Annotation type declaration: capture annotation name
    - kind: annotation_type_declaration
      has:
        field: name
        kind: identifier
        pattern: $ANNOTATION_DEF_NAME

    # Annotation type element declaration: capture annotation element name
    - kind: annotation_type_element_declaration
      has:
        field: name
        kind: identifier
        pattern: $ANNOTATION_DECLARATION_DEF_NAME

    # Method declaration: capture method name
    - kind: method_declaration
      has:
        field: name
        kind: identifier
        pattern: $METHOD_DEF_NAME

    # Constructor declaration: capture constructor name
    - kind: constructor_declaration
      has:
        field: name
        kind: identifier
        pattern: $CONSTRUCTOR_DEF_NAME

    # Formal parameter: capture record constructor
    - all:
        - kind: formal_parameters
          inside:
            kind: record_declaration
            has: 
              kind: identifier
              pattern: $CONSTRUCTOR_DEF_NAME
  
    - kind: field_declaration
      has:
        field: declarator
        all:
          - has:
              kind: identifier
              field: name
              pattern: $LAMBDA_DEF_NAME
          - has:
              kind: lambda_expression

    - kind: local_variable_declaration
      has:
        field: declarator
        all:
          - has:
              kind: identifier
              field: name
              pattern: $LAMBDA_DEF_NAME
          - has:
              kind: lambda_expression
