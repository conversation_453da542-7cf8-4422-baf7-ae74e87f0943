//! Core parser traits and implementations

use crate::{Error, Result};
use ast_grep_config::{GlobalRules, RuleConfig, from_yaml_string};
use ast_grep_core::AstGrep;
use ast_grep_core::tree_sitter::StrDoc;
use ast_grep_language::{LanguageExt, SupportLang};
use once_cell::sync::Lazy;
use rustc_hash::FxHashMap;
use serde::{Deserialize, Serialize};
use smallvec::SmallVec;
use std::borrow::Cow;
use std::fmt;

macro_rules! define_languages {
    (
        $(
            $variant:ident {
                name: $name:literal,
                extensions: [$($ext:literal),+],
                names: [$($lang_name:literal),+]
            }
        ),+ $(,)?
    ) => {
        /// Supported languages for parsing
        #[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
        pub enum SupportedLanguage {
            $($variant),+
        }

        $(
            paste::paste! {
                const [<$variant:upper _EXTENSIONS>]: &[&str] = &[$($ext),+];
            }
        )+

        impl SupportedLanguage {
            /// Convert to ast-grep's SupportLang
            pub fn to_support_lang(&self) -> SupportLang {
                match self {
                    $(SupportedLanguage::$variant => SupportLang::$variant),+
                }
            }

            pub const fn as_str(&self) -> &'static str {
                match self {
                    $(SupportedLanguage::$variant => $name),+
                }
            }

            pub const fn file_extensions(&self) -> &'static [&'static str] {
                match self {
                    $(SupportedLanguage::$variant => paste::paste! { [<$variant:upper _EXTENSIONS>] }),+
                }
            }
        }

        static EXTENSION_MAP: Lazy<FxHashMap<&'static str, SupportedLanguage>> = Lazy::new(|| {
            let mut map = FxHashMap::default();
            $(
                $(
                    map.insert($ext, SupportedLanguage::$variant);
                )+
            )+
            map
        });
        /// Detect language from a pre-computed file extension
        ///
        /// # Arguments
        /// * `extension` - The file extension without the dot (e.g., "rb", "py")
        ///
        /// # Example
        /// ```
        /// use parser_core::parser::detect_language_from_extension;
        /// let lang = detect_language_from_extension("rb").unwrap();
        /// ```
        pub fn detect_language_from_extension(extension: &str) -> Result<SupportedLanguage> {
            match extension {
                $($(
                    $ext => Ok(SupportedLanguage::$variant),
                )+)+
                _ => Err(Error::UnsupportedLanguage(format!(
                    "Unsupported file extension: {}",
                    extension
                ))),
            }
        }

        pub fn detect_language_from_path(file_path: &str) -> Result<SupportedLanguage> {
            let path = std::path::Path::new(file_path);
            let extension = path
                .extension()
                .and_then(|ext| ext.to_str())
                .ok_or_else(|| Error::UnsupportedLanguage("No file extension".to_string()))?;

            EXTENSION_MAP
                .get(extension)
                .copied()
                .ok_or_else(|| Error::UnsupportedLanguage(format!(
                    "Unsupported file extension: {}",
                    extension
                )))
        }

        pub fn get_supported_extensions() -> SmallVec<[&'static str; 11]> {
            let mut extensions = SmallVec::new();
            $(
                extensions.extend_from_slice(paste::paste! { [<$variant:upper _EXTENSIONS>] });
            )+
            extensions
        }
    };
}

define_languages! {
    Ruby {
        // string representation of the language
        name: "ruby",
        // file extensions that are associated with the language
        extensions: ["rb", "rbw", "rake", "gemspec"],
        // names that are associated with the language
        // eg. for typescript, we have both "typescript" and "javascript"
        // since the typescript parser can parse both javascript and typescript files
        names: ["ruby"]
    },
    Python {
        name: "python",
        extensions: ["py"],
        names: ["python"]
    },
    TypeScript {
        name: "typescript",
        extensions: ["js", "ts"],
        names: ["typescript", "javascript"]
    },
    Kotlin {
        name: "kotlin",
        extensions: ["kt", "kts"],
        names: ["kotlin"]
    },
    CSharp {
        name: "csharp",
        extensions: ["cs"],
        names: ["csharp"]
    },
    Java {
        name: "java",
        extensions: ["java"],
        names: ["java"]
    }
}

impl fmt::Display for SupportedLanguage {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.as_str())
    }
}

/// Load rules from YAML string for a specific language
pub fn load_rules_from_yaml(yaml: &str, lang: SupportedLanguage) -> Vec<RuleConfig<SupportLang>> {
    let globals = GlobalRules::default();
    from_yaml_string::<SupportLang>(yaml, &globals)
        .expect("Failed to parse YAML rules")
        .into_iter()
        .filter(|rule| rule.language == lang.to_support_lang())
        .collect()
}

/// Result of parsing operations
#[derive(Clone)]
pub struct ParseResult<'a> {
    /// The language that was parsed
    pub language: SupportedLanguage,

    /// File path (if available)
    pub file_path: Option<Cow<'a, str>>,

    /// The AST grep instance for further rule matching
    pub ast: AstGrep<StrDoc<SupportLang>>,
}

impl<'a> ParseResult<'a> {
    /// Create a new ParseResult
    pub fn new(
        language: SupportedLanguage,
        file_path: Option<&'a str>,
        ast: AstGrep<StrDoc<SupportLang>>,
    ) -> Self {
        Self {
            language,
            file_path: file_path.map(Cow::Borrowed),
            ast,
        }
    }
}

/// Core trait for language parsers
pub trait LanguageParser {
    fn parse<'a>(&self, code: &str, file_path: Option<&'a str>) -> Result<ParseResult<'a>>;

    fn language(&self) -> SupportedLanguage;

    fn parse_ast(&self, code: &str) -> Result<AstGrep<StrDoc<SupportLang>>> {
        let ast = self.language().to_support_lang().ast_grep(code);

        if ast.root().text().is_empty() && !code.is_empty() {
            return Err(Error::Parse(
                "Failed to parse AST - empty result".to_string(),
            ));
        }

        Ok(ast)
    }
}

/// A generic parser implementation that can be configured for different languages
#[derive(Debug, Clone)]
pub struct GenericParser {
    language: SupportedLanguage,
}

impl GenericParser {
    pub const fn new(language: SupportedLanguage) -> Self {
        Self { language }
    }

    pub const fn default_for_language(language: SupportedLanguage) -> Self {
        Self::new(language)
    }

    pub const fn language(&self) -> SupportedLanguage {
        self.language
    }
}

impl LanguageParser for GenericParser {
    fn parse<'a>(&self, code: &str, file_path: Option<&'a str>) -> Result<ParseResult<'a>> {
        let ast = self.parse_ast(code)?;

        Ok(ParseResult::new(self.language, file_path, ast))
    }

    fn language(&self) -> SupportedLanguage {
        self.language
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_supported_language_conversion() {
        assert_eq!(SupportedLanguage::Ruby.as_str(), "ruby");
        assert_eq!(SupportedLanguage::Python.as_str(), "python");
    }

    #[test]
    fn test_all_ruby_file_extensions() {
        let extensions = SupportedLanguage::Ruby.file_extensions();
        assert!(extensions.contains(&"rb"));
        assert!(extensions.contains(&"rbw"));
        assert!(extensions.contains(&"rake"));
        assert!(extensions.contains(&"gemspec"));
        assert_eq!(extensions.len(), 4);
    }

    #[test]
    fn test_all_python_file_extensions() {
        let extensions = SupportedLanguage::Python.file_extensions();
        assert!(extensions.contains(&"py"));
        assert_eq!(extensions.len(), 1);
    }

    #[test]
    fn test_language_detection() {
        assert_eq!(
            detect_language_from_path("test.rb").unwrap(),
            SupportedLanguage::Ruby
        );
        assert_eq!(
            detect_language_from_path("test.py").unwrap(),
            SupportedLanguage::Python
        );
        assert!(detect_language_from_path("unknown.xyz").is_err());
    }

    #[test]
    fn test_detect_language_from_extension() {
        // Test the new optimized function
        assert_eq!(
            detect_language_from_extension("rb").unwrap(),
            SupportedLanguage::Ruby
        );
        assert_eq!(
            detect_language_from_extension("rbw").unwrap(),
            SupportedLanguage::Ruby
        );
        assert_eq!(
            detect_language_from_extension("rake").unwrap(),
            SupportedLanguage::Ruby
        );
        assert_eq!(
            detect_language_from_extension("gemspec").unwrap(),
            SupportedLanguage::Ruby
        );
        assert_eq!(
            detect_language_from_extension("py").unwrap(),
            SupportedLanguage::Python
        );

        // Test error case
        assert!(detect_language_from_extension("unknown").is_err());
        assert!(detect_language_from_extension("xyz").is_err());
        // Test that the result matches the path-based version
        assert_eq!(
            detect_language_from_extension("rb").unwrap(),
            detect_language_from_path("test.rb").unwrap()
        );
        assert_eq!(
            detect_language_from_extension("py").unwrap(),
            detect_language_from_path("test.py").unwrap()
        );
    }

    #[test]
    fn test_detect_language_consistency() {
        // Ensure both methods return the same results
        let test_files = vec![
            ("test.rb", "rb"),
            ("script.rbw", "rbw"),
            ("Rakefile.rake", "rake"),
            ("gem.gemspec", "gemspec"),
        ];

        for (file_path, extension) in test_files {
            let path_result = detect_language_from_path(file_path);
            let ext_result = detect_language_from_extension(extension);

            assert_eq!(
                path_result.unwrap(),
                ext_result.unwrap(),
                "Results should match for file: {file_path} with extension: {extension}"
            );
        }
    }

    #[test]
    fn test_parser_creation() {
        let parser = GenericParser::new(SupportedLanguage::Ruby);
        assert_eq!(parser.language(), SupportedLanguage::Ruby);
    }

    #[test]
    fn test_parse_simple_ruby() -> Result<()> {
        let parser = GenericParser::default_for_language(SupportedLanguage::Ruby);
        let code = "class Test\n  def hello\n    puts 'world'\n  end\nend";
        let result = parser.parse(code, Some("test.rb"))?;

        assert_eq!(result.language, SupportedLanguage::Ruby);
        assert_eq!(result.file_path.as_deref(), Some("test.rb"));
        assert!(!result.ast.root().text().is_empty());

        Ok(())
    }

    #[test]
    fn test_parse_simple_python() -> Result<()> {
        let parser = GenericParser::default_for_language(SupportedLanguage::Python);
        let code = "class Test:\n  def hello(self):\n    print('world')\n";
        let result = parser.parse(code, Some("test.py"))?;

        assert_eq!(result.language, SupportedLanguage::Python);
        assert_eq!(result.file_path.as_deref(), Some("test.py"));
        assert!(!result.ast.root().text().is_empty());

        Ok(())
    }

    #[test]
    fn test_load_rules_from_yaml() {
        // Test that the function exists and can handle basic cases
        // The actual YAML format is complex, so we just test basic functionality
        let valid_yaml = r#"
id: test-rule
language: Ruby
rule:
  pattern: "puts $MSG"
"#;

        let result =
            std::panic::catch_unwind(|| load_rules_from_yaml(valid_yaml, SupportedLanguage::Ruby));
        assert!(result.is_ok(), "Function should not panic on input");

        // Test with completely invalid YAML - this should panic due to .expect()
        let invalid_yaml = "not: yaml: content: [unclosed";
        let result = std::panic::catch_unwind(|| {
            load_rules_from_yaml(invalid_yaml, SupportedLanguage::Ruby)
        });
        assert!(result.is_err(), "Function should panic on invalid YAML");
    }

    #[test]
    fn test_display_implementation() {
        assert_eq!(format!("{}", SupportedLanguage::Ruby), "ruby");
        assert_eq!(format!("{}", SupportedLanguage::Python), "python");
    }

    #[test]
    fn test_language_detection_edge_cases() {
        let result = detect_language_from_path("Gemfile");
        assert!(result.is_err());

        let result = detect_language_from_path("");
        assert!(result.is_err());

        let result = detect_language_from_path("test.");
        assert!(result.is_err());
    }

    #[test]
    fn test_default_for_language() {
        let parser1 = GenericParser::new(SupportedLanguage::Ruby);
        let parser2 = GenericParser::default_for_language(SupportedLanguage::Ruby);
        assert_eq!(parser1.language(), parser2.language());
    }

    #[test]
    fn test_parse_result_fields() -> Result<()> {
        let parser = GenericParser::new(SupportedLanguage::Ruby);
        let code = "puts 'hello'";
        let result = parser.parse(code, Some("test.rb"))?;

        assert_eq!(result.language, SupportedLanguage::Ruby);
        assert_eq!(result.file_path.as_deref(), Some("test.rb"));
        assert_eq!(result.ast.root().text(), code);

        // Test parse without file path
        let result_no_path = parser.parse(code, None)?;
        assert_eq!(result_no_path.file_path, None);

        Ok(())
    }

    #[test]
    fn test_parse_ast_error_handling() {
        let parser = GenericParser::new(SupportedLanguage::Ruby);

        // Test with empty code - this should be fine as empty AST is expected for empty input
        let result = parser.parse_ast("");
        assert!(result.is_ok());

        // Test normal parsing
        let valid_result = parser.parse_ast("class Test; end");
        assert!(valid_result.is_ok());
        assert!(!valid_result.unwrap().root().text().is_empty());

        // The error condition (empty AST with non-empty code) is very hard to trigger
        // with tree-sitter Ruby parser since it's robust
    }

    #[test]
    fn test_to_support_lang() {
        let ruby_lang = SupportedLanguage::Ruby.to_support_lang();
        assert_eq!(ruby_lang, SupportLang::Ruby);
        let python_lang = SupportedLanguage::Python.to_support_lang();
        assert_eq!(python_lang, SupportLang::Python);
    }

    #[test]
    fn test_parser_clone() {
        let parser = GenericParser::new(SupportedLanguage::Ruby);
        let cloned = parser.clone();
        assert_eq!(parser.language(), cloned.language());
    }

    #[test]
    fn test_parse_result_clone() {
        let parser = GenericParser::new(SupportedLanguage::Ruby);
        let code = "puts 'hello'";
        let result = parser.parse(code, Some("test.rb")).unwrap();

        let cloned = result.clone();
        assert_eq!(result.language, cloned.language);
        assert_eq!(result.file_path, cloned.file_path);
        assert_eq!(result.ast.root().text(), cloned.ast.root().text());
    }

    #[test]
    fn test_get_supported_extensions() {
        let extensions = get_supported_extensions();

        // NOTE: Make sure to also update the count in the macro when updating this test.
        assert_eq!(extensions.len(), 11);

        assert!(extensions.contains(&"rb"));
        assert!(extensions.contains(&"rbw"));
        assert!(extensions.contains(&"rake"));
        assert!(extensions.contains(&"gemspec"));
        assert!(extensions.contains(&"py"));
        assert!(extensions.contains(&"js"));
        assert!(extensions.contains(&"ts"));
        assert!(extensions.contains(&"kt"));
        assert!(extensions.contains(&"kts"));
        assert!(extensions.contains(&"cs"));
        assert!(extensions.contains(&"java"));
    }
}
