//! Generic definition structures for representing code definitions across languages
//!
//! This module provides generic data structures for representing definitions found in source code.
//! It is designed to be language-agnostic while allowing for language-specific extensions through
//! generics and trait implementations.

use std::collections::HashMap;

use crate::AnalysisResult;
use crate::fqn::Fqn;
use crate::rules::MatchInfo;

/// Generic Definition Information structure
///
/// This represents a definition found in source code with its metadata.
/// It is generic over both the definition type and the FQN type to allow
/// language-specific implementations while maintaining a common structure.
///
/// # Examples
///
/// ```rust
/// use parser_core::definitions::DefinitionInfo;
/// use parser_core::fqn::Fqn;
///
/// // For a language-specific definition type
/// #[derive(Debug, Clone, PartialEq)]
/// enum RubyDefinitionType {
///     Class,
///     Module,
///     Method,
/// }
///
/// // Ruby-specific FQN type
/// type RubyFqn = Fqn<String>;
///
/// // Ruby-specific definition with Ruby FQN
/// type RubyDefinitionInfo = DefinitionInfo<RubyDefinitionType, RubyFqn>;
/// ```
#[derive(Debug, Clone)]
pub struct DefinitionInfo<DefinitionType, FqnType = Fqn<String>> {
    /// The type of definition (class, method, etc.) - language-specific
    pub definition_type: DefinitionType,
    /// The name of the definition
    pub name: String,
    /// The fully qualified name of the definition (language-specific)
    pub fqn: Option<FqnType>,
    /// The match information from the rule engine
    pub match_info: MatchInfo,
}

impl<DefinitionType, FqnType> DefinitionInfo<DefinitionType, FqnType> {
    /// Create a new definition info with the specified parameters
    pub fn new(
        definition_type: DefinitionType,
        name: String,
        fqn: Option<FqnType>,
        match_info: MatchInfo,
    ) -> Self {
        Self {
            definition_type,
            name,
            fqn,
            match_info,
        }
    }
}

/// Trait for definition types that can be converted to string representations
/// This enables generic processing of definitions across different languages
pub trait DefinitionTypeInfo {
    /// Get the string representation of this definition type
    fn as_str(&self) -> &str;
}

/// Trait for common definition lookup operations
///
/// This trait provides common operations for working with collections of definitions,
/// allowing for easy filtering and searching of definitions by type or name.
pub trait DefinitionLookup<FqnType, DefinitionType, ImportType> {
    /// Get definitions of a specific type
    fn definitions_of_type(
        &self,
        def_type: &DefinitionType,
    ) -> Vec<&DefinitionInfo<DefinitionType, FqnType>>;

    /// Get definitions by name (case-sensitive)
    fn definitions_by_name(&self, name: &str) -> Vec<&DefinitionInfo<DefinitionType, FqnType>>;

    /// Count definitions by type
    fn count_definitions_by_type(&self) -> HashMap<DefinitionType, usize>
    where
        DefinitionType: Clone + std::hash::Hash + Eq;

    /// Get all definition names
    fn definition_names(&self) -> Vec<&str>;

    /// Get all definitions with FQNs
    fn definitions_with_fqn(&self) -> Vec<&DefinitionInfo<DefinitionType, FqnType>>;
}

impl<FqnType, DefinitionType, ImportType> DefinitionLookup<FqnType, DefinitionType, ImportType>
    for AnalysisResult<FqnType, DefinitionType, ImportType>
where
    DefinitionType: PartialEq + Clone + std::hash::Hash + Eq,
{
    fn definitions_of_type(
        &self,
        def_type: &DefinitionType,
    ) -> Vec<&DefinitionInfo<DefinitionType, FqnType>> {
        self.definitions
            .iter()
            .filter(|def| &def.definition_type == def_type)
            .collect()
    }

    fn definitions_by_name(&self, name: &str) -> Vec<&DefinitionInfo<DefinitionType, FqnType>> {
        self.definitions
            .iter()
            .filter(|def| def.name == name)
            .collect()
    }

    fn count_definitions_by_type(&self) -> HashMap<DefinitionType, usize> {
        let mut counts = HashMap::new();
        for def in &self.definitions {
            *counts.entry(def.definition_type.clone()).or_insert(0) += 1;
        }
        counts
    }

    fn definition_names(&self) -> Vec<&str> {
        self.definitions
            .iter()
            .map(|def| def.name.as_str())
            .collect()
    }

    fn definitions_with_fqn(&self) -> Vec<&DefinitionInfo<DefinitionType, FqnType>> {
        self.definitions
            .iter()
            .filter(|def| def.fqn.is_some())
            .collect()
    }
}
