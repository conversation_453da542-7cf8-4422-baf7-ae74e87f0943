//! Shared types for the Ruby parser
//!
//! This module defines the type hierarchy for Ruby language constructs that can appear
//! in Fully Qualified Names (FQNs).
//!
//! ## Type Hierarchy
//!
//! - `RubyFqnPartType`: Represents **any symbol** that can appear in an FQN path, including
//!   both definitions and contextual elements like constants, receivers, and blocks.
//!
//! - `RubyDefinitionType`: Represents **callable/definable** constructs that create new scopes
//!   or can be invoked. These are the primary targets for code analysis and indexing.

use crate::definitions::DefinitionTypeInfo;
use serde::{Deserialize, Serialize};

/// Macro to define the core Ruby definition types that can be called or invoked.
/// These represent the fundamental constructs that create new scopes or can be executed.
macro_rules! define_ruby_definition_types {
    ($(($variant:ident, $str_repr:literal, $meta_var:expr)),* $(,)?) => {
        /// Represents a **callable/definable** Ruby construct.
        ///
        /// These are the primary targets for code analysis because they:
        /// - Create new scopes (classes, modules)
        /// - Can be invoked/called (methods, lambdas, procs)
        /// - Represent reusable code units
        ///
        /// This is a **subset** of `RubyFqnPartType` - every definition type
        /// can appear in an FQN, but not every FQN part is a definition.
        #[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
        pub enum RubyDefinitionType {
            $(
                #[doc = concat!("A Ruby ", $str_repr, " definition")]
                $variant,
            )*
        }

        impl DefinitionTypeInfo for RubyDefinitionType {
            fn as_str(&self) -> &str {
                match self {
                    $(RubyDefinitionType::$variant => $str_repr,)*
                }
            }
        }

        impl RubyDefinitionType {
            /// Get the meta variable name for simple definition types.
            ///
            /// Returns `None` for complex types (Lambda, Proc) that require
            /// special handling to determine their capture variable.
            pub fn as_meta_var(&self) -> Option<&'static str> {
                match self {
                    $(RubyDefinitionType::$variant => $meta_var,)*
                }
            }
        }
    };
}

/// Macro to define all possible FQN part types, including both definitions and contextual elements.
macro_rules! define_ruby_fqn_part_types {
    (
        // Definition types that map directly from RubyDefinitionType
        definitions: [$(($def_variant:ident, $def_str:literal)),* $(,)?],
        // Additional contextual types that can appear in FQNs but aren't definitions
        contextual: [$(($ctx_variant:ident, $ctx_str:literal)),* $(,)?]
    ) => {
        /// Represents **any symbol** that can be part of a Ruby FQN path.
        ///
        /// This includes:
        /// - **Definition types**: Callable/definable constructs (classes, methods, etc.)
        /// - **Contextual types**: Supporting elements that provide context in FQNs
        ///   - `Constant`: Named constants that aren't callable
        ///   - `Receiver`: Objects that receive method calls (e.g., `user` in `user.method`)
        ///   - `Block`: Anonymous code blocks
        ///   - `Unknown`: Fallback for unrecognized constructs
        ///
        /// This is a **superset** of `RubyDefinitionType` - it includes all definition
        /// types plus additional contextual elements needed for complete FQN representation.
        #[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
        pub enum RubyFqnPartType {
            // Definition types (these map 1:1 with RubyDefinitionType)
            $(
                #[doc = concat!("A Ruby ", $def_str, " (definition type)")]
                $def_variant,
            )*
            // Contextual types (these provide additional FQN context)
            $(
                #[doc = concat!("A Ruby ", $ctx_str, " (contextual type)")]
                $ctx_variant,
            )*
        }

        impl RubyFqnPartType {
            pub fn as_str(&self) -> &'static str {
                match self {
                    $(RubyFqnPartType::$def_variant => $def_str,)*
                    $(RubyFqnPartType::$ctx_variant => $ctx_str,)*
                }
            }

            /// Check if this FQN part type represents a callable/definable construct.
            pub fn is_definition(&self) -> bool {
                matches!(self, $(RubyFqnPartType::$def_variant)|*)
            }
        }

        // Automatic conversion from definition types to FQN part types
        impl From<RubyDefinitionType> for RubyFqnPartType {
            fn from(def_type: RubyDefinitionType) -> Self {
                match def_type {
                    $(RubyDefinitionType::$def_variant => RubyFqnPartType::$def_variant,)*
                }
            }
        }

        // Fallible conversion from FQN part types to definition types
        impl TryFrom<RubyFqnPartType> for RubyDefinitionType {
            type Error = ();

            fn try_from(part_type: RubyFqnPartType) -> Result<Self, Self::Error> {
                match part_type {
                    $(RubyFqnPartType::$def_variant => Ok(RubyDefinitionType::$def_variant),)*
                    _ => Err(()), // Contextual types cannot be converted to definition types
                }
            }
        }
    };
}

/// Type-safe constants for capture variable names used in Ruby rule definitions
/// These can be found in the yaml rules for the ruby parser under
/// `gitlab-code-parser/crates/parser-core/src/ruby/rules/definitions.yaml`
pub mod definition_meta_vars {
    pub const CLASS_DEF_NAME: &str = "CLASS_DEF_NAME";
    pub const MODULE_DEF_NAME: &str = "MODULE_DEF_NAME";
    pub const METHOD_DEF_NAME: &str = "METHOD_DEF_NAME";
    pub const SINGLETON_METHOD_DEF_NAME: &str = "SINGLETON_METHOD_DEF_NAME";
    pub const LAMBDA_DEF: &str = "LAMBDA_DEF";
    pub const LAMBDA_CONSTANT_NAME: &str = "LAMBDA_CONSTANT_NAME";
    pub const LAMBDA_VARIABLE_NAME: &str = "LAMBDA_VARIABLE_NAME";
    pub const LAMBDA_INSTANCE_VAR: &str = "LAMBDA_INSTANCE_VAR";
    pub const LAMBDA_CLASS_VAR: &str = "LAMBDA_CLASS_VAR";
    pub const PROC_DEF: &str = "PROC_DEF";
    pub const PROC_ASSIGNMENT: &str = "PROC_ASSIGNMENT";
    pub const PROC_CONSTANT_NAME: &str = "PROC_CONSTANT_NAME";
    pub const PROC_VARIABLE_NAME: &str = "PROC_VARIABLE_NAME";
    pub const PROC_INSTANCE_VAR: &str = "PROC_INSTANCE_VAR";
    pub const PROC_CLASS_VAR: &str = "PROC_CLASS_VAR";
}

/// Ruby AST node type constants from tree-sitter-ruby
pub mod node_types {
    pub const CLASS: &str = "class";
    pub const MODULE: &str = "module";
    pub const METHOD: &str = "method";
    pub const SINGLETON_METHOD: &str = "singleton_method";
    pub const ASSIGNMENT: &str = "assignment";
    pub const CALL: &str = "call";
    pub const DO_BLOCK: &str = "do_block";
    pub const BLOCK: &str = "block";
    pub const CONSTANT: &str = "constant";
    pub const IDENTIFIER: &str = "identifier";
    pub const INSTANCE_VARIABLE: &str = "instance_variable";
    pub const CLASS_VARIABLE: &str = "class_variable";
    pub const SELF: &str = "self";
    pub const RECEIVER: &str = "receiver";
}

// Define the core definition types with their string representations and meta variables
define_ruby_definition_types! {
    (Class, "Class", Some(definition_meta_vars::CLASS_DEF_NAME)),
    (Module, "Module", Some(definition_meta_vars::MODULE_DEF_NAME)),
    (Method, "Method", Some(definition_meta_vars::METHOD_DEF_NAME)),
    (SingletonMethod, "SingletonMethod", Some(definition_meta_vars::SINGLETON_METHOD_DEF_NAME)),
    (Lambda, "Lambda", None), // Complex type - requires special handling
    (Proc, "Proc", None),     // Complex type - requires special handling
}

// Define all FQN part types, including both definitions and contextual elements
define_ruby_fqn_part_types! {
    definitions: [
        (Class, "Class"),
        (Module, "Module"),
        (Method, "Method"),
        (SingletonMethod, "SingletonMethod"),
        (Lambda, "Lambda"),
        (Proc, "Proc"),
    ],
    contextual: [
        (Constant, "Constant"),     // Named constants (MY_CONSTANT = "value")
        (Receiver, "Receiver"),     // Method call receivers (user.method -> "user")
        (Block, "Block"),           // Anonymous blocks ({ |x| x + 1 })
        (Unknown, "Unknown"),       // Fallback for unrecognized constructs
    ]
}
