use crate::parser::{SupportedLanguage, load_rules_from_yaml};
use ast_grep_config::RuleConfig;
use ast_grep_language::SupportLang;
use once_cell::sync::Lazy;
use rustc_hash::FxHashMap;

pub const DEFINITIONS_YAML: &str = include_str!("./rules/definitions.yaml");

pub static RUBY_RULES: Lazy<String> = Lazy::new(|| DEFINITIONS_YAML.to_string());

pub static RULES_CONFIG: Lazy<Vec<RuleConfig<SupportLang>>> =
    Lazy::new(|| load_rules_from_yaml(&RUBY_RULES, SupportedLanguage::Ruby));

#[derive(Debug, PartialEq, Eq, Clone)]
pub enum RubyMatchKind {
    Definition,
    Other(String),
}

pub static RULE_ID_KIND_MAP: Lazy<FxHashMap<&'static str, RubyMatchKind>> = Lazy::new(|| {
    let mut m = FxHashMap::default();
    m.insert("ruby-definitions", RubyMatchKind::Definition);
    m
});

impl RubyMatchKind {
    pub fn from_rule_id(rule_id: &str) -> Self {
        RULE_ID_KIND_MAP
            .get(rule_id)
            .cloned()
            .unwrap_or_else(|| RubyMatchKind::Other(rule_id.to_string()))
    }
}

// validation that all rule IDs are captured correctly
#[cfg(test)]
mod compile_time_tests {
    use super::*;

    #[test]
    fn test_all_rule_ids_are_mapped() {
        // Force lazy initialization
        let rules = &*RULES_CONFIG;
        let kind_map = &*RULE_ID_KIND_MAP;

        for rule in rules {
            let rule_id = &rule.id;
            assert!(
                kind_map.contains_key(rule_id.as_str()),
                "Rule ID '{rule_id}' is not mapped in RULE_ID_KIND_MAP"
            );
        }
    }

    #[test]
    fn test_required_patterns_are_present() {
        use crate::ruby::types::definition_meta_vars::*;

        // Force lazy initialization
        let _rules = &*RULES_CONFIG;

        // Verify definitions YAML contains required patterns for basic definitions
        assert!(
            DEFINITIONS_YAML.contains(&format!("${CLASS_DEF_NAME}")),
            "CLASS_DEF_NAME pattern missing"
        );
        assert!(
            DEFINITIONS_YAML.contains(&format!("${MODULE_DEF_NAME}")),
            "MODULE_DEF_NAME pattern missing"
        );
        assert!(
            DEFINITIONS_YAML.contains(&format!("${METHOD_DEF_NAME}")),
            "METHOD_DEF_NAME pattern missing"
        );
        assert!(
            DEFINITIONS_YAML.contains(&format!("${SINGLETON_METHOD_DEF_NAME}")),
            "SINGLETON_METHOD_DEF_NAME pattern missing"
        );

        assert!(
            DEFINITIONS_YAML.contains(&format!("${LAMBDA_DEF}")),
            "LAMBDA_DEF pattern missing"
        );
        assert!(
            DEFINITIONS_YAML.contains(&format!("${PROC_DEF}")),
            "PROC_DEF pattern missing"
        );
    }

    #[test]
    fn test_rule_loading_works() {
        // Force lazy initialization and verify rules load successfully
        let rules = &*RULES_CONFIG;
        assert!(!rules.is_empty(), "No rules loaded");

        // Verify we have the expected rule
        let rule_ids: Vec<String> = rules.iter().map(|r| r.id.clone()).collect();
        assert!(rule_ids.contains(&"ruby-definitions".to_string()));
    }

    #[test]
    fn test_lazy_loading() {
        // Rules are loaded only when first accessed
        println!("Accessing RULES_CONFIG for the first time...");
        let rules = &*RULES_CONFIG;
        println!("Loaded {} rules", rules.len());

        for rule in rules {
            println!("  - Rule ID: {}", rule.id);
            println!("    Language: {:?}", rule.language);
            let kind = RubyMatchKind::from_rule_id(&rule.id);
            println!("    Kind: {kind:?}");
        }

        println!("Definitions YAML length: {} chars", DEFINITIONS_YAML.len());

        // Demonstrate that subsequent accesses use cached data
        println!("Accessing RULES_CONFIG again (should be instant)...");
        let rules_again = &*RULES_CONFIG;
        assert_eq!(rules.len(), rules_again.len());
        println!("Still {} rules (cached)", rules_again.len());
    }
}
