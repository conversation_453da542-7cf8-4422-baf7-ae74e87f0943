use crate::fqn::{FQ<PERSON>art, Fqn};
use crate::ruby::types::{RubyFqnPartType, node_types};
use crate::utils::{Range, node_to_range};
use ast_grep_core::tree_sitter::StrDoc;
use ast_grep_core::{AstGrep, Node};
use ast_grep_language::SupportLang;
use smallvec::{SmallVec, smallvec};
use std::collections::HashMap;
use std::sync::Arc;

/// Ruby-specific FQN part with metadata
pub type RubyFqnPart = FQNPart<RubyFqnPartType>;

/// Ruby-specific metadata for FQN parts
/// Contains information about the AST node that created this FQN part
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct RubyFqn {
    /// Note: we use a SmallVec here because FQN parts shouldn't be too deep
    /// SmallVec will automatically spill over to the heap if it exceeds 8 elements
    /// https://crates.io/crates/smallvec
    pub parts: Arc<SmallVec<[RubyFqnPart; 8]>>,
}

impl RubyFqn {
    pub fn new(parts: SmallVec<[RubyFqnPart; 8]>) -> Self {
        Self {
            parts: Arc::new(parts),
        }
    }

    pub fn len(&self) -> usize {
        self.parts.len()
    }

    pub fn is_empty(&self) -> bool {
        self.parts.is_empty()
    }
}

/// Use SmallVec for scope stack since Ruby nesting is typically shallow (usually < 8 levels)
type ScopeStack = SmallVec<[RubyFqnPart; 8]>;

/// Stack entry for iterative traversal
struct StackEntry<'a> {
    node: Node<'a, StrDoc<SupportLang>>,
    scope_depth: usize,
    is_exiting: bool, // is true when we're exiting the node
}

/// Index a node by its range for efficient lookup
fn index_node<'a>(node: &Node<'a, StrDoc<SupportLang>>, node_index_map: &mut NodeIndexMap<'a>) {
    let node_range = node_to_range(node);
    node_index_map.insert(node_range, node.clone());
}

/// Store an FQN mapping for a node
fn store_fqn_mapping<'a>(
    name_node: Node<'a, StrDoc<SupportLang>>,
    fqn_parts: SmallVec<[RubyFqnPart; 8]>,
    node_fqn_map: &mut RubyNodeFqnMap<'a>,
) {
    let name_range = node_to_range(&name_node);
    node_fqn_map.insert(name_range, (name_node, Arc::new(fqn_parts)));
}

/// Create an FQN part with the entire definition range
fn create_fqn_part(
    fqn_part_type: RubyFqnPartType,
    name: String,
    definition_node: &Node<StrDoc<SupportLang>>,
) -> RubyFqnPart {
    RubyFqnPart::new(fqn_part_type, name, node_to_range(definition_node))
}

/// Result of processing a node
struct FQNResult {
    creates_scope: bool,
    new_scope_part: Option<RubyFqnPart>,
}

impl FQNResult {
    fn no_scope() -> Self {
        Self {
            creates_scope: false,
            new_scope_part: None,
        }
    }

    fn with_scope(scope_part: RubyFqnPart) -> Self {
        Self {
            creates_scope: true,
            new_scope_part: Some(scope_part),
        }
    }
}

fn compute_fqns_and_index_iterative<'a>(
    root: Node<'a, StrDoc<SupportLang>>,
    node_fqn_map: &mut RubyNodeFqnMap<'a>,
    node_index_map: &mut NodeIndexMap<'a>,
) {
    let mut current_scope: ScopeStack = smallvec![];

    // Use SmallVec for the traversal stack - most Ruby files don't have deep nesting
    let mut stack: SmallVec<[StackEntry; 32]> = smallvec![StackEntry {
        node: root,
        scope_depth: 0,
        is_exiting: false,
    }];

    while let Some(entry) = stack.pop() {
        let StackEntry {
            node,
            scope_depth,
            is_exiting,
        } = entry;

        if is_exiting {
            // Restore scope when exiting a node that created a new scope
            current_scope.truncate(scope_depth);
            continue;
        }

        // Index all nodes by range for efficient lookup
        index_node(&node, node_index_map);

        // Process the node and determine if it creates a new scope
        let result = process_node(&node, &current_scope, node_fqn_map);

        // If this node creates a new scope, push an exit marker first
        if result.creates_scope {
            stack.push(StackEntry {
                node: node.clone(),
                scope_depth: current_scope.len(), // Store current depth for restoration
                is_exiting: true,
            });
        }

        // Collect children and push to stack in reverse order for correct left-to-right traversal
        let children: Vec<_> = node.children().collect();

        // Helps to avoid reallocations
        stack.reserve(children.len());

        // Push children in reverse order
        for child in children.into_iter().rev() {
            stack.push(StackEntry {
                node: child,
                scope_depth: current_scope.len(),
                is_exiting: false,
            });
        }

        // Update current scope if this node creates a new scope
        if result.creates_scope {
            if let Some(scope_part) = result.new_scope_part {
                current_scope.push(scope_part);
            }
        }
    }
}

/// Process a single node and determine its FQN contribution
fn process_node<'a>(
    node: &Node<'a, StrDoc<SupportLang>>,
    current_scope: &ScopeStack,
    node_fqn_map: &mut RubyNodeFqnMap<'a>,
) -> FQNResult {
    let node_kind = node.kind();
    let kind_str = node_kind.as_ref(); // Cache the string reference

    match kind_str {
        node_types::CLASS
        | node_types::MODULE
        | node_types::METHOD
        | node_types::SINGLETON_METHOD => {
            process_class_module_method_node(node, current_scope, node_fqn_map)
        }
        node_types::ASSIGNMENT => process_assignment_node(node, current_scope, node_fqn_map),
        node_types::CALL => process_call_node(node, current_scope, node_fqn_map),
        node_types::DO_BLOCK | node_types::BLOCK => {
            process_block_node(node, current_scope, node_fqn_map)
        }
        _ => FQNResult::no_scope(),
    }
}

fn process_class_module_method_node<'a>(
    node: &Node<'a, StrDoc<SupportLang>>,
    current_scope: &ScopeStack,
    node_fqn_map: &mut RubyNodeFqnMap<'a>,
) -> FQNResult {
    let Some(name_node) = node.field("name") else {
        return FQNResult::no_scope();
    };

    let node_kind = node.kind();
    let name = name_node.text().into_owned();
    let mut fqn_parts = current_scope.clone();

    let fqn_part_type = match node_kind.as_ref() {
        node_types::CLASS => RubyFqnPartType::Class,
        node_types::MODULE => RubyFqnPartType::Module,
        node_types::METHOD => RubyFqnPartType::Method,
        node_types::SINGLETON_METHOD => RubyFqnPartType::SingletonMethod,
        _ => RubyFqnPartType::Unknown,
    };

    if node_kind.as_ref() == node_types::SINGLETON_METHOD {
        if let Some(object_node) = node.field("object") {
            if object_node.kind() != node_types::SELF {
                let receiver_part = create_fqn_part(
                    RubyFqnPartType::Receiver,
                    object_node.text().into_owned(),
                    &object_node,
                );
                fqn_parts.push(receiver_part);
            }
        }
    }

    let fqn_part = create_fqn_part(fqn_part_type, name.clone(), node);

    fqn_parts.push(fqn_part.clone());
    store_fqn_mapping(name_node, fqn_parts, node_fqn_map);

    // classes, modules, methods, and singleton methods create a new scope
    let creates_scope = matches!(
        node_kind.as_ref(),
        node_types::CLASS | node_types::MODULE | node_types::METHOD | node_types::SINGLETON_METHOD
    );

    if creates_scope {
        FQNResult::with_scope(fqn_part)
    } else {
        FQNResult::no_scope()
    }
}

fn process_assignment_node<'a>(
    node: &Node<'a, StrDoc<SupportLang>>,
    current_scope: &ScopeStack,
    node_fqn_map: &mut RubyNodeFqnMap<'a>,
) -> FQNResult {
    // Cache field lookups
    let Some(left_node) = node.field("left") else {
        return FQNResult::no_scope();
    };
    let Some(right_node) = node.field("right") else {
        return FQNResult::no_scope();
    };

    let left_kind = left_node.kind();
    let left_kind_str = left_kind.as_ref(); // Cache string reference

    match left_kind_str {
        node_types::CONSTANT => {
            process_constant_assignment(&left_node, &right_node, current_scope, node_fqn_map)
        }
        node_types::IDENTIFIER | node_types::INSTANCE_VARIABLE | node_types::CLASS_VARIABLE => {
            process_variable_assignment(&left_node, &right_node, current_scope, node_fqn_map)
        }
        _ => FQNResult::no_scope(),
    }
}

fn process_constant_assignment<'a>(
    left_node: &Node<'a, StrDoc<SupportLang>>,
    right_node: &Node<StrDoc<SupportLang>>,
    current_scope: &ScopeStack,
    node_fqn_map: &mut RubyNodeFqnMap<'a>,
) -> FQNResult {
    let name = left_node.text().into_owned();
    let mut fqn_parts = current_scope.clone();

    // Determine the type based on the right-hand side
    let fqn_part_type = if is_lambda_assignment(right_node) {
        RubyFqnPartType::Lambda
    } else if is_proc_assignment(right_node) {
        RubyFqnPartType::Proc
    } else {
        RubyFqnPartType::Constant
    };

    let assignment_node = left_node.parent().expect("Assignment should have parent");
    let fqn_part = create_fqn_part(fqn_part_type, name, &assignment_node);

    fqn_parts.push(fqn_part);
    store_fqn_mapping(left_node.clone(), fqn_parts, node_fqn_map);

    FQNResult::no_scope()
}

fn process_variable_assignment<'a>(
    left_node: &Node<'a, StrDoc<SupportLang>>,
    right_node: &Node<StrDoc<SupportLang>>,
    current_scope: &ScopeStack,
    node_fqn_map: &mut RubyNodeFqnMap<'a>,
) -> FQNResult {
    // note: we only handle lambda and proc assignments for now
    if !is_lambda_assignment(right_node) && !is_proc_assignment(right_node) {
        return FQNResult::no_scope();
    }

    let name = left_node.text().into_owned();
    let mut fqn_parts = current_scope.clone();

    let fqn_part_type = if is_lambda_assignment(right_node) {
        RubyFqnPartType::Lambda
    } else {
        RubyFqnPartType::Proc
    };

    let assignment_node = left_node.parent().expect("Assignment should have parent");
    let fqn_part = create_fqn_part(fqn_part_type, name, &assignment_node);

    fqn_parts.push(fqn_part);
    store_fqn_mapping(left_node.clone(), fqn_parts, node_fqn_map);

    FQNResult::no_scope()
}

fn process_call_node<'a>(
    node: &Node<'a, StrDoc<SupportLang>>,
    current_scope: &ScopeStack,
    node_fqn_map: &mut RubyNodeFqnMap<'a>,
) -> FQNResult {
    let Some(method_node) = node.field("method") else {
        return FQNResult::no_scope();
    };

    // Handle standalone Proc.new calls
    if method_node.text() != "new" {
        return FQNResult::no_scope();
    }

    let Some(receiver) = node.field("receiver") else {
        return FQNResult::no_scope();
    };

    if receiver.kind().as_ref() != node_types::CONSTANT || receiver.text() != "Proc" {
        return FQNResult::no_scope();
    }

    // Check if this Proc.new is part of an assignment (skip if it is)
    let is_part_of_assignment = node
        .parent()
        .map(|parent| {
            parent.kind().as_ref() == node_types::ASSIGNMENT
                && parent.field("right").map(|right| right.node_id()) == Some(node.node_id())
        })
        .unwrap_or(false);

    if is_part_of_assignment {
        return FQNResult::no_scope();
    }

    // Create standalone Proc.new FQN part using the entire call node
    let mut fqn_parts = current_scope.clone();
    let fqn_part = create_fqn_part(RubyFqnPartType::Proc, "Proc.new".to_string(), node);

    fqn_parts.push(fqn_part);
    store_fqn_mapping(method_node, fqn_parts, node_fqn_map);

    FQNResult::no_scope()
}

fn process_block_node<'a>(
    node: &Node<'a, StrDoc<SupportLang>>,
    current_scope: &ScopeStack,
    node_fqn_map: &mut RubyNodeFqnMap<'a>,
) -> FQNResult {
    // Only handle blocks with parameters that aren't lambda blocks
    if node.field("parameters").is_none() || is_lambda_block(node) {
        return FQNResult::no_scope();
    }

    let mut fqn_parts = current_scope.clone();
    let fqn_part = create_fqn_part(RubyFqnPartType::Block, "block".to_string(), node);

    fqn_parts.push(fqn_part);
    store_fqn_mapping(node.clone(), fqn_parts, node_fqn_map);

    FQNResult::no_scope()
}

/// Ruby-specific helper functions for FQN operations
/// Returns the FQN as a string, joined by '::' (Ruby-specific)
pub fn ruby_fqn_to_string(fqn: &RubyFqn) -> String {
    if fqn.parts.is_empty() {
        return String::new();
    }

    if fqn.parts.len() == 1 {
        let part = &fqn.parts[0];
        if part.node_type == RubyFqnPartType::SingletonMethod {
            let mut result = String::with_capacity(part.node_name.len() + 1);
            result.push('.');
            result.push_str(&part.node_name);
            result
        } else {
            part.node_name.clone()
        }
    } else {
        let mut total_capacity = 0;
        let mut singleton_methods = 0;

        for part in fqn.parts.iter() {
            total_capacity += part.node_name.len();
            if part.node_type == RubyFqnPartType::SingletonMethod {
                singleton_methods += 1;
            }
        }

        total_capacity += (fqn.parts.len() - 1) * 2; // "::" separators
        total_capacity += singleton_methods; // "." prefixes

        let mut result = String::with_capacity(total_capacity);

        for (i, part) in fqn.parts.iter().enumerate() {
            if i > 0 {
                result.push_str("::");
            }

            // singleton methods are prefixed with '.' to distinguish from instance methods
            if part.node_type == RubyFqnPartType::SingletonMethod {
                result.push('.');
            }
            result.push_str(&part.node_name);
        }

        result
    }
}

/// Create an FQN from a Ruby-style string like "Module::Class::method"
pub fn fqn_from_ruby_string(fqn_str: &str) -> Fqn<String> {
    let parts = fqn_str.split("::").map(|s| s.to_string()).collect();
    Fqn::new(parts)
}

pub type NodeIndexMap<'a> = HashMap<Range, Node<'a, StrDoc<SupportLang>>>;
pub type RubyNodeFqnMap<'a> = HashMap<
    Range,
    (
        Node<'a, StrDoc<SupportLang>>,
        Arc<SmallVec<[RubyFqnPart; 8]>>,
    ),
>;

/// FQN indexing and computation functionality for Ruby
/// This handles the core logic for building FQN maps from AST traversal
/// Returns a tuple of (ruby_node_fqn_map, node_index_map) where:
/// - ruby_node_fqn_map: Maps byte ranges to (Node, FQN parts with metadata)
/// - node_index_map: Maps byte ranges to Nodes for efficient lookup
pub fn build_fqn_and_node_indices<'a>(
    ast: &'a AstGrep<StrDoc<SupportLang>>,
) -> (RubyNodeFqnMap<'a>, NodeIndexMap<'a>) {
    // Pre-allocate HashMaps with estimated capacity based on AST size
    let estimated_capacity = ast.root().children().count() * 3;
    let mut node_fqn_map = HashMap::with_capacity(estimated_capacity);
    let mut node_index_map = HashMap::with_capacity(estimated_capacity);

    compute_fqns_and_index_iterative(ast.root(), &mut node_fqn_map, &mut node_index_map);

    (node_fqn_map, node_index_map)
}

/// Utility function to find FQN for a node by looking up its name node in the FQN map
/// This combines the lookup logic that was previously split across multiple functions
pub fn find_fqn_for_node<'a>(range: Range, node_fqn_map: &RubyNodeFqnMap<'a>) -> Option<RubyFqn> {
    node_fqn_map
        .get(&range)
        .map(|(_name_node, fqn_parts_arc)| RubyFqn {
            parts: Arc::clone(fqn_parts_arc),
        })
}

pub fn find_ruby_fqn_for_node<'a>(
    range: Range,
    ruby_node_fqn_map: &RubyNodeFqnMap<'a>,
) -> Option<RubyFqn> {
    ruby_node_fqn_map
        .get(&range)
        .map(|(_name_node, fqn_parts_arc)| RubyFqn {
            parts: Arc::clone(fqn_parts_arc),
        })
}

fn is_lambda_assignment(node: &Node<StrDoc<SupportLang>>) -> bool {
    if node.kind() == node_types::CALL {
        if let Some(method_node) = node.field(node_types::METHOD) {
            method_node.text() == "lambda"
        } else {
            false
        }
    } else {
        false
    }
}

fn is_proc_assignment(node: &Node<StrDoc<SupportLang>>) -> bool {
    if node.kind() == node_types::CALL {
        if let Some(method_node) = node.field(node_types::METHOD) {
            // Handle lowercase "proc" kernel method
            if method_node.text() == "proc" && node.field(node_types::RECEIVER).is_none() {
                return true;
            }

            // Handle "Proc.new" class method
            if let Some(receiver) = node.field(node_types::RECEIVER) {
                return receiver.kind() == node_types::CONSTANT
                    && receiver.text() == "Proc"
                    && method_node.text() == "new";
            }
        }
    }
    false
}

fn is_lambda_block(node: &Node<StrDoc<SupportLang>>) -> bool {
    // check if this block's parent is a lambda call
    if let Some(parent) = node.parent() {
        if parent.kind() == node_types::CALL {
            if let Some(method_node) = parent.field(node_types::METHOD) {
                return method_node.text() == "lambda";
            }
        }
    }
    false
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::parser::{GenericParser, LanguageParser, SupportedLanguage};
    use crate::utils::Position;

    #[test]
    fn test_ruby_fqn_string_operations() {
        let fqn = fqn_from_ruby_string("AuthenticationService::CredentialsChecker::initialize");

        assert_eq!(fqn.len(), 3);
        assert_eq!(fqn.parts[0], "AuthenticationService");
        assert_eq!(fqn.parts[1], "CredentialsChecker");
        assert_eq!(fqn.parts[2], "initialize");
    }

    #[test]
    fn test_ruby_fqn_computation_with_sample_code() {
        let ruby_code = r#"
module AuthenticationService
  class CredentialsChecker
    def initialize(user, password)
      @user = user
      @password = password
    end

    def self.authenticate(email, password)
      # Authentication logic
    end
  end

  module Utilities
    class Audit
      def self.log_event(message)
        puts message
      end
    end
  end
end
"#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Ruby);
        let parse_result = parser.parse(ruby_code, Some("test.rb")).unwrap();
        let (ruby_node_fqn_map, _node_index_map) = build_fqn_and_node_indices(&parse_result.ast);

        // Verify we have FQN mappings with metadata
        assert!(!ruby_node_fqn_map.is_empty(), "Should have FQN mappings");

        // Find some expected FQNs and check metadata
        let mut found_fqns = Vec::new();
        for (_node, fqn_parts) in ruby_node_fqn_map.values() {
            let ruby_fqn = RubyFqn::new((**fqn_parts).clone());
            let fqn_string = ruby_fqn_to_string(&ruby_fqn);
            found_fqns.push(fqn_string.clone());

            // Check that each part has valid node type information
            for part in ruby_fqn.parts.iter() {
                assert!(
                    part.node_type != RubyFqnPartType::Unknown,
                    "Each FQN part should have a known node type"
                );
                assert!(
                    !part.node_name.is_empty(),
                    "Each FQN part should have a node name"
                );
            }
        }

        // Check for expected FQNs
        assert!(
            found_fqns.iter().any(|fqn| fqn == "AuthenticationService"),
            "Should find AuthenticationService"
        );
        assert!(
            found_fqns
                .iter()
                .any(|fqn| fqn == "AuthenticationService::CredentialsChecker"),
            "Should find AuthenticationService::CredentialsChecker"
        );
        assert!(
            found_fqns
                .iter()
                .any(|fqn| fqn == "AuthenticationService::CredentialsChecker::initialize"),
            "Should find AuthenticationService::CredentialsChecker::initialize"
        );

        println!("Found Ruby FQNs with metadata: {found_fqns:?}");
    }

    #[test]
    fn test_ruby_fqn_metadata_types() {
        // Test that the metadata types are properly defined and work
        let metadata = RubyFqnPart::new(
            RubyFqnPartType::Class,
            "MyClass".to_string(),
            Range::new(Position::new(10, 20), Position::new(10, 20), (10, 20)),
        );
        assert_eq!(metadata.node_type, RubyFqnPartType::Class);
        assert_eq!(
            metadata.range,
            Range::new(Position::new(10, 20), Position::new(10, 20), (10, 20))
        );

        let method_metadata = RubyFqnPart::new(
            RubyFqnPartType::Method,
            "MyMethod".to_string(),
            Range::new(Position::new(30, 40), Position::new(30, 40), (30, 40)),
        );
        assert_eq!(method_metadata.node_type, RubyFqnPartType::Method);

        // Test RubyFqnPart creation
        let part = RubyFqnPart::new(
            RubyFqnPartType::Class,
            "MyClass".to_string(),
            Range::new(Position::new(10, 20), Position::new(10, 20), (10, 20)),
        );
        assert_eq!(part.node_type, RubyFqnPartType::Class);
        assert_eq!(part.node_name, "MyClass");
    }

    #[test]
    fn test_ruby_fqn_with_metadata() {
        // Test creating a Ruby FQN
        let parts = vec![
            RubyFqnPart::new(
                RubyFqnPartType::Class,
                "MyClass".to_string(),
                Range::new(Position::new(10, 20), Position::new(10, 20), (10, 20)),
            ),
            RubyFqnPart::new(
                RubyFqnPartType::Method,
                "my_method".to_string(),
                Range::new(Position::new(30, 40), Position::new(30, 40), (30, 40)),
            ),
        ];

        let ruby_fqn = RubyFqn::new(parts.into_iter().collect());
        assert_eq!(ruby_fqn.len(), 2);
        assert_eq!(ruby_fqn_to_string(&ruby_fqn), "MyClass::my_method");

        // Test accessing node types
        assert_eq!(ruby_fqn.parts[0].node_type, RubyFqnPartType::Class);
        assert_eq!(ruby_fqn.parts[1].node_type, RubyFqnPartType::Method);
    }

    #[test]
    fn test_enhanced_fqn_maps_with_metadata() {
        let ruby_code = r#"
module TestModule
  class TestClass
    def test_method
    end
  end
end
"#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Ruby);
        let parse_result = parser.parse(ruby_code, Some("test.rb")).unwrap();

        // Test enhanced version
        let (ruby_node_fqn_map, _) = build_fqn_and_node_indices(&parse_result.ast);

        // Should find FQN parts
        assert!(
            !ruby_node_fqn_map.is_empty(),
            "Enhanced version should find FQN parts"
        );

        // Enhanced version should have valid FQN part type information
        for (_node, fqn_parts) in ruby_node_fqn_map.values() {
            for part in fqn_parts.iter() {
                assert!(
                    part.node_type != RubyFqnPartType::Unknown,
                    "Enhanced FQN parts should have a known node type"
                );
                assert!(
                    !part.node_name.is_empty(),
                    "Enhanced FQN parts should have node name"
                );
            }
        }

        println!("Enhanced FQN map has {} entries", ruby_node_fqn_map.len());

        // Verify we can convert to string representations
        for (_node, fqn_parts) in ruby_node_fqn_map.values() {
            let ruby_fqn = RubyFqn::new((**fqn_parts).clone());
            let fqn_string = ruby_fqn_to_string(&ruby_fqn);
            assert!(!fqn_string.is_empty(), "FQN string should not be empty");
            println!("Found FQN with metadata: {fqn_string}");
        }
    }

    #[test]
    fn test_fqn_captures_whole_definition_range() {
        let ruby_code = r#"
module TestModule
  class TestClass
    def test_method(param)
      puts "hello"
    end
    
    def self.class_method
      puts "class method"
    end
    
    def receiver_obj.receiver_method
      puts "receiver method"
    end
  end
  
  # Constant assignments
  MY_CONSTANT = "regular constant"
  MY_PROC = proc do |x|
    x * 2
  end
  MY_LAMBDA = lambda do |x|
    x * 3
  end
  
  # Variable assignments (lambda/proc)
  lambda_var = lambda do |x|
    x * 2
  end
  
  proc_var = proc do |x|
    x * 4
  end
  
  # Instance variable assignments
  @lambda_instance_var = lambda do |x|
    x * 5
  end
  
  @proc_instance_var = proc do |x|
    x * 6
  end
  
  # Class variable assignments
  @@lambda_class_var = lambda do |x|
    x * 7
  end
  
  @@proc_class_var = proc do |x|
    x * 8
  end
  
  # Standalone Proc.new call
  Proc.new do |x|
    x * 9
  end
  
  # Block with parameters
  [1, 2, 3].each do |item|
    puts item
  end
end
"#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Ruby);
        let parse_result = parser.parse(ruby_code, Some("test.rb")).unwrap();
        let (ruby_node_fqn_map, _) = build_fqn_and_node_indices(&parse_result.ast);

        // Find all FQN entries and check their ranges
        let mut found_entries = Vec::new();
        for (range, (_node, fqn_parts)) in &ruby_node_fqn_map {
            let ruby_fqn = RubyFqn::new((**fqn_parts).clone());
            let fqn_string = ruby_fqn_to_string(&ruby_fqn);

            // Get the last part (the actual definition)
            if let Some(last_part) = ruby_fqn.parts.last() {
                found_entries.push((fqn_string.clone(), last_part.range, range));
            }
        }

        // Test that the ranges are correct (span multiple lines)
        let module_entry = found_entries
            .iter()
            .find(|(fqn, _, _)| fqn == "TestModule")
            .expect("Should find TestModule");

        assert!(
            module_entry.1.line_span() > 1,
            "Module range should span multiple lines, got {} lines. Range: {:?}",
            module_entry.1.line_span(),
            module_entry.1
        );

        let class_entry = found_entries
            .iter()
            .find(|(fqn, _, _)| fqn == "TestModule::TestClass")
            .expect("Should find TestClass");

        assert!(
            class_entry.1.line_span() > 1,
            "Class range should span multiple lines, got {} lines. Range: {:?}",
            class_entry.1.line_span(),
            class_entry.1
        );

        let method_entry = found_entries
            .iter()
            .find(|(fqn, _, _)| fqn == "TestModule::TestClass::test_method")
            .expect("Should find test_method");

        assert!(
            method_entry.1.line_span() > 1,
            "Method range should span multiple lines, got {} lines. Range: {:?}",
            method_entry.1.line_span(),
            method_entry.1
        );

        let singleton_method_entry = found_entries
            .iter()
            .find(|(fqn, _, _)| fqn == "TestModule::TestClass::.class_method")
            .expect("Should find class_method");

        assert!(
            singleton_method_entry.1.line_span() > 1,
            "Singleton method range should span multiple lines, got {} lines. Range: {:?}",
            singleton_method_entry.1.line_span(),
            singleton_method_entry.1
        );

        let receiver_method_entry = found_entries
            .iter()
            .find(|(fqn, _, _)| fqn == "TestModule::TestClass::receiver_obj::.receiver_method")
            .expect("Should find receiver_method");

        assert!(
            receiver_method_entry.1.line_span() > 1,
            "Receiver method range should span multiple lines, got {} lines. Range: {:?}",
            receiver_method_entry.1.line_span(),
            receiver_method_entry.1
        );

        let constant_entry = found_entries
            .iter()
            .find(|(fqn, _, _)| fqn == "TestModule::MY_CONSTANT")
            .expect("Should find MY_CONSTANT");

        assert!(
            constant_entry.1.line_span() >= 1,
            "Constant range should span at least one line, got {} lines. Range: {:?}",
            constant_entry.1.line_span(),
            constant_entry.1
        );

        let proc_entry = found_entries
            .iter()
            .find(|(fqn, _, _)| fqn == "TestModule::MY_PROC")
            .expect("Should find MY_PROC");

        assert!(
            proc_entry.1.line_span() > 1,
            "Proc constant range should span multiple lines, got {} lines. Range: {:?}",
            proc_entry.1.line_span(),
            proc_entry.1
        );

        let lambda_constant_entry = found_entries
            .iter()
            .find(|(fqn, _, _)| fqn == "TestModule::MY_LAMBDA")
            .expect("Should find MY_LAMBDA");

        assert!(
            lambda_constant_entry.1.line_span() > 1,
            "Lambda constant range should span multiple lines, got {} lines. Range: {:?}",
            lambda_constant_entry.1.line_span(),
            lambda_constant_entry.1
        );

        let lambda_entry = found_entries
            .iter()
            .find(|(fqn, _, _)| fqn == "TestModule::lambda_var")
            .expect("Should find lambda_var");

        assert!(
            lambda_entry.1.line_span() > 1,
            "Lambda variable range should span multiple lines, got {} lines. Range: {:?}",
            lambda_entry.1.line_span(),
            lambda_entry.1
        );

        let proc_var_entry = found_entries
            .iter()
            .find(|(fqn, _, _)| fqn == "TestModule::proc_var")
            .expect("Should find proc_var");

        assert!(
            proc_var_entry.1.line_span() > 1,
            "Proc variable range should span multiple lines, got {} lines. Range: {:?}",
            proc_var_entry.1.line_span(),
            proc_var_entry.1
        );

        let lambda_instance_var_entry = found_entries
            .iter()
            .find(|(fqn, _, _)| fqn == "TestModule::@lambda_instance_var")
            .expect("Should find @lambda_instance_var");

        assert!(
            lambda_instance_var_entry.1.line_span() > 1,
            "Lambda instance variable range should span multiple lines, got {} lines. Range: {:?}",
            lambda_instance_var_entry.1.line_span(),
            lambda_instance_var_entry.1
        );

        let proc_instance_var_entry = found_entries
            .iter()
            .find(|(fqn, _, _)| fqn == "TestModule::@proc_instance_var")
            .expect("Should find @proc_instance_var");

        assert!(
            proc_instance_var_entry.1.line_span() > 1,
            "Proc instance variable range should span multiple lines, got {} lines. Range: {:?}",
            proc_instance_var_entry.1.line_span(),
            proc_instance_var_entry.1
        );

        let lambda_class_var_entry = found_entries
            .iter()
            .find(|(fqn, _, _)| fqn == "TestModule::@@lambda_class_var")
            .expect("Should find @@lambda_class_var");

        assert!(
            lambda_class_var_entry.1.line_span() > 1,
            "Lambda class variable range should span multiple lines, got {} lines. Range: {:?}",
            lambda_class_var_entry.1.line_span(),
            lambda_class_var_entry.1
        );

        let proc_class_var_entry = found_entries
            .iter()
            .find(|(fqn, _, _)| fqn == "TestModule::@@proc_class_var")
            .expect("Should find @@proc_class_var");

        assert!(
            proc_class_var_entry.1.line_span() > 1,
            "Proc class variable range should span multiple lines, got {} lines. Range: {:?}",
            proc_class_var_entry.1.line_span(),
            proc_class_var_entry.1
        );

        let standalone_proc_entry = found_entries
            .iter()
            .find(|(fqn, _, _)| fqn == "TestModule::Proc.new")
            .expect("Should find standalone Proc.new");

        assert!(
            standalone_proc_entry.1.line_span() > 1,
            "Standalone Proc.new range should span multiple lines, got {} lines. Range: {:?}",
            standalone_proc_entry.1.line_span(),
            standalone_proc_entry.1
        );

        let block_entry = found_entries
            .iter()
            .find(|(fqn, _, _)| fqn == "TestModule::block")
            .expect("Should find block");

        assert!(
            block_entry.1.line_span() > 1,
            "Block range should span multiple lines, got {} lines. Range: {:?}",
            block_entry.1.line_span(),
            block_entry.1
        );
    }
}
