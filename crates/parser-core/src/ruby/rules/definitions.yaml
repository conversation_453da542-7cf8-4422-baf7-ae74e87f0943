id: ruby-definitions
language: ruby
# This rule captures definitions.
# Receivers are captured explicitly by FQN extraction 

rule:
  any:
    # Class definition: capture class name
    - kind: class
      has:
        field: name
        kind: constant
        pattern: $CLASS_DEF_NAME

    # Module definition: capture module name
    - kind: module
      has:
        field: name
        kind: constant
        pattern: $MODULE_DEF_NAME

    # Method definition (handles standard and potentially nested ones)
    - kind: method
      has:
        field: name
        kind: identifier
        pattern: $METHOD_DEF_NAME

    # Singleton Method definition (handles standard and potentially nested ones)
    - kind: singleton_method
      has:
        field: name
        kind: identifier
        pattern: $SINGLETON_METHOD_DEF_NAME

    # Lambda definitions: capture lambda expressions assigned to constants/variables
    - all:
        - kind: assignment
        - has:
            field: left
            any:
              - kind: constant
                pattern: $LAMBDA_CONSTANT_NAME
              - kind: identifier
                pattern: $LAMBDA_VARIABLE_NAME
              - kind: instance_variable
                pattern: $LAMBDA_INSTANCE_VAR
              - kind: class_variable
                pattern: $LAMBDA_CLASS_VAR
        - has:
            field: right
            all:
              - kind: call
              - has:
                  field: method
                  kind: identifier
                  pattern: "lambda"
              - has:
                  field: block
                  any:
                    - kind: do_block
                    - kind: block
        - pattern: $LAMBDA_DEF

    # Lambda literals: capture lambda literals (-> syntax) assigned to constants/variables
    - all:
        - kind: assignment
        - has:
            field: left
            any:
              - kind: constant
                pattern: $LAMBDA_CONSTANT_NAME
              - kind: identifier
                pattern: $LAMBDA_VARIABLE_NAME
              - kind: instance_variable
                pattern: $LAMBDA_INSTANCE_VAR
              - kind: class_variable
                pattern: $LAMBDA_CLASS_VAR
        - has:
            field: right
            kind: lambda
        - pattern: $LAMBDA_DEF

    # Proc.new assignments: capture Proc.new expressions assigned to constants/variables
    - all:
        - kind: assignment
        - has:
            field: left
            any:
              - kind: constant
                pattern: $PROC_CONSTANT_NAME
              - kind: identifier
                pattern: $PROC_VARIABLE_NAME
              - kind: instance_variable
                pattern: $PROC_INSTANCE_VAR
              - kind: class_variable
                pattern: $PROC_CLASS_VAR
        - has:
            field: right
            all:
              - kind: call
              - has:
                  field: receiver
                  kind: constant
                  pattern: "Proc"
              - has:
                  field: method
                  kind: identifier
                  pattern: "new"
        - pattern: $PROC_ASSIGNMENT

    # Proc method assignments: capture proc {} method calls assigned to constants/variables
    - all:
        - kind: assignment
        - has:
            field: left
            any:
              - kind: constant
                pattern: $PROC_CONSTANT_NAME
              - kind: identifier
                pattern: $PROC_VARIABLE_NAME
              - kind: instance_variable
                pattern: $PROC_INSTANCE_VAR
              - kind: class_variable
                pattern: $PROC_CLASS_VAR
        - has:
            field: right
            all:
              - kind: call
              - has:
                  field: method
                  kind: identifier
                  pattern: "proc"
        - pattern: $PROC_ASSIGNMENT

    # Standalone Proc.new definitions: only capture when they are direct statements, not part of expressions
    - all:
        - kind: call
        - has:
            field: receiver
            kind: constant
            pattern: "Proc"
        - has:
            field: method
            kind: identifier
            pattern: "new"
        - pattern: $PROC_DEF
        - inside:
            any:
              - kind: body_statement
              - kind: program
        - not:
            inside:
              kind: assignment
