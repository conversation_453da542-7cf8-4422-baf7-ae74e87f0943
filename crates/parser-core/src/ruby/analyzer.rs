use crate::ParseResult;
use crate::analyzer::{<PERSON><PERSON><PERSON>ult, Analy<PERSON>};
use crate::ruby::definitions::find_definitions;
use crate::ruby::fqn::{RubyFqn, build_fqn_and_node_indices, ruby_fqn_to_string};
use crate::ruby::types::RubyDefinitionType;
use crate::rules::MatchWithNodes;

/// Type alias for Ruby-specific analyzer
pub type RubyAnalyzer = Analyzer<RubyFqn, RubyDefinitionType>;

/// Type alias for Ruby-specific analysis result
pub type RubyAnalysisResult = AnalysisResult<RubyFqn, RubyDefinitionType>;

impl RubyAnalyzer {
    /// Analyze Ruby code and extract definitions with FQN computation
    pub fn analyze(
        &self,
        matches: &[MatchWithNodes],
        parser_result: &ParseResult,
    ) -> crate::Result<RubyAnalysisResult> {
        let (ruby_node_fqn_map, _node_index_map) = build_fqn_and_node_indices(&parser_result.ast);
        let definitions = find_definitions(matches, &ruby_node_fqn_map);
        let imports = Vec::new(); // Implement find_imports

        Ok(RubyAnalysisResult::new(definitions, imports))
    }
}

impl RubyAnalysisResult {
    /// Get FQN strings for all definitions that have them
    pub fn ruby_definition_fqn_strings(&self) -> Vec<String> {
        self.definition_fqn_strings(ruby_fqn_to_string)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::definitions::DefinitionLookup;
    use crate::parser::SupportedLanguage;
    use crate::ruby::types::RubyDefinitionType;
    use crate::rules::{RuleManager, run_rules};
    use crate::{LanguageParser, parser::GenericParser};

    #[test]
    fn test_analyzer_creation() {
        let _analyzer = RubyAnalyzer::new();
    }

    #[test]
    fn test_analyze_simple_ruby_code() -> crate::Result<()> {
        let analyzer = RubyAnalyzer::new();
        let ruby_code = r#"
class Calculator
  attr_reader :value

  def initialize
    @value = 0
  end

  def add(number)
    @value += number
    self
  end

  def self.create
    new
  end
end

module MathUtils
  PI = 3.14159
  
  def self.square(n)
    n * n
  end
end
"#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Ruby);
        let parse_result = parser.parse(ruby_code, Some("calculator.rb"))?;
        let rule_manager = RuleManager::new(SupportedLanguage::Ruby);
        let matches = run_rules(&parse_result.ast, Some("calculator.rb"), &rule_manager);
        let result = analyzer.analyze(&matches, &parse_result)?;

        // Check that we found definitions
        assert!(!result.definitions.is_empty(), "Should find definitions");

        // Check specific types
        let classes = result.definitions_of_type(&RubyDefinitionType::Class);
        let modules = result.definitions_of_type(&RubyDefinitionType::Module);
        let methods = result.definitions_of_type(&RubyDefinitionType::Method);
        let singleton_methods = result.definitions_of_type(&RubyDefinitionType::SingletonMethod);

        assert!(!classes.is_empty(), "Should find classes");
        assert!(!modules.is_empty(), "Should find modules");
        assert!(!methods.is_empty(), "Should find methods");
        assert!(
            !singleton_methods.is_empty(),
            "Should find singleton methods"
        );

        // Check specific names
        let calculator_class = result.definitions_by_name("Calculator");
        assert!(!calculator_class.is_empty(), "Should find Calculator class");

        let math_utils_module = result.definitions_by_name("MathUtils");
        assert!(
            !math_utils_module.is_empty(),
            "Should find MathUtils module"
        );

        // Check FQN functionality
        let fqn_strings = result.ruby_definition_fqn_strings();
        assert!(!fqn_strings.is_empty(), "Should have FQN strings");

        println!("Found FQN strings: {fqn_strings:?}");

        // Print results for debugging
        println!("Found {} definitions:", result.definitions.len());
        for def in &result.definitions {
            if let Some(fqn) = &def.fqn {
                println!(
                    "  {:?}: {} -> {} (line {})",
                    def.definition_type,
                    def.name,
                    ruby_fqn_to_string(fqn),
                    def.match_info.range.start.line
                );
            } else {
                println!(
                    "  {:?}: {} (line {})",
                    def.definition_type, def.name, def.match_info.range.start.line
                );
            }
        }

        let counts = result.count_definitions_by_type();
        println!("Counts by type: {counts:?}");

        Ok(())
    }

    #[test]
    fn test_analyzer_with_complex_ruby_code() -> crate::Result<()> {
        let analyzer = RubyAnalyzer::new();
        let ruby_code = r#"
module Authentication
  class User
    attr_accessor :name, :email
    
    ROLES = %w[admin user guest].freeze
    
    def initialize(name, email)
      @name = name
      @email = email
    end
    
    def admin?
      @role == :admin
    end
    
    def self.find_by_email(email)
      # Implementation
    end
    
    class << self
      def all_users
        # Implementation
      end
    end
  end
  
  module Validators
    EMAIL_REGEX = /\A[\w+\-.]+@[a-z\d\-]+(\.[a-z\d\-]+)*\.[a-z]+\z/i
    
    def self.valid_email?(email)
      EMAIL_REGEX.match?(email)
    end
  end
end
"#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Ruby);
        let parse_result = parser.parse(ruby_code, Some("user.rb"))?;
        let rule_manager = RuleManager::new(SupportedLanguage::Ruby);
        let matches = run_rules(&parse_result.ast, Some("user.rb"), &rule_manager);
        let result = analyzer.analyze(&matches, &parse_result)?;

        // Verify we found various types of definitions
        assert!(!result.definitions.is_empty(), "Should find definitions");

        let counts = result.count_definitions_by_type();
        println!("Complex code counts: {counts:?}");

        // Should find nested structures - updated for callable-only design
        assert!(counts.get(&RubyDefinitionType::Module).unwrap_or(&0) >= &2);
        assert!(counts.get(&RubyDefinitionType::Class).unwrap_or(&0) >= &1);
        assert!(counts.get(&RubyDefinitionType::Method).unwrap_or(&0) >= &3);

        // Test FQN functionality with nested structures
        let definitions_with_fqn = result.definitions_with_fqn();
        assert!(
            !definitions_with_fqn.is_empty(),
            "Should have definitions with FQNs"
        );

        for def in &definitions_with_fqn {
            let fqn_string = ruby_fqn_to_string(def.fqn.as_ref().unwrap());
            println!("Definition with FQN: {} -> {}", def.name, fqn_string);
        }

        Ok(())
    }

    #[test]
    fn test_analysis_result_methods() -> crate::Result<()> {
        let analyzer = RubyAnalyzer::new();
        let ruby_code = r#"
class Test
  def method1; end
  def method2; end
end

class Test2
  def method1; end
end
"#;

        let parser = GenericParser::default_for_language(SupportedLanguage::Ruby);
        let parse_result = parser.parse(ruby_code, Some("test.rb"))?;
        let rule_manager = RuleManager::new(SupportedLanguage::Ruby);
        let matches = run_rules(&parse_result.ast, Some("test.rb"), &rule_manager);
        let result = analyzer.analyze(&matches, &parse_result)?;

        // Test definitions_by_name
        let method1_defs = result.definitions_by_name("method1");
        assert_eq!(method1_defs.len(), 2, "Should find 2 method1 definitions");

        let test_defs = result.definitions_by_name("Test");
        assert_eq!(test_defs.len(), 1, "Should find 1 Test class");

        // Test definition_names
        let names = result.definition_names();
        assert!(names.contains(&"Test"));
        assert!(names.contains(&"Test2"));
        assert!(names.contains(&"method1"));
        assert!(names.contains(&"method2"));

        // Test definitions_with_fqn
        let defs_with_fqn = result.definitions_with_fqn();
        assert!(
            !defs_with_fqn.is_empty(),
            "Should have definitions with FQNs"
        );

        // Test fqn_strings
        let fqn_strings = result.ruby_definition_fqn_strings();
        assert!(!fqn_strings.is_empty(), "Should have FQN strings");

        for fqn_string in &fqn_strings {
            assert!(!fqn_string.is_empty(), "FQN strings should not be empty");
        }

        Ok(())
    }

    #[test]
    fn test_error_handling() {
        use crate::parser::{GenericParser, LanguageParser};

        let analyzer = RubyAnalyzer::new();

        // Create a valid Ruby parse result
        let parser = GenericParser::default_for_language(SupportedLanguage::Ruby);
        let parse_result = parser.parse("class Test; end", Some("test.rb")).unwrap();

        let rule_manager = RuleManager::new(SupportedLanguage::Ruby);
        let matches = run_rules(&parse_result.ast, Some("test.rb"), &rule_manager);
        let result = analyzer.analyze(&matches, &parse_result);
        assert!(result.is_ok(), "Should work with Ruby language");
    }

    #[test]
    fn test_analyzer_with_sample_rb_fixture() -> crate::Result<()> {
        let analyzer = RubyAnalyzer::new();
        let fixture_path = "src/ruby/fixtures/sample.rb";
        let ruby_code = std::fs::read_to_string(fixture_path)
            .expect("Should be able to read sample.rb fixture");

        let parser = GenericParser::default_for_language(SupportedLanguage::Ruby);
        let parse_result = parser.parse(&ruby_code, Some(fixture_path))?;
        let rule_manager = RuleManager::new(SupportedLanguage::Ruby);
        let matches = run_rules(&parse_result.ast, Some(fixture_path), &rule_manager);
        let result = analyzer.analyze(&matches, &parse_result)?;

        // Verify we found definitions
        assert!(
            !result.definitions.is_empty(),
            "Should find definitions in sample.rb"
        );

        // Count by type
        let counts = result.count_definitions_by_type();
        println!("sample.rb analyzer counts: {counts:?}");

        // Test specific filtering
        let modules = result.definitions_of_type(&RubyDefinitionType::Module);
        let classes = result.definitions_of_type(&RubyDefinitionType::Class);
        let methods = result.definitions_of_type(&RubyDefinitionType::Method);
        let singleton_methods = result.definitions_of_type(&RubyDefinitionType::SingletonMethod);

        assert!(modules.len() >= 2, "Should find at least 2 modules");
        assert!(classes.len() >= 2, "Should find at least 2 classes");
        assert!(methods.len() >= 2, "Should find at least 2 methods");
        assert!(
            singleton_methods.len() >= 2,
            "Should find at least 2 singleton methods"
        );

        // Test name searching
        let auth_service_defs = result.definitions_by_name("AuthenticationService");
        assert!(
            !auth_service_defs.is_empty(),
            "Should find AuthenticationService"
        );

        let credentials_checker_defs = result.definitions_by_name("CredentialsChecker");
        assert!(
            !credentials_checker_defs.is_empty(),
            "Should find CredentialsChecker"
        );

        // Test definition names
        let names = result.definition_names();
        assert!(names.contains(&"AuthenticationService"));
        assert!(names.contains(&"CredentialsChecker"));
        assert!(names.contains(&"initialize"));
        assert!(names.contains(&"valid_password?"));

        // Test FQN functionality
        let definitions_with_fqn = result.definitions_with_fqn();
        assert!(
            !definitions_with_fqn.is_empty(),
            "Should have definitions with FQNs"
        );

        let fqn_strings = result.ruby_definition_fqn_strings();
        assert!(!fqn_strings.is_empty(), "Should have FQN strings");

        println!(
            "Found {} definitions in sample.rb",
            result.definitions.len()
        );
        for def in &result.definitions {
            if let Some(fqn) = &def.fqn {
                println!(
                    "  {:?}: {} -> {} (line {})",
                    def.definition_type,
                    def.name,
                    ruby_fqn_to_string(fqn),
                    def.match_info.range.start.line
                );
            } else {
                println!(
                    "  {:?}: {} (line {})",
                    def.definition_type, def.name, def.match_info.range.start.line
                );
            }
        }

        Ok(())
    }

    #[test]
    fn test_analyzer_with_monolith_sample_1_rb_fixture() -> crate::Result<()> {
        let analyzer = RubyAnalyzer::new();
        let fixture_path = "src/ruby/fixtures/monolith_sample_1.rb";
        let ruby_code = std::fs::read_to_string(fixture_path)
            .expect("Should be able to read monolith_sample_1.rb fixture");

        let parser = GenericParser::default_for_language(SupportedLanguage::Ruby);
        let parse_result = parser.parse(&ruby_code, Some(fixture_path))?;
        let rule_manager = RuleManager::new(SupportedLanguage::Ruby);
        let matches = run_rules(&parse_result.ast, Some(fixture_path), &rule_manager);
        let result = analyzer.analyze(&matches, &parse_result)?;

        // Verify we found definitions
        assert!(
            !result.definitions.is_empty(),
            "Should find definitions in monolith_sample_1.rb"
        );

        // Count by type
        let counts = result.count_definitions_by_type();
        println!("monolith_sample_1.rb analyzer counts: {counts:?}");

        assert_eq!(
            counts.get(&RubyDefinitionType::Class).unwrap_or(&0),
            &1,
            "Should find exactly 1 class"
        );
        assert!(
            counts.get(&RubyDefinitionType::Method).unwrap_or(&0) >= &9,
            "Should find at least 9 methods"
        );

        // Test name searching
        let jwt_controller_defs = result.definitions_by_name("JwtController");
        assert_eq!(
            jwt_controller_defs.len(),
            1,
            "Should find exactly 1 JwtController"
        );

        // Verify specific method names
        let names = result.definition_names();
        let expected_methods = [
            "auth",
            "authenticate_project_or_user",
            "log_authentication_failed",
            "render_access_denied",
            "auth_params",
            "additional_params",
            "scopes_param",
            "auth_user",
            "bypass_admin_mode!",
        ];

        for method_name in &expected_methods {
            assert!(
                names.contains(method_name),
                "Should find {method_name} method"
            );
        }

        println!(
            "Found {} definitions in monolith_sample_1.rb",
            result.definitions.len()
        );

        Ok(())
    }

    #[test]
    fn test_analyzer_with_references_test_rails_rb_fixture() -> crate::Result<()> {
        let analyzer = RubyAnalyzer::new();
        let fixture_path = "src/ruby/fixtures/references_test_rails.rb";
        let ruby_code = std::fs::read_to_string(fixture_path)
            .expect("Should be able to read references_test_rails.rb fixture");

        let parser = GenericParser::default_for_language(SupportedLanguage::Ruby);
        let parse_result = parser.parse(&ruby_code, Some(fixture_path))?;
        let rule_manager = RuleManager::new(SupportedLanguage::Ruby);
        let matches = run_rules(&parse_result.ast, Some(fixture_path), &rule_manager);
        let result = analyzer.analyze(&matches, &parse_result)?;

        // Verify we found definitions
        assert!(
            !result.definitions.is_empty(),
            "Should find definitions in references_test_rails.rb"
        );

        // Count by type
        let counts = result.count_definitions_by_type();
        println!("references_test_rails.rb analyzer counts: {counts:?}");

        // Should find multiple classes, modules, and methods
        assert!(
            counts.get(&RubyDefinitionType::Class).unwrap_or(&0) >= &3,
            "Should find at least 3 classes"
        );
        assert!(
            counts.get(&RubyDefinitionType::Module).unwrap_or(&0) >= &3,
            "Should find at least 3 modules"
        );
        assert!(
            counts.get(&RubyDefinitionType::Method).unwrap_or(&0) >= &4,
            "Should find at least 4 methods"
        );

        // Test specific definitions
        let names = result.definition_names();
        assert!(names.contains(&"ApplicationController"));
        assert!(names.contains(&"UsersController"));
        assert!(names.contains(&"User"));
        assert!(names.contains(&"ActionController"));
        assert!(names.contains(&"set_user"));
        assert!(names.contains(&"show"));
        assert!(names.contains(&"create"));

        println!(
            "Found {} definitions in references_test_rails.rb",
            result.definitions.len()
        );

        Ok(())
    }

    #[test]
    fn test_analyzer_with_references_test_tracing_rb_fixture() -> crate::Result<()> {
        let analyzer = RubyAnalyzer::new();
        let fixture_path = "src/ruby/fixtures/references_test_tracing.rb";
        let ruby_code = std::fs::read_to_string(fixture_path)
            .expect("Should be able to read references_test_tracing.rb fixture");

        let parser = GenericParser::default_for_language(SupportedLanguage::Ruby);
        let parse_result = parser.parse(&ruby_code, Some(fixture_path))?;
        let rule_manager = RuleManager::new(SupportedLanguage::Ruby);
        let matches = run_rules(&parse_result.ast, Some(fixture_path), &rule_manager);
        let result = analyzer.analyze(&matches, &parse_result)?;

        // Verify we found definitions
        assert!(
            !result.definitions.is_empty(),
            "Should find definitions in references_test_tracing.rb"
        );

        // Count by type
        let counts = result.count_definitions_by_type();
        println!("references_test_tracing.rb analyzer counts: {counts:?}");

        assert!(
            counts.get(&RubyDefinitionType::Module).unwrap_or(&0) >= &2,
            "Should find at least 2 modules"
        );
        assert!(
            counts.get(&RubyDefinitionType::Class).unwrap_or(&0) >= &1,
            "Should find at least 1 class"
        );
        assert!(
            counts.get(&RubyDefinitionType::Method).unwrap_or(&0) >= &1,
            "Should find at least 1 method"
        );
        assert!(
            counts
                .get(&RubyDefinitionType::SingletonMethod)
                .unwrap_or(&0)
                >= &1,
            "Should find at least 1 singleton method"
        );

        // Test specific definitions
        let names = result.definition_names();
        assert!(names.contains(&"Service"));
        assert!(names.contains(&"Client"));
        assert!(names.contains(&"Runner"));
        assert!(names.contains(&"execute"));
        assert!(names.contains(&"build"));

        println!(
            "Found {} definitions in references_test_tracing.rb",
            result.definitions.len()
        );

        Ok(())
    }

    #[test]
    fn test_analyzer_comprehensive_fixture_coverage() -> crate::Result<()> {
        let analyzer = RubyAnalyzer::new();
        let fixtures = [
            "src/ruby/fixtures/sample.rb",
            "src/ruby/fixtures/monolith_sample_1.rb",
            "src/ruby/fixtures/references_test_rails.rb",
            "src/ruby/fixtures/references_test_tracing.rb",
        ];

        let mut total_definitions = 0;
        let mut total_counts = std::collections::HashMap::new();
        let mut all_definition_names = std::collections::HashSet::new();
        let mut all_fqn_strings = std::collections::HashSet::new();

        for fixture_path in &fixtures {
            let ruby_code = std::fs::read_to_string(fixture_path)
                .unwrap_or_else(|_| panic!("Should be able to read {fixture_path}"));

            let parser = GenericParser::default_for_language(SupportedLanguage::Ruby);
            let parse_result = parser.parse(&ruby_code, Some(fixture_path))?;
            let rule_manager = RuleManager::new(SupportedLanguage::Ruby);
            let matches = run_rules(&parse_result.ast, Some(fixture_path), &rule_manager);
            let result = analyzer.analyze(&matches, &parse_result)?;

            assert!(
                !result.definitions.is_empty(),
                "Should find definitions in {fixture_path}"
            );

            total_definitions += result.definitions.len();

            // Accumulate counts
            let counts = result.count_definitions_by_type();
            for (def_type, count) in counts {
                *total_counts.entry(def_type).or_insert(0) += count;
            }

            // Collect all definition names
            for name in result.definition_names() {
                all_definition_names.insert(name.to_string());
            }

            // Collect all FQN strings
            for fqn_string in result.ruby_definition_fqn_strings() {
                all_fqn_strings.insert(fqn_string);
            }

            println!("{}: {} definitions", fixture_path, result.definitions.len());
        }

        println!("Total definitions across all fixtures: {total_definitions}");
        println!("Total counts by type: {total_counts:?}");
        println!(
            "Total unique definition names: {}",
            all_definition_names.len()
        );
        println!("Total unique FQN strings: {}", all_fqn_strings.len());

        // Comprehensive assertions - updated for callable-only design
        assert!(
            total_definitions >= 30,
            "Should find at least 30 definitions across all fixtures"
        );
        assert!(
            total_counts.get(&RubyDefinitionType::Class).unwrap_or(&0) >= &5,
            "Should find at least 5 classes"
        );
        assert!(
            total_counts.get(&RubyDefinitionType::Module).unwrap_or(&0) >= &5,
            "Should find at least 5 modules"
        );
        assert!(
            total_counts.get(&RubyDefinitionType::Method).unwrap_or(&0) >= &10,
            "Should find at least 10 methods"
        );
        assert!(
            total_counts
                .get(&RubyDefinitionType::SingletonMethod)
                .unwrap_or(&0)
                >= &3,
            "Should find at least 3 singleton methods"
        );

        // Verify we found a good variety of definition names
        assert!(
            all_definition_names.len() >= 25,
            "Should find at least 25 unique definition names"
        );

        // Verify we have FQN strings
        assert!(!all_fqn_strings.is_empty(), "Should have FQN strings");

        // Test that key definitions are found across fixtures
        let key_definitions = [
            "AuthenticationService",
            "CredentialsChecker",
            "JwtController",
            "ApplicationController",
            "UsersController",
            "Service",
            "Client",
            "Runner",
        ];

        for key_def in &key_definitions {
            assert!(
                all_definition_names.contains(*key_def),
                "Should find key definition: {key_def}"
            );
        }

        Ok(())
    }

    #[test]
    fn test_analyzer_definition_filtering_and_grouping() -> crate::Result<()> {
        let analyzer = RubyAnalyzer::new();
        let fixture_path = "src/ruby/fixtures/sample.rb";
        let ruby_code = std::fs::read_to_string(fixture_path)
            .expect("Should be able to read sample.rb fixture");

        let parser = GenericParser::default_for_language(SupportedLanguage::Ruby);
        let parse_result = parser.parse(&ruby_code, Some(fixture_path))?;
        let rule_manager = RuleManager::new(SupportedLanguage::Ruby);
        let matches = run_rules(&parse_result.ast, Some(fixture_path), &rule_manager);
        let result = analyzer.analyze(&matches, &parse_result)?;

        // Test definitions_of_type filtering
        let all_modules = result.definitions_of_type(&RubyDefinitionType::Module);
        let all_classes = result.definitions_of_type(&RubyDefinitionType::Class);
        let all_methods = result.definitions_of_type(&RubyDefinitionType::Method);

        // Each filtered group should only contain the specified type
        for module_def in &all_modules {
            assert_eq!(module_def.definition_type, RubyDefinitionType::Module);
        }
        for class_def in &all_classes {
            assert_eq!(class_def.definition_type, RubyDefinitionType::Class);
        }
        for method_def in &all_methods {
            assert_eq!(method_def.definition_type, RubyDefinitionType::Method);
        }

        // Test definitions_by_name filtering
        let auth_service_defs = result.definitions_by_name("AuthenticationService");
        for def in &auth_service_defs {
            assert_eq!(def.name, "AuthenticationService");
        }

        // Test count_definitions_by_type
        let counts = result.count_definitions_by_type();
        let manual_module_count = result
            .definitions
            .iter()
            .filter(|d| d.definition_type == RubyDefinitionType::Module)
            .count();
        assert_eq!(
            counts.get(&RubyDefinitionType::Module).unwrap_or(&0),
            &manual_module_count
        );

        // Test definition_names
        let names = result.definition_names();
        let manual_names: std::collections::HashSet<_> =
            result.definitions.iter().map(|d| d.name.as_str()).collect();
        let names_set: std::collections::HashSet<_> = names.into_iter().collect();
        assert_eq!(names_set, manual_names);

        // Test FQN functionality
        let definitions_with_fqn = result.definitions_with_fqn();
        let fqn_strings = result.ruby_definition_fqn_strings();

        assert_eq!(
            definitions_with_fqn.len(),
            fqn_strings.len(),
            "Number of definitions with FQN should match number of FQN strings"
        );

        println!("Filtering and grouping tests passed for sample.rb");

        Ok(())
    }

    #[test]
    fn test_analyzer_with_comprehensive_definitions_fixture() -> crate::Result<()> {
        let analyzer = RubyAnalyzer::new();
        let fixture_path = "src/ruby/fixtures/comprehensive_definitions.rb";
        let ruby_code = std::fs::read_to_string(fixture_path)
            .expect("Should be able to read comprehensive_definitions.rb fixture");

        let parser = GenericParser::default_for_language(SupportedLanguage::Ruby);
        let parse_result = parser.parse(&ruby_code, Some(fixture_path))?;
        let rule_manager = RuleManager::new(SupportedLanguage::Ruby);
        let matches = run_rules(&parse_result.ast, Some(fixture_path), &rule_manager);
        let result = analyzer.analyze(&matches, &parse_result)?;

        // Verify we found definitions
        assert!(
            !result.definitions.is_empty(),
            "Should find definitions in comprehensive_definitions.rb"
        );

        // Count by type
        let counts = result.count_definitions_by_type();
        println!("comprehensive_definitions.rb analyzer counts: {counts:?}");

        // Verify all supported definition types are present
        assert!(
            counts.get(&RubyDefinitionType::Module).unwrap_or(&0) >= &2,
            "Should find at least 2 modules"
        );
        assert!(
            counts.get(&RubyDefinitionType::Class).unwrap_or(&0) >= &3,
            "Should find at least 3 classes"
        );
        assert!(
            counts.get(&RubyDefinitionType::Method).unwrap_or(&0) >= &8,
            "Should find at least 8 methods"
        );
        assert!(
            counts
                .get(&RubyDefinitionType::SingletonMethod)
                .unwrap_or(&0)
                >= &4,
            "Should find at least 4 singleton methods"
        );
        assert!(
            counts.get(&RubyDefinitionType::Lambda).unwrap_or(&0) >= &4,
            "Should find at least 4 lambdas"
        );
        assert!(
            counts.get(&RubyDefinitionType::Proc).unwrap_or(&0) >= &3,
            "Should find at least 3 procs"
        );

        // Test specific definitions we expect - updated for callable-only design
        let names = result.definition_names();
        let expected_definitions = [
            "DataProcessing",
            "Processor",
            "ConfigurationManager",
            "Utilities",
            "Cache",
            "VALIDATOR",
            "TRANSFORMER",
            "CONFIG_VALIDATOR",
            "initialize",
            "process",
            "create_default",
        ];

        for expected_def in &expected_definitions {
            assert!(
                names.contains(expected_def),
                "Should find definition: {expected_def}"
            );
        }

        // Test FQN functionality
        let definitions_with_fqn = result.definitions_with_fqn();
        assert!(
            !definitions_with_fqn.is_empty(),
            "Should have definitions with FQNs"
        );

        let fqn_strings = result.ruby_definition_fqn_strings();
        assert!(!fqn_strings.is_empty(), "Should have FQN strings");

        // Verify we have good representation of each definition type
        let lambda_defs = result.definitions_of_type(&RubyDefinitionType::Lambda);
        let proc_defs = result.definitions_of_type(&RubyDefinitionType::Proc);

        assert!(!lambda_defs.is_empty(), "Should find lambda definitions");
        assert!(!proc_defs.is_empty(), "Should find proc definitions");

        // Log some examples for verification
        println!("Found {} total definitions", result.definitions.len());
        println!("Lambda definitions found:");
        for def in lambda_defs {
            let fqn_str = def
                .fqn
                .as_ref()
                .map(ruby_fqn_to_string)
                .unwrap_or_else(|| "None".to_string());
            println!("  {} -> {}", def.name, fqn_str);
        }

        println!("Proc definitions found:");
        for def in proc_defs {
            let fqn_str = def
                .fqn
                .as_ref()
                .map(ruby_fqn_to_string)
                .unwrap_or_else(|| "None".to_string());
            println!("  {} -> {}", def.name, fqn_str);
        }

        Ok(())
    }
}
