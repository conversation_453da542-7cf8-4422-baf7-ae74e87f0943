use crate::{
    Position, Range,
    csharp::{
        csharp_ast::CSharpMatchKind,
        fqn::{CSharpFqn, CSharpNodeFqnMap},
    },
    definitions::DefinitionInfo,
    rules::{MatchWithNodes, MetaVarNode},
};
use once_cell::sync::Lazy;
use rustc_hash::FxHashMap;

/// Type-safe constants for capture variable names used in CSharp rule definitions
pub mod meta_vars {
    pub const NAMESPACE_IDENTIFIER: &str = "NAMESPACE_IDENTIFIER";
    pub const INTERFACE_IDENTIFIER: &str = "INTERFACE_IDENTIFIER";
    pub const CLASS_IDENTIFIER: &str = "CLASS_IDENTIFIER";
    pub const RECORD_IDENTIFIER: &str = "RECORD_IDENTIFIER";
    pub const STRUCT_IDENTIFIER: &str = "STRUCT_IDENTIFIER";
    pub const ENUM_IDENTIFIER: &str = "ENUM_IDENTIFIER";
    pub const DELEGATE_IDENTIFIER: &str = "DELEGATE_IDENTIFIER";
    pub const PROPERTY_IDENTIFIER: &str = "PROPERTY_IDENTIFIER";
    pub const INSTANCE_METHOD_IDENTIFIER: &str = "INSTANCE_METHOD_IDENTIFIER";
    pub const STATIC_METHOD_IDENTIFIER: &str = "STATIC_METHOD_IDENTIFIER";
    pub const EXTENSION_METHOD_IDENTIFIER: &str = "EXTENSION_METHOD_IDENTIFIER";
    pub const CONSTRUCTOR: &str = "CONSTRUCTOR";
    pub const FINALIZER: &str = "FINALIZER";
    pub const LAMBDA_LOCAL_NAME: &str = "LAMBDA_LOCAL_NAME";
    pub const EVENT_DECLARATION: &str = "EVENT_DECLARATION";
    pub const FIELD_DECLARATION: &str = "FIELD_DECLARATION";
    pub const ANONYMOUS_TYPE: &str = "ANONYMOUS_TYPE";
    pub const INDEXER_DECLARATION: &str = "INDEXER_DECLARATION";
    pub const OPERATOR_DECLARATION: &str = "OPERATOR_DECLARATION_BODY";
}

/// Configuration for extracting different types of definitions
#[derive(Debug)]
struct DefinitionExtractor {
    definition_type: CSharpDefinitionType,
    extractor: fn(&FxHashMap<String, MetaVarNode>) -> Option<&MetaVarNode>,
}

pub type CSharpDefinitionInfo = DefinitionInfo<CSharpDefinitionType, CSharpFqn>;

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, serde::Serialize, serde::Deserialize)]
pub enum CSharpDefinitionType {
    Namespace,
    Class,
    InstanceMethod,
    StaticMethod,
    ExtensionMethod,
    Property,
    Constructor,
    Finalizer,
    Interface,
    Enum,
    Struct,
    Record,
    Delegate,
    Lambda,
    Operator,
    Field,
    AnonymousType,
    Indexer,
    Event,
}

pub fn find_definitions(
    matches: &[MatchWithNodes],
    fqn_map: &CSharpNodeFqnMap,
) -> Vec<CSharpDefinitionInfo> {
    let mut definitions = Vec::new();

    for match_item in matches {
        if CSharpMatchKind::from_rule_id(&match_item.match_info.rule_id)
            != CSharpMatchKind::Definition
        {
            continue;
        }

        if let Some(def_info) = extract_definition_info(match_item, fqn_map) {
            definitions.push(def_info);
        }
    }

    definitions
}

fn find_fqn_for_definition<'a>(
    meta_var_node: &MetaVarNode,
    node_fqn_map: &'a CSharpNodeFqnMap,
) -> Option<&'a CSharpFqn> {
    let range = Range::new(
        Position::new(meta_var_node.range.0, meta_var_node.range.1),
        Position::new(meta_var_node.range.2, meta_var_node.range.3),
        meta_var_node.byte_offset,
    );

    node_fqn_map.get(&range).map(|(_, fqn)| fqn)
}

fn extract_definition_info(
    match_item: &MatchWithNodes,
    fqn_map: &CSharpNodeFqnMap,
) -> Option<CSharpDefinitionInfo> {
    let meta_var_map = &match_item.match_info.meta_var_map;

    // Try each extractor until we find one that matches
    for extractor in &*DEFINITION_EXTRACTORS {
        if let Some(meta_var_node) = (extractor.extractor)(meta_var_map) {
            // Try to find FQN for this definition
            let fqn = find_fqn_for_definition(meta_var_node, fqn_map);

            if extractor.definition_type == CSharpDefinitionType::Operator {
                return Some(CSharpDefinitionInfo::new(
                    CSharpDefinitionType::Operator,
                    fqn.and_then(|vec| vec.last())
                        .map(|part| part.node_name.clone())
                        .unwrap_or("operator".to_string()),
                    fqn.cloned(),
                    match_item.match_info.clone(),
                ));
            } else if extractor.definition_type == CSharpDefinitionType::Indexer {
                return Some(CSharpDefinitionInfo::new(
                    CSharpDefinitionType::Indexer,
                    "indexer".to_string(),
                    fqn.cloned(),
                    match_item.match_info.clone(),
                ));
            }

            return Some(CSharpDefinitionInfo::new(
                extractor.definition_type,
                meta_var_node.text.clone(),
                fqn.cloned(),
                match_item.match_info.clone(),
            ));
        }
    }

    None
}

static DEFINITION_EXTRACTORS: Lazy<Vec<DefinitionExtractor>> = Lazy::new(|| {
    vec![
        DefinitionExtractor {
            definition_type: CSharpDefinitionType::Namespace,
            extractor: |meta_var_map| meta_var_map.get(meta_vars::NAMESPACE_IDENTIFIER),
        },
        DefinitionExtractor {
            definition_type: CSharpDefinitionType::Class,
            extractor: |meta_var_map| meta_var_map.get(meta_vars::CLASS_IDENTIFIER),
        },
        DefinitionExtractor {
            definition_type: CSharpDefinitionType::Interface,
            extractor: |meta_var_map| meta_var_map.get(meta_vars::INTERFACE_IDENTIFIER),
        },
        DefinitionExtractor {
            definition_type: CSharpDefinitionType::Record,
            extractor: |meta_var_map| meta_var_map.get(meta_vars::RECORD_IDENTIFIER),
        },
        DefinitionExtractor {
            definition_type: CSharpDefinitionType::Struct,
            extractor: |meta_var_map| meta_var_map.get(meta_vars::STRUCT_IDENTIFIER),
        },
        DefinitionExtractor {
            definition_type: CSharpDefinitionType::Enum,
            extractor: |meta_var_map| meta_var_map.get(meta_vars::ENUM_IDENTIFIER),
        },
        DefinitionExtractor {
            definition_type: CSharpDefinitionType::Property,
            extractor: |meta_var_map| meta_var_map.get(meta_vars::PROPERTY_IDENTIFIER),
        },
        DefinitionExtractor {
            definition_type: CSharpDefinitionType::InstanceMethod,
            extractor: |meta_var_map| meta_var_map.get(meta_vars::INSTANCE_METHOD_IDENTIFIER),
        },
        DefinitionExtractor {
            definition_type: CSharpDefinitionType::StaticMethod,
            extractor: |meta_var_map| meta_var_map.get(meta_vars::STATIC_METHOD_IDENTIFIER),
        },
        DefinitionExtractor {
            definition_type: CSharpDefinitionType::ExtensionMethod,
            extractor: |meta_var_map| meta_var_map.get(meta_vars::EXTENSION_METHOD_IDENTIFIER),
        },
        DefinitionExtractor {
            definition_type: CSharpDefinitionType::Constructor,
            extractor: |meta_var_map| meta_var_map.get(meta_vars::CONSTRUCTOR),
        },
        DefinitionExtractor {
            definition_type: CSharpDefinitionType::Finalizer,
            extractor: |meta_var_map| meta_var_map.get(meta_vars::FINALIZER),
        },
        DefinitionExtractor {
            definition_type: CSharpDefinitionType::Lambda,
            extractor: |meta_var_map| meta_var_map.get(meta_vars::LAMBDA_LOCAL_NAME),
        },
        DefinitionExtractor {
            definition_type: CSharpDefinitionType::Field,
            extractor: |meta_var_map| meta_var_map.get(meta_vars::FIELD_DECLARATION),
        },
        DefinitionExtractor {
            definition_type: CSharpDefinitionType::AnonymousType,
            extractor: |meta_var_map| meta_var_map.get(meta_vars::ANONYMOUS_TYPE),
        },
        DefinitionExtractor {
            definition_type: CSharpDefinitionType::Indexer,
            extractor: |meta_var_map| meta_var_map.get(meta_vars::INDEXER_DECLARATION),
        },
        DefinitionExtractor {
            definition_type: CSharpDefinitionType::Operator,
            extractor: |meta_var_map| meta_var_map.get(meta_vars::OPERATOR_DECLARATION),
        },
        DefinitionExtractor {
            definition_type: CSharpDefinitionType::Event,
            extractor: |meta_var_map| meta_var_map.get(meta_vars::EVENT_DECLARATION),
        },
    ]
});
