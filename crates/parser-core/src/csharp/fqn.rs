use crate::fqn::FQNPart;
use crate::utils::{Range, node_to_range};
use ast_grep_core::tree_sitter::StrDoc;
use ast_grep_core::{AstGrep, Node};
use ast_grep_language::SupportLang;
use rustc_hash::FxHashMap;
use smallvec::{SmallVec, smallvec};
use std::sync::Arc;

#[derive(Debug, Clone, PartialEq, Eq, Hash, serde::Serialize, serde::Deserialize)]
pub enum CSharpFqnPartType {
    Namespace,
    Class,
    InstanceMethod,
    StaticMethod,
    ExtensionMethod,
    Property,
    Field,
    Constructor,
    Finalizer,
    Delegate,
    Interface,
    Enum,
    Struct,
    Record,
    Lambda,
    Operator,
    Indexer,
    Event,
    AnonymousType,
}

pub type CSharpFqnPart = FQNPart<CSharpFqnPartType>;

pub type CSharpFqn = Arc<SmallVec<[CSharpFqnPart; 8]>>;

pub type CSharpNodeFqnMap<'a> = FxHashMap<Range, (Node<'a, StrDoc<SupportLang>>, CSharpFqn)>;

type ScopeStack = SmallVec<[CSharpFqnPart; 8]>;

/// Helper function to add children to stack in reverse order
fn push_children_reverse<'a>(
    node: &Node<'a, StrDoc<SupportLang>>,
    stack: &mut Vec<Option<Node<'a, StrDoc<SupportLang>>>>,
) {
    let children: Vec<_> = node.children().collect();
    stack.reserve(children.len());
    for child in children.into_iter().rev() {
        stack.push(Some(child));
    }
}

// get the range of node identifier and build the fqn part
fn node_to_fqn_part(
    node: &Node<StrDoc<SupportLang>>,
    kind: &str,
) -> Option<(Range, CSharpFqnPart)> {
    match kind {
        "namespace_declaration" | "file_scoped_namespace_declaration" => {
            process_simple_node(node, CSharpFqnPartType::Namespace)
        }
        "class_declaration" => process_simple_node(node, CSharpFqnPartType::Class),
        "interface_declaration" => process_simple_node(node, CSharpFqnPartType::Interface),
        "enum_declaration" => process_simple_node(node, CSharpFqnPartType::Enum),
        "struct_declaration" => process_simple_node(node, CSharpFqnPartType::Struct),
        "record_declaration" => process_simple_node(node, CSharpFqnPartType::Record),
        "constructor_declaration" => process_simple_node(node, CSharpFqnPartType::Constructor),
        "destructor_declaration" => process_simple_node(node, CSharpFqnPartType::Finalizer),
        "delegate_declaration" => process_simple_node(node, CSharpFqnPartType::Delegate),
        "property_declaration" => process_simple_node(node, CSharpFqnPartType::Property),
        "method_declaration" => process_method_node(node),
        "variable_declarator" => process_variable_declarator_node(node),
        "operator_declaration" => process_operator_declaration_node(node),
        "indexer_declaration" => process_indexer_declaration_node(node),
        "event_field_declaration" => process_class_member_node(node, CSharpFqnPartType::Event),
        "field_declaration" => process_class_member_node(node, CSharpFqnPartType::Field),
        _ => None,
    }
}

fn process_simple_node(
    node: &Node<StrDoc<SupportLang>>,
    node_type: CSharpFqnPartType,
) -> Option<(Range, CSharpFqnPart)> {
    let identifier_node = node.field("name")?;
    let name = identifier_node.text().into_owned();
    Some((
        node_to_range(&identifier_node),
        CSharpFqnPart {
            node_type,
            node_name: name,
            range: node_to_range(node),
            metadata: None,
        },
    ))
}

fn process_operator_declaration_node(
    node: &Node<StrDoc<SupportLang>>,
) -> Option<(Range, CSharpFqnPart)> {
    let mut children = node.children();
    // The "operator" keyword has to exist.
    children.find(|c| c.kind() == "operator")?;

    // The operation name always follows the "operator" keyword.
    let identifier_node = children.next()?;

    let name = format!("operator{}", identifier_node.text());

    Some((
        node_to_range(node),
        CSharpFqnPart {
            node_type: CSharpFqnPartType::Operator,
            node_name: name,
            range: node_to_range(node),
            metadata: None,
        },
    ))
}

fn process_indexer_declaration_node(
    node: &Node<StrDoc<SupportLang>>,
) -> Option<(Range, CSharpFqnPart)> {
    let identifier_node = node
        .children()
        .find(|c| c.kind() == "bracketed_parameter_list")?;

    Some((
        node_to_range(&identifier_node),
        CSharpFqnPart {
            node_type: CSharpFqnPartType::Indexer,
            node_name: "indexer".to_string(),
            range: node_to_range(node),
            metadata: None,
        },
    ))
}

fn process_class_member_node(
    node: &Node<StrDoc<SupportLang>>,
    node_type: CSharpFqnPartType,
) -> Option<(Range, CSharpFqnPart)> {
    let variable_declaration_node = node
        .children()
        .find(|c| c.kind() == "variable_declaration")?;
    let variable_declarator_node = variable_declaration_node
        .children()
        .find(|c| c.kind() == "variable_declarator")?;
    let identifier_node = variable_declarator_node.field("name")?;
    let name = identifier_node.text().into_owned();

    Some((
        node_to_range(&identifier_node),
        CSharpFqnPart {
            node_type,
            node_name: name,
            range: node_to_range(node),
            metadata: None,
        },
    ))
}

fn process_variable_declarator_node(
    node: &Node<StrDoc<SupportLang>>,
) -> Option<(Range, CSharpFqnPart)> {
    let part_type = if node
        .children()
        .any(|child| child.kind() == "lambda_expression")
    {
        Some(CSharpFqnPartType::Lambda)
    } else if node
        .children()
        .any(|child| child.kind() == "anonymous_object_creation_expression")
    {
        Some(CSharpFqnPartType::AnonymousType)
    } else {
        None
    };

    if let Some(part_type) = part_type {
        let identifier_node = node.field("name")?;
        let name = identifier_node.text().into_owned();

        Some((
            node_to_range(&identifier_node),
            CSharpFqnPart {
                node_type: part_type,
                node_name: name,
                range: node_to_range(node),
                metadata: None,
            },
        ))
    } else {
        None
    }
}

fn process_method_node(node: &Node<StrDoc<SupportLang>>) -> Option<(Range, CSharpFqnPart)> {
    // Check if method has static modifier
    let has_static_modifier = node
        .children()
        .any(|child| child.kind() == "modifier" && child.text().as_ref() == "static");

    let identifier_node = node.field("name")?;

    let name = identifier_node.text().into_owned();

    let node_type = if has_static_modifier {
        let is_extension_method = node
            .field("parameters")
            .and_then(|param_list| param_list.children().find(|c| c.kind() == "parameter"))
            .is_some_and(|first_param| {
                first_param
                    .children()
                    .any(|child| child.kind() == "modifier" && child.text().as_ref() == "this")
            });

        if is_extension_method {
            CSharpFqnPartType::ExtensionMethod
        } else {
            CSharpFqnPartType::StaticMethod
        }
    } else {
        CSharpFqnPartType::InstanceMethod
    };

    Some((
        node_to_range(&identifier_node),
        CSharpFqnPart {
            node_type,
            node_name: name,
            range: node_to_range(node),
            metadata: None,
        },
    ))
}

fn process_node(node: &Node<StrDoc<SupportLang>>) -> Option<(Range, CSharpFqnPart)> {
    let node_kind = node.kind();
    let kind_str = node_kind.as_ref();
    node_to_fqn_part(node, kind_str)
}

pub fn build_fqn_index(ast: &AstGrep<StrDoc<SupportLang>>) -> CSharpNodeFqnMap {
    // a map of ranges to nodes with their fqns
    let mut node_fqn_map = FxHashMap::with_capacity_and_hasher(128, Default::default());

    // a stack of fqn parts for the current scope
    let mut current_scope: ScopeStack = smallvec![];

    // a stack of nodes to process and their known fqn parts
    let mut stack: Vec<Option<Node<StrDoc<SupportLang>>>> = Vec::with_capacity(128);

    stack.push(Some(ast.root()));

    while let Some(node_option) = stack.pop() {
        if let Some(node) = node_option {
            if let Some((range, fqn_part)) = process_node(&node) {
                current_scope.push(fqn_part);

                // add a None to the stack to indicate the end of the scope
                stack.push(None);

                // add all children to the stack in reverse order
                push_children_reverse(&node, &mut stack);

                // add the fqn to the map
                node_fqn_map.insert(range, (node, Arc::new(current_scope.clone())));
            } else {
                // this node kind does not create a scope just add children to the stack in reverse order
                push_children_reverse(&node, &mut stack);
            }
        } else {
            // None indicates the end of a scope
            current_scope.pop();
        }
    }
    node_fqn_map
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::{
        LanguageParser, SupportedLanguage,
        parser::GenericParser,
        utils::{Position, Range},
    };

    fn assert_fqn_range(
        code: &str,
        name: &str,
        expected_type: CSharpFqnPartType,
        expected_range: Range,
    ) {
        let parser = GenericParser::default_for_language(SupportedLanguage::CSharp);
        let ast = parser.parse(code, None).unwrap().ast;
        let fqn_index = build_fqn_index(&ast);

        println!("--- Testing for {name} ---");
        for (range, (node, fqn)) in &fqn_index {
            println!(
                "Found FQN: {:?}, range: {:?}, node kind: {}, node text: '{}'",
                fqn.iter().map(|p| &p.node_name).collect::<Vec<_>>(),
                range,
                node.kind(),
                node.text()
            );
        }

        let found_entry = fqn_index.iter().find(|(_, (_node, fqn))| {
            if let Some(part) = fqn.last() {
                part.node_name == name && part.node_type == expected_type
            } else {
                false
            }
        });

        match found_entry {
            Some((range, (_node, fqn))) => {
                assert_eq!(
                    *range, expected_range,
                    "Range mismatch for FQN of '{name}': {fqn:?}"
                );
            }
            None => {
                panic!("FQN part with name '{name}' and type {expected_type:?} not found in index.")
            }
        }
    }

    #[test]
    fn test_namespace_fqn() {
        let code = "namespace My.Test.App { }";
        assert_fqn_range(
            code,
            "My.Test.App",
            CSharpFqnPartType::Namespace,
            Range::new(Position::new(0, 10), Position::new(0, 21), (10, 21)),
        );
    }

    #[test]
    fn test_class_fqn() {
        let code = "class MyClass { }";
        assert_fqn_range(
            code,
            "MyClass",
            CSharpFqnPartType::Class,
            Range::new(Position::new(0, 6), Position::new(0, 13), (6, 13)),
        );
    }

    #[test]
    fn test_interface_fqn() {
        let code = "interface IMyInterface { }";
        assert_fqn_range(
            code,
            "IMyInterface",
            CSharpFqnPartType::Interface,
            Range::new(Position::new(0, 10), Position::new(0, 22), (10, 22)),
        );
    }

    #[test]
    fn test_enum_fqn() {
        let code = "enum MyEnum { One, Two }";
        assert_fqn_range(
            code,
            "MyEnum",
            CSharpFqnPartType::Enum,
            Range::new(Position::new(0, 5), Position::new(0, 11), (5, 11)),
        );
    }

    #[test]
    fn test_struct_fqn() {
        let code = "struct MyStruct { }";
        assert_fqn_range(
            code,
            "MyStruct",
            CSharpFqnPartType::Struct,
            Range::new(Position::new(0, 7), Position::new(0, 15), (7, 15)),
        );
    }

    #[test]
    fn test_record_fqn() {
        let code = "public record Person(string Name);";
        assert_fqn_range(
            code,
            "Person",
            CSharpFqnPartType::Record,
            Range::new(Position::new(0, 14), Position::new(0, 20), (14, 20)),
        );
    }

    #[test]
    fn test_constructor_fqn() {
        let code = "class MyClass { public MyClass() {} }";
        assert_fqn_range(
            code,
            "MyClass",
            CSharpFqnPartType::Constructor,
            Range::new(Position::new(0, 23), Position::new(0, 30), (23, 30)),
        );
    }

    #[test]
    fn test_finalizer_fqn() {
        let code = "class MyClass { ~MyClass() {} }";
        assert_fqn_range(
            code,
            "MyClass",
            CSharpFqnPartType::Finalizer,
            Range::new(Position::new(0, 17), Position::new(0, 24), (17, 24)),
        );
    }

    #[test]
    fn test_delegate_fqn() {
        let code = "public delegate void MyDelegate(int arg);";
        assert_fqn_range(
            code,
            "MyDelegate",
            CSharpFqnPartType::Delegate,
            Range::new(Position::new(0, 21), Position::new(0, 31), (21, 31)),
        );
    }

    #[test]
    fn test_property_fqn() {
        let code = "class C { public int MyProperty { get; set; } }";
        assert_fqn_range(
            code,
            "MyProperty",
            CSharpFqnPartType::Property,
            Range::new(Position::new(0, 21), Position::new(0, 31), (21, 31)),
        );
    }

    #[test]
    fn test_instancemethod_fqn() {
        let code = "class C { void MyMethod() {} }";
        assert_fqn_range(
            code,
            "MyMethod",
            CSharpFqnPartType::InstanceMethod,
            Range::new(Position::new(0, 15), Position::new(0, 23), (15, 23)),
        );
    }

    #[test]
    fn test_staticmethod_fqn() {
        let code = "class C { static void MyStaticMethod() {} }";
        assert_fqn_range(
            code,
            "MyStaticMethod",
            CSharpFqnPartType::StaticMethod,
            Range::new(Position::new(0, 22), Position::new(0, 36), (22, 36)),
        );
    }

    #[test]
    fn test_lambda_fqn() {
        let code = "class C { Action a = () => {}; }";
        assert_fqn_range(
            code,
            "a",
            CSharpFqnPartType::Lambda,
            Range::new(Position::new(0, 17), Position::new(0, 18), (17, 18)),
        );
    }

    #[test]
    fn test_operator_fqn() {
        let code = "class C { public static C operator+(C a, C b) => new C(); }";
        assert_fqn_range(
            code,
            "operator+",
            CSharpFqnPartType::Operator,
            Range::new(Position::new(0, 10), Position::new(0, 57), (10, 57)),
        );
    }

    #[test]
    fn test_indexer_fqn() {
        let code = "class C { public int this[int index] { get; set; } }";
        assert_fqn_range(
            code,
            "indexer",
            CSharpFqnPartType::Indexer,
            Range::new(Position::new(0, 25), Position::new(0, 36), (25, 36)),
        );
    }

    #[test]
    fn test_extensionmethod_fqn() {
        let code = "static class C { public static void MyExt(this int i) {} }";
        assert_fqn_range(
            code,
            "MyExt",
            CSharpFqnPartType::ExtensionMethod,
            Range::new(Position::new(0, 36), Position::new(0, 41), (36, 41)),
        );
    }

    #[test]
    fn test_event_fqn() {
        let code = "class C { public event System.Action MyEvent; }";
        assert_fqn_range(
            code,
            "MyEvent",
            CSharpFqnPartType::Event,
            Range::new(Position::new(0, 37), Position::new(0, 44), (37, 44)),
        );
    }

    #[test]
    fn test_field_fqn() {
        let code = "class C { public int myField; }";
        assert_fqn_range(
            code,
            "myField",
            CSharpFqnPartType::Field,
            Range::new(Position::new(0, 21), Position::new(0, 28), (21, 28)),
        );
    }

    #[test]
    fn test_anonymous_type_fqn() {
        let code = "class C { void M() { var an = new { a = 1 }; } }";
        assert_fqn_range(
            code,
            "an",
            CSharpFqnPartType::AnonymousType,
            Range::new(Position::new(0, 25), Position::new(0, 27), (25, 27)),
        );
    }

    #[test]
    fn test_nested_fqn() {
        let code = r#"
namespace N {
    class C {
        void M() { }
    }
}
        "#;
        let parser = GenericParser::default_for_language(SupportedLanguage::CSharp);
        let ast = parser.parse(code, None).unwrap().ast;
        let fqn_index = build_fqn_index(&ast);

        let method_entry = fqn_index
            .iter()
            .find(|(_, (_node, fqn))| {
                if let Some(part) = fqn.last() {
                    part.node_name == "M"
                } else {
                    false
                }
            })
            .expect("Could not find method M");

        let fqn = &method_entry.1.1;
        let fqn_parts: Vec<_> = fqn.iter().map(|p| p.node_name.as_str()).collect();
        assert_eq!(fqn_parts, vec!["N", "C", "M"]);
    }
}
