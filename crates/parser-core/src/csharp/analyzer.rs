use crate::ParseResult;
use crate::analyzer::{<PERSON><PERSON><PERSON>ult, Analyzer};
use crate::csharp::definitions::{CSharpDefinitionType, find_definitions};
use crate::csharp::fqn::{CSharpFqn, build_fqn_index};
use crate::rules::MatchWithNodes;

/// Type alias for CSharp-specific analyzer
pub type CSharpAnalyzer = Analyzer<CSharpFqn, CSharpDefinitionType>;

/// Type alias for CSharp-specific analysis result
pub type CSharpAnalysisResult = AnalysisResult<CSharpFqn, CSharpDefinitionType>;

impl CSharpAnalyzer {
    /// Analyze CSharp code and extract definitions with FQN computation
    pub fn analyze(
        &self,
        matches: &[MatchWithNodes],
        parser_result: &ParseResult,
    ) -> crate::Result<CSharpAnalysisResult> {
        let fqn_index = build_fqn_index(&parser_result.ast);
        let definitions = find_definitions(matches, &fqn_index);
        let imports = Vec::new();

        Ok(CSharpAnalysisResult::new(definitions, imports))
    }
}

#[cfg(test)]
mod tests {
    use crate::{
        DefinitionLookup, LanguageParser, RuleManager, SupportedLanguage,
        csharp::analyzer::CSharpAnalyzer, parser::GenericParser, rules::run_rules,
    };

    use super::*;

    #[test]
    fn test_comprehensive_definitions_fixture() {
        let analyzer = CSharpAnalyzer::new();
        let fixture_path = "src/csharp/fixtures/ComprehensiveCSharp.cs";
        let csharp_code = std::fs::read_to_string(fixture_path)
            .expect("Should be able to read ComprehensiveCSharp.cs fixture");

        let parser = GenericParser::default_for_language(SupportedLanguage::CSharp);
        let parse_result = parser.parse(&csharp_code, Some(fixture_path)).unwrap();
        let rule_manager = RuleManager::new(SupportedLanguage::CSharp);
        let matches = run_rules(&parse_result.ast, Some(fixture_path), &rule_manager);

        let result = analyzer.analyze(&matches, &parse_result).unwrap();

        // Ensure all definitions have an FQN.
        let definitions_without_fqn: Vec<_> = result
            .definitions
            .iter()
            .filter(|d| d.fqn.is_none())
            .collect();
        assert!(
            definitions_without_fqn.is_empty(),
            "Found definitions without FQN: {definitions_without_fqn:?}"
        );

        println!(
            "ComprehensiveCSharp.cs analyzer counts: {:?}",
            result.count_definitions_by_type()
        );

        // Test that all major definition types are found
        assert!(
            !result
                .definitions_of_type(&CSharpDefinitionType::Namespace)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&CSharpDefinitionType::Class)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&CSharpDefinitionType::Interface)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&CSharpDefinitionType::Struct)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&CSharpDefinitionType::Enum)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&CSharpDefinitionType::Record)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&CSharpDefinitionType::Property)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&CSharpDefinitionType::InstanceMethod)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&CSharpDefinitionType::Constructor)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&CSharpDefinitionType::Operator)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&CSharpDefinitionType::Indexer)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&CSharpDefinitionType::Event)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&CSharpDefinitionType::Field)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&CSharpDefinitionType::AnonymousType)
                .is_empty()
        );
        assert!(
            !result
                .definitions_of_type(&CSharpDefinitionType::ExtensionMethod)
                .is_empty()
        );

        // Validate the file-scoped namespace
        validate_definition_exists(
            &result,
            "ComprehensiveLanguageFeatures",
            CSharpDefinitionType::Namespace,
        );

        // Validate interfaces with different features
        validate_definition_exists(&result, "IBasicInterface", CSharpDefinitionType::Interface);
        validate_definition_exists(
            &result,
            "IGenericInterface",
            CSharpDefinitionType::Interface,
        );
        validate_definition_exists(&result, "IModernInterface", CSharpDefinitionType::Interface);
        validate_definition_exists(&result, "INestedInterface", CSharpDefinitionType::Interface);

        // Validate enums (simple, flags, and nested)
        validate_definition_exists(&result, "SimpleEnum", CSharpDefinitionType::Enum);
        validate_definition_exists(&result, "FlagsEnum", CSharpDefinitionType::Enum);
        validate_definition_exists(&result, "NestedEnum", CSharpDefinitionType::Enum);

        // Validate structs (simple, record, readonly, ref, and nested)
        validate_definition_exists(&result, "SimpleStruct", CSharpDefinitionType::Struct);
        validate_definition_exists(&result, "ImmutablePoint", CSharpDefinitionType::Struct);
        validate_definition_exists(&result, "SpanWrapper", CSharpDefinitionType::Struct);
        validate_definition_exists(&result, "NestedStruct", CSharpDefinitionType::Struct);

        // Validate records (basic, positional, with inheritance, and custom equality)
        validate_definition_exists(&result, "Person", CSharpDefinitionType::Record);
        validate_definition_exists(&result, "Employee", CSharpDefinitionType::Record);
        validate_definition_exists(&result, "Point", CSharpDefinitionType::Record);
        validate_definition_exists(&result, "CustomRecord", CSharpDefinitionType::Record);

        // Validate classes (attribute, abstract, sealed, generic, partial, static, nested, and specialized)
        validate_definition_exists(&result, "CustomAttribute", CSharpDefinitionType::Class);
        validate_definition_exists(&result, "AbstractBase", CSharpDefinitionType::Class);
        validate_definition_exists(&result, "SealedClass", CSharpDefinitionType::Class);
        validate_definition_exists(&result, "GenericClass", CSharpDefinitionType::Class);
        validate_definition_exists(&result, "PartialClass", CSharpDefinitionType::Class);
        validate_definition_exists(&result, "FeatureDemonstration", CSharpDefinitionType::Class);
        validate_definition_exists(&result, "NestedClass", CSharpDefinitionType::Class);
        validate_definition_exists(&result, "LambdaExamples", CSharpDefinitionType::Class);
        validate_definition_exists(&result, "ExceptionExamples", CSharpDefinitionType::Class);
        validate_definition_exists(&result, "CustomException", CSharpDefinitionType::Class);
        validate_definition_exists(&result, "LinqExamples", CSharpDefinitionType::Class);
        validate_definition_exists(&result, "UnsafeExamples", CSharpDefinitionType::Class);
        validate_definition_exists(&result, "PreprocessorExamples", CSharpDefinitionType::Class);
        validate_definition_exists(&result, "Program", CSharpDefinitionType::Class);
        validate_definition_exists(&result, "FileLocalClass", CSharpDefinitionType::Class);

        // Validate properties (auto, init-only, required, computed, mixed access, static, abstract)
        validate_definition_exists(&result, "AutoProperty", CSharpDefinitionType::Property);
        validate_definition_exists(&result, "InitOnlyProperty", CSharpDefinitionType::Property);
        validate_definition_exists(&result, "RequiredProperty", CSharpDefinitionType::Property);
        validate_definition_exists(
            &result,
            "PropertyWithBackingField",
            CSharpDefinitionType::Property,
        );
        validate_definition_exists(&result, "ComputedProperty", CSharpDefinitionType::Property);
        validate_definition_exists(
            &result,
            "MixedAccessProperty",
            CSharpDefinitionType::Property,
        );
        validate_definition_exists(&result, "StaticProperty", CSharpDefinitionType::Property);
        validate_definition_exists(&result, "AbstractProperty", CSharpDefinitionType::Property);

        // Validate instance methods (interface implementations, overrides, async, generic, expression-bodied)
        validate_definition_exists(
            &result,
            "AbstractMethod",
            CSharpDefinitionType::InstanceMethod,
        );
        validate_definition_exists(&result, "Method", CSharpDefinitionType::InstanceMethod);
        validate_definition_exists(&result, "GetValue", CSharpDefinitionType::InstanceMethod);
        validate_definition_exists(&result, "SetValue", CSharpDefinitionType::InstanceMethod);
        validate_definition_exists(&result, "AsyncMethod", CSharpDefinitionType::InstanceMethod);
        validate_definition_exists(
            &result,
            "ExpressionBodiedMethod",
            CSharpDefinitionType::InstanceMethod,
        );
        validate_definition_exists(
            &result,
            "ParameterModifiers",
            CSharpDefinitionType::InstanceMethod,
        );
        validate_definition_exists(
            &result,
            "PatternMatchingExamples",
            CSharpDefinitionType::InstanceMethod,
        );

        // Validate operator and indexer definitions
        validate_definition_exists(&result, "operator+", CSharpDefinitionType::Operator);
        validate_definition_exists(&result, "indexer", CSharpDefinitionType::Indexer);

        // Validate event and field definitions
        validate_definition_exists(&result, "Event", CSharpDefinitionType::Event);
        validate_definition_exists(&result, "_backingField", CSharpDefinitionType::Field);

        // Validate anonymous type definitions
        validate_definition_exists(&result, "anonymous", CSharpDefinitionType::AnonymousType);

        // Validate static methods (entry points and utility methods)
        validate_definition_exists(&result, "Main", CSharpDefinitionType::StaticMethod);
        validate_definition_exists(&result, "MainAsync", CSharpDefinitionType::StaticMethod);
        validate_definition_exists(
            &result,
            "ExtensionMethod",
            CSharpDefinitionType::ExtensionMethod,
        );
        validate_definition_exists(
            &result,
            "StaticAbstractMethod",
            CSharpDefinitionType::StaticMethod,
        );

        // Validate constructors are found across different classes
        let constructors = result.definitions_of_type(&CSharpDefinitionType::Constructor);
        assert!(
            constructors.len() > 3,
            "Should find multiple constructors, found {}",
            constructors.len()
        );

        // Validate nested and file-local definitions
        validate_definition_exists(
            &result,
            "NestedMethod",
            CSharpDefinitionType::InstanceMethod,
        );
        validate_definition_exists(
            &result,
            "NestedInterfaceMethod",
            CSharpDefinitionType::InstanceMethod,
        );
        validate_definition_exists(
            &result,
            "FileLocalMethod",
            CSharpDefinitionType::InstanceMethod,
        );

        // Validate lambda definitions
        validate_definition_exists(&result, "square", CSharpDefinitionType::Lambda);
        validate_definition_exists(&result, "print", CSharpDefinitionType::Lambda);
        validate_definition_exists(&result, "add", CSharpDefinitionType::Lambda);

        // Validate that we found the finalizer/destructor
        assert!(
            !result
                .definitions_of_type(&CSharpDefinitionType::Finalizer)
                .is_empty()
        );
    }

    #[test]
    fn test_definition_grouping_and_filtering() {
        let analyzer = CSharpAnalyzer::new();
        let fixture_path = "src/csharp/fixtures/ComprehensiveCSharp.cs";
        let csharp_code = std::fs::read_to_string(fixture_path)
            .expect("Should be able to read ComprehensiveCSharp.cs fixture");

        let parser = GenericParser::default_for_language(SupportedLanguage::CSharp);
        let parse_result = parser.parse(&csharp_code, Some(fixture_path)).unwrap();
        let rule_manager = RuleManager::new(SupportedLanguage::CSharp);
        let matches = run_rules(&parse_result.ast, Some(fixture_path), &rule_manager);

        let result = analyzer.analyze(&matches, &parse_result).unwrap();

        // Test that all definitions have an FQN, which is critical for references.
        let definitions_without_fqn: Vec<_> = result
            .definitions
            .iter()
            .filter(|d| d.fqn.is_none())
            .collect();
        assert!(
            definitions_without_fqn.is_empty(),
            "Found definitions without FQN: {definitions_without_fqn:?}"
        );

        // Test that we can filter definitions by type and they're not empty
        for definition_type in [
            CSharpDefinitionType::Namespace,
            CSharpDefinitionType::Class,
            CSharpDefinitionType::Interface,
            CSharpDefinitionType::Struct,
            CSharpDefinitionType::Enum,
            CSharpDefinitionType::Record,
            CSharpDefinitionType::Property,
            CSharpDefinitionType::InstanceMethod,
            CSharpDefinitionType::Constructor,
            CSharpDefinitionType::Operator,
            CSharpDefinitionType::Indexer,
            CSharpDefinitionType::Event,
            CSharpDefinitionType::Field,
            CSharpDefinitionType::AnonymousType,
            CSharpDefinitionType::ExtensionMethod,
        ] {
            let defs = result.definitions_of_type(&definition_type);
            assert!(
                !defs.is_empty(),
                "Should find {definition_type:?} definitions"
            );
        }

        // Test count_definitions_by_type returns reasonable counts
        let counts = result.count_definitions_by_type();
        assert!(
            counts.get(&CSharpDefinitionType::Class).unwrap_or(&0) > &10,
            "Should find multiple classes"
        );
        assert!(
            counts
                .get(&CSharpDefinitionType::InstanceMethod)
                .unwrap_or(&0)
                > &20,
            "Should find multiple instance methods"
        );
        assert!(
            counts.get(&CSharpDefinitionType::Property).unwrap_or(&0) > &10,
            "Should find multiple properties"
        );
    }

    fn validate_definition_exists(
        result: &CSharpAnalysisResult,
        name: &str,
        expected_type: CSharpDefinitionType,
    ) {
        let defs = result.definitions_by_name(name);

        assert!(
            !defs.is_empty(),
            "Should find definition with name '{name}'"
        );

        let matching_defs: Vec<_> = defs
            .iter()
            .filter(|d| d.definition_type == expected_type)
            .collect();

        assert!(
            !matching_defs.is_empty(),
            "Should find '{}' with type {:?}, found types for this name: {:?}",
            name,
            expected_type,
            defs.iter().map(|d| d.definition_type).collect::<Vec<_>>()
        );

        for def in matching_defs {
            assert!(
                def.fqn.is_some(),
                "Definition '{name}' ({expected_type:?}) should have an FQN but doesn't: {def:?}"
            );
        }
    }
}
