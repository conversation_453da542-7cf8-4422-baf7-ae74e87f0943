id: csharp-definitions
language: CSharp
rule:
  any:
    - kind: namespace_declaration
      all:
        - has:
            kind: identifier
            field: name
            pattern: $NAMESPACE_IDENTIFIER
    - kind: file_scoped_namespace_declaration
      all:
        - has:
            kind: identifier
            field: name
            pattern: $NAMESPACE_IDENTIFIER
    - kind: interface_declaration
      all:
        - has:
            kind: identifier
            field: name
            pattern: $INTERFACE_IDENTIFIER
    - kind: class_declaration
      all:
        - has:
            kind: identifier
            field: name
            pattern: $CLASS_IDENTIFIER
    - kind: record_declaration
      all:
        - has:
            kind: identifier
            field: name
            pattern: $RECORD_IDENTIFIER
    - kind: struct_declaration
      all:
        - has:
            kind: identifier
            field: name
            pattern: $STRUCT_IDENTIFIER
    - kind: enum_declaration
      all:
        - has:
            kind: identifier
            field: name
            pattern: $ENUM_IDENTIFIER
    - kind: delegate_declaration
      all:
        - has:
            kind: identifier
            field: name
            pattern: $DELEGATE_IDENTIFIER
    - kind: property_declaration
      all:
        - has:
            kind: identifier
            field: name
            pattern: $PROPERTY_IDENTIFIER
    - kind: constructor_declaration
      all:
        - has:
            kind: identifier
            field: name
            pattern: $CONSTRUCTOR
    - kind: destructor_declaration
      all:
        - has:
            kind: identifier
            field: name
            pattern: $FINALIZER
    - kind: variable_declarator
      all:
        - has:
            kind: identifier
            pattern: $LAMBDA_LOCAL_NAME
        - has:
            kind: lambda_expression
    - kind: method_declaration
      all:
        - has:
            kind: modifier
            regex: ^static$
        - has:
            kind: identifier
            field: name
            pattern: $STATIC_METHOD_IDENTIFIER
        - not:
            has:
              kind: parameter_list
              has:
                kind: parameter
                nthChild: 1
                has:
                  kind: modifier
                  regex: ^this$
    - kind: method_declaration
      all:
        - has:
            kind: modifier
            regex: ^static$
        - has:
            kind: identifier
            field: name
            pattern: $EXTENSION_METHOD_IDENTIFIER
        - has:
            kind: parameter_list
            has:
              kind: parameter
              nthChild: 1
              has:
                kind: modifier
                regex: ^this$
    - kind: method_declaration
      all:
        - not:
            has:
              kind: modifier
              regex: ^static$
        - has:
            kind: identifier
            field: name
            pattern: $INSTANCE_METHOD_IDENTIFIER
    - kind: indexer_declaration
      all:
        - has:
            kind: bracketed_parameter_list
            pattern: $INDEXER_DECLARATION
    - kind: event_field_declaration
      all:
        - has:
            kind: variable_declaration
            has:
              kind: variable_declarator
              has:
                kind: identifier
                field: name
                pattern: $EVENT_DECLARATION
    - kind: operator_declaration
      pattern: $OPERATOR_DECLARATION_BODY
    - kind: field_declaration
      all:
        - has:
            kind: variable_declaration
            has:
              kind: variable_declarator
              has:
                kind: identifier
                field: name
                pattern: $FIELD_DECLARATION
    - kind: anonymous_object_creation_expression
      all:
        - inside:
            kind: variable_declarator
            has:
              kind: identifier
              field: name
              pattern: $ANONYMOUS_TYPE
