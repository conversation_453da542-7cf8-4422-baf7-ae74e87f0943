use crate::parser::{SupportedLanguage, load_rules_from_yaml};
use ast_grep_config::RuleConfig;
use ast_grep_language::SupportLang;
use once_cell::sync::Lazy;
use rustc_hash::FxHashMap;

pub const DEFINITIONS_YAML: &str = include_str!("./rules/definitions.yaml");

pub static CSHARP_RULES: Lazy<String> = Lazy::new(|| DEFINITIONS_YAML.to_string());

pub static RULES_CONFIG: Lazy<Vec<RuleConfig<SupportLang>>> =
    Lazy::new(|| load_rules_from_yaml(&CSHARP_RULES, SupportedLanguage::CSharp));

#[derive(Debug, PartialEq, Eq, Clone)]
pub enum CSharpMatchKind {
    Definition,
    Other(String),
}

pub static RULE_ID_KIND_MAP: Lazy<FxHashMap<&'static str, CSharpMatchKind>> = Lazy::new(|| {
    let mut m = FxHashMap::default();
    m.insert("csharp-definitions", CSharpMatchKind::Definition);
    m
});

impl CSharpMatchKind {
    pub fn from_rule_id(rule_id: &str) -> Self {
        RULE_ID_KIND_MAP
            .get(rule_id)
            .cloned()
            .unwrap_or_else(|| CSharpMatchKind::Other(rule_id.to_string()))
    }
}
