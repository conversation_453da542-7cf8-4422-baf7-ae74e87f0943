//! Generic analyzer structures for representing code analysis results across languages
//!
//! This module provides generic data structures for analyzing source code and returning
//! results with definitions. It is designed to be language-agnostic while allowing for
//! language-specific extensions through generics.

use crate::definitions::DefinitionInfo;
use crate::imports::ImportedSymbolInfo;

/// Generic analyzer for extracting definitions from source code
///
/// This analyzer is generic over the definition type and FQN type to allow
/// language-specific implementations while maintaining a common interface.
pub struct Analyzer<FqnType, DefinitionType = (), ImportType = ()> {
    _phantom: std::marker::PhantomData<(FqnType, DefinitionType, ImportType)>,
}

/// Generic result of analyzing source code
///
/// Contains all definitions found during analysis with methods for filtering
/// and accessing the results.
pub struct AnalysisResult<FqnType, DefinitionType = (), ImportType = ()> {
    /// All definitions found in the code
    pub definitions: Vec<DefinitionInfo<DefinitionType, FqnType>>,
    /// All imported symbols found in the code
    pub imports: Vec<ImportedSymbolInfo<ImportType, FqnType>>,
}

impl<FqnType, DefinitionType, ImportType> Analyzer<DefinitionType, FqnType, ImportType> {
    /// Create a new analyzer
    pub fn new() -> Self {
        Self {
            _phantom: std::marker::PhantomData,
        }
    }
}

impl<FqnType, DefinitionType, ImportType> Default
    for Analyzer<FqnType, DefinitionType, ImportType>
{
    fn default() -> Self {
        Self::new()
    }
}

impl<FqnType, DefinitionType, ImportType> AnalysisResult<FqnType, DefinitionType, ImportType>
where
    DefinitionType: Clone + PartialEq + Eq + std::hash::Hash,
    ImportType: Clone + PartialEq + Eq + std::hash::Hash,
{
    /// Create a new analysis result
    pub fn new(
        definitions: Vec<DefinitionInfo<DefinitionType, FqnType>>,
        imports: Vec<ImportedSymbolInfo<ImportType, FqnType>>,
    ) -> Self {
        Self {
            definitions,
            imports,
        }
    }

    /// Get FQN strings for all definitions that have them
    /// Takes a function to convert FQN to string representation
    pub fn definition_fqn_strings<ToString>(&self, fqn_to_string: ToString) -> Vec<String>
    where
        ToString: Fn(&FqnType) -> String,
    {
        self.definitions
            .iter()
            .filter_map(|def| def.fqn.as_ref().map(&fqn_to_string))
            .collect()
    }
}
