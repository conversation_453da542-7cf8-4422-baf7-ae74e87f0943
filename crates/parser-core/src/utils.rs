//! Common utilities and types for the parser core

use ast_grep_core::Node;
use ast_grep_core::tree_sitter::StrDoc;
use ast_grep_language::SupportLang;
use serde::{Deserialize, Serialize};

/// Represents a position in source code (line, column)
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct Position {
    pub line: usize,
    pub column: usize,
}

impl Position {
    pub const fn new(line: usize, column: usize) -> Self {
        Self { line, column }
    }
}

/// Represents a range in source code (start and end positions)
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct Range {
    pub start: Position,
    pub end: Position,
    pub byte_offset: (usize, usize),
}

impl Range {
    pub const fn new(start: Position, end: Position, byte_offset: (usize, usize)) -> Self {
        Self {
            start,
            end,
            byte_offset,
        }
    }

    pub fn empty() -> Self {
        Self {
            start: Position::new(0, 0),
            end: Position::new(0, 0),
            byte_offset: (0, 0),
        }
    }

    /// Check if a position is within this range
    pub fn contains(&self, pos: &Position) -> bool {
        use std::cmp::Ordering;

        let starts_before_or_eq = match self.start.line.cmp(&pos.line) {
            Ordering::Less => true,
            Ordering::Equal => self.start.column <= pos.column,
            Ordering::Greater => false,
        };

        let ends_after_or_eq = match self.end.line.cmp(&pos.line) {
            Ordering::Greater => true,
            Ordering::Equal => self.end.column >= pos.column,
            Ordering::Less => false,
        };

        starts_before_or_eq && ends_after_or_eq
    }

    /// Get the size of the range in lines
    pub const fn line_span(&self) -> usize {
        self.end.line.saturating_sub(self.start.line) + 1
    }

    /// Get the byte length of the range
    pub const fn byte_length(&self) -> usize {
        self.byte_offset.1.saturating_sub(self.byte_offset.0)
    }
}

pub fn compare_positions(p1: &Position, p2: &Position) -> std::cmp::Ordering {
    p1.line
        .cmp(&p2.line)
        .then_with(|| p1.column.cmp(&p2.column))
}

/// Convert a tree-sitter node to a Range
pub fn node_to_range(node: &Node<StrDoc<SupportLang>>) -> Range {
    let start_pos = node.start_pos();
    let end_pos = node.end_pos();
    let byte_range = node.range();

    Range::new(
        Position::new(start_pos.line(), start_pos.column(node)),
        Position::new(end_pos.line(), end_pos.column(node)),
        (byte_range.start, byte_range.end),
    )
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_position_creation() {
        let pos = Position::new(5, 10);
        assert_eq!(pos.line, 5);
        assert_eq!(pos.column, 10);
    }

    #[test]
    fn test_range_contains() {
        let range = Range::new(Position::new(1, 0), Position::new(3, 10), (0, 50));

        assert!(range.contains(&Position::new(2, 5)));
        assert!(range.contains(&Position::new(1, 0)));
        assert!(range.contains(&Position::new(3, 10)));
        assert!(!range.contains(&Position::new(0, 5)));
        assert!(!range.contains(&Position::new(4, 0)));
    }

    #[test]
    fn test_range_metrics() {
        let range = Range::new(Position::new(1, 0), Position::new(3, 10), (0, 50));

        assert_eq!(range.line_span(), 3);
        assert_eq!(range.byte_length(), 50);
    }

    #[test]
    fn test_compare_positions() {
        let p1 = Position::new(1, 5);
        let p2 = Position::new(2, 3);
        let p3 = Position::new(1, 10);

        assert_eq!(compare_positions(&p1, &p2), std::cmp::Ordering::Less);
        assert_eq!(compare_positions(&p2, &p1), std::cmp::Ordering::Greater);
        assert_eq!(compare_positions(&p1, &p3), std::cmp::Ordering::Less);
        assert_eq!(compare_positions(&p1, &p1), std::cmp::Ordering::Equal);
    }

    #[test]
    fn test_range_contains_edge_cases() {
        let range = Range::new(Position::new(2, 5), Position::new(2, 15), (10, 20));

        // Test same line range
        assert!(range.contains(&Position::new(2, 5))); // start boundary
        assert!(range.contains(&Position::new(2, 10))); // middle
        assert!(range.contains(&Position::new(2, 15))); // end boundary
        assert!(!range.contains(&Position::new(2, 4))); // before start
        assert!(!range.contains(&Position::new(2, 16))); // after end
        assert!(!range.contains(&Position::new(1, 10))); // line before
        assert!(!range.contains(&Position::new(3, 10))); // line after
    }

    #[test]
    fn test_const_functions() {
        // Test that const functions work at compile time
        const POS: Position = Position::new(1, 2);
        const RANGE: Range = Range::new(POS, Position::new(3, 4), (0, 10));

        assert_eq!(POS.line, 1);
        assert_eq!(POS.column, 2);
        assert_eq!(RANGE.line_span(), 3);
        assert_eq!(RANGE.byte_length(), 10);
    }
}
