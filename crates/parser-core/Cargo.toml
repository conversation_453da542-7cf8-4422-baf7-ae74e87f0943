[package]
name = "parser-core"
version = "0.5.0"
edition = "2024"

[lib]
path = "src/lib.rs"

[dependencies]
ast-grep-core = { workspace = true }
ast-grep-config = { workspace = true }
ast-grep-language = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
once_cell = { workspace = true }
tracing = { workspace = true }
toml = { workspace = true }
thiserror = { workspace = true }
paste = { workspace = true }
rustc-hash = { workspace = true }
smallvec = { workspace = true }

[dev-dependencies]
criterion = { workspace = true }
flamegraph = { workspace = true }
tempfile = { workspace = true }

# Criterion benchmarks
[[bench]]
name = "parser_benchmark_ruby_e2e"
path = "benches/criterion/parser_benchmark_ruby_e2e.rs"
harness = false

[[bench]]
name = "parser_benchmark_kotlin_e2e"
path = "benches/criterion/parser_benchmark_kotlin_e2e.rs"
harness = false

[[bench]]
name = "parser_benchmark_java_e2e"
path = "benches/criterion/parser_benchmark_java_e2e.rs"
harness = false

[[bench]]
name = "parser_benchmark_csharp_e2e"
path = "benches/criterion/parser_benchmark_csharp_e2e.rs"
harness = false

# Flamegraph benchmarks
[[bench]]
name = "parser_flamegraph_ruby"
path = "benches/flamegraph/parser_flamegraph_ruby.rs"
harness = false

[[bench]]
name = "parser_flamegraph_kotlin"
path = "benches/flamegraph/parser_flamegraph_kotlin.rs"
harness = false

[[bench]]
name = "parser_flamegraph_java"
path = "benches/flamegraph/parser_flamegraph_java.rs"
harness = false

[[bench]]
name = "parser_flamegraph_csharp"
path = "benches/flamegraph/parser_flamegraph_csharp.rs"
harness = false
