# Chunker

A Rust crate for intelligently chunking source code.

## Overview

The chunker crate provides smart algorithms to split source code into meaningful segments. It uses syntax-aware chunking with a size-based fallback for reliable processing of any code.

## Usage

```rust
use chunker::{<PERSON><PERSON><PERSON>hunker, SplitCodeChunker, StrChunker};

const MAX_CHUNK_SIZE: usize = 1024;
const CHUNK_OVERLAP: usize = 0;

fn main() {
    // Create a fallback chunker for when syntax parsing fails
    let fallback = Box::new(SizeChunker::new(MAX_CHUNK_SIZE, CHUNK_OVERLAP).unwrap());
    
    // Create the main chunker with fallback strategy
    let chunker = SplitCodeChunker::new(MAX_CHUNK_SIZE).with_fallback(fallback);
    
    let source_code = "fn main() { println!(\"Hello, world!\"); }";
    let chunks = chunker.chunk_string(source_code, "main.rs").unwrap();
    
    for chunk in chunks {
        println!("Chunk: {}", chunk.content(source_code));
    }
}
```
