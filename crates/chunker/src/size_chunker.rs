use crate::types::{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>r<PERSON><PERSON><PERSON>};

/// `SizeChunker` divides source code into chunks of approximately equal size while attempting to
/// break at reasonable boundaries (line breaks) to avoid splitting tokens or strings.
pub struct SizeChunker {
    max_chunk_size: usize,
    chunk_overlap: usize,
}

impl SizeChunker {
    pub fn new(max_chunk_size: usize, chunk_overlap: usize) -> Result<Self, ChunkError> {
        if chunk_overlap >= max_chunk_size {
            return Err(ChunkError::InvalidOptions(
                "chunk overlap must be less than chunk size".to_string(),
            ));
        }

        Ok(Self {
            max_chunk_size,
            chunk_overlap,
        })
    }
}

impl StrChunker for SizeChunker {
    fn chunk_string<'a>(
        &self,
        source_code: &str,
        file_path: &'a str,
    ) -> Result<Vec<Chunk<'a>>, ChunkError> {
        let mut chunks = Vec::new();
        let mut start_byte = 0;
        let total_length = source_code.len();

        loop {
            if start_byte >= total_length {
                break;
            }

            let remaining_length = total_length - start_byte;
            if remaining_length <= self.max_chunk_size {
                chunks.push(Chunk {
                    file_path,
                    start_byte,
                    end_byte: total_length,
                    language: None,
                });
                break;
            }

            let mut end_byte = start_byte + self.max_chunk_size;

            if let Some(last_newline_post) = &source_code[start_byte..end_byte].rfind('\n') {
                end_byte = start_byte + last_newline_post + 1;
            }

            chunks.push(Chunk {
                file_path,
                start_byte,
                end_byte,
                language: None,
            });

            if self.chunk_overlap > 0 {
                let chunk_length = end_byte - start_byte;
                let overlap = self.chunk_overlap.min(chunk_length);
                start_byte = end_byte - overlap;
            } else {
                start_byte = end_byte;
            }
        }

        Ok(chunks)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    const SIZE_CHUNKER_TEST_CONTENT: &str = r#"This is a test content for chunking.
We will split this content into multiple chunks of a certain size.
We will use the simple size chunker.

This is not meant for testing with a parser,
since the parser is specifically for code content.

The simple chunker should split this content given a chunk size.
If the split does not occur at a new line, the simple chunker
will try to split at the last new line index.
"#;

    const SIZE_CHUNKER_TEST_CHUNK1: &str = r#"This is a test content for chunking.
We will split this content into multiple chunks of a certain size.
We will use the simple size chunker.

This is not meant for testing with a parser,
"#;

    const SIZE_CHUNKER_TEST_CHUNK2: &str = r#"since the parser is specifically for code content.

The simple chunker should split this content given a chunk size.
If the split does not occur at a new line, the simple chunker
"#;

    const SIZE_CHUNKER_TEST_CHUNK3: &str = r#"will try to split at the last new line index.
"#;

    #[test]
    fn test_new_with_too_large_chunk_overlap() {
        let result = SizeChunker::new(10, 10);

        assert!(result.is_err());
    }

    #[test]
    fn test_chunk_string() {
        let chunker = match SizeChunker::new(200, 0) {
            Ok(chunker) => chunker,
            Err(e) => panic!("unexpected error = {e}"),
        };

        let result = chunker.chunk_string(SIZE_CHUNKER_TEST_CONTENT, "test-file.txt");
        let chunks = match result {
            Ok(chunks) => chunks,
            Err(e) => panic!("unexpected error = {e}"),
        };

        assert_eq!(3, chunks.len());

        let (chunk1, chunk2, chunk3) = (&chunks[0], &chunks[1], &chunks[2]);

        assert_eq!(
            SIZE_CHUNKER_TEST_CHUNK1,
            chunk1.content(SIZE_CHUNKER_TEST_CONTENT)
        );
        assert_eq!(0, chunk1.start_byte);

        assert_eq!(
            SIZE_CHUNKER_TEST_CHUNK2,
            chunk2.content(SIZE_CHUNKER_TEST_CONTENT)
        );
        assert_eq!(SIZE_CHUNKER_TEST_CHUNK1.len(), chunk2.start_byte);

        assert_eq!(
            SIZE_CHUNKER_TEST_CHUNK3,
            chunk3.content(SIZE_CHUNKER_TEST_CONTENT)
        );
        assert_eq!(
            SIZE_CHUNKER_TEST_CHUNK1.len() + SIZE_CHUNKER_TEST_CHUNK2.len(),
            chunk3.start_byte
        );
    }

    #[test]
    fn test_chunk_string_with_overlap() {
        let source_code = "1234567890";
        let chunker = match SizeChunker::new(5, 1) {
            Ok(chunker) => chunker,
            Err(e) => panic!("unexpected error = {e}"),
        };

        let result = chunker.chunk_string(source_code, "test-file.txt");
        let chunks = match result {
            Ok(chunks) => chunks,
            Err(e) => panic!("unexpected error = {e}"),
        };

        assert_eq!(3, chunks.len());

        let (chunk1, chunk2, chunk3) = (&chunks[0], &chunks[1], &chunks[2]);

        assert_eq!("12345", chunk1.content(source_code));
        assert_eq!(0, chunk1.start_byte);

        assert_eq!("56789", chunk2.content(source_code));
        assert_eq!(4, chunk2.start_byte);

        assert_eq!("90", chunk3.content(source_code));
        assert_eq!(8, chunk3.start_byte);
    }
}
