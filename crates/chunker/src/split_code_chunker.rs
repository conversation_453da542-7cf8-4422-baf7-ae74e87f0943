use crate::types::{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>};
use ast_grep_core::{Node, tree_sitter::StrDoc};
use ast_grep_language::{Language, LanguageExt, SupportLang};
use std::path::Path;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
struct SplitPoint {
    position: usize,
}

/// `SplitCodeChunker` analyzes source code to identify natural break points such as
/// function boundaries, class definitions, and other syntactic structures. This approach
/// ensures that chunks maintain semantic coherence and don't break in the middle of
/// logical code units.
pub struct SplitCodeChunker {
    max_chunk_size: usize,
    fallback: Option<Box<dyn StrChunker>>,
}

impl SplitCodeChunker {
    pub fn new(max_chunk_size: usize) -> Self {
        Self {
            max_chunk_size,
            fallback: None,
        }
    }

    pub fn with_fallback(mut self, fallback: Box<dyn StrChunker>) -> Self {
        self.fallback = Some(fallback);
        self
    }

    fn chunk_ast_grep<'a>(
        &self,
        root: &Node<StrDoc<SupportLang>>,
        source_code: &str,
        file_path: &'a str,
    ) -> Result<Vec<Chunk<'a>>, ChunkError> {
        let split_points = self.find_split_points_ast_grep(root);

        let mut chunks = Vec::new();
        let mut start_split = SplitPoint { position: 0 };
        let mut last_split = SplitPoint { position: 0 };

        for split in split_points {
            let chunk_size = split.position - start_split.position;

            if chunk_size > self.max_chunk_size {
                if start_split.position == last_split.position {
                    match self.fallback.as_ref() {
                        Some(fallback) => {
                            let fallback_chunks = fallback.chunk_string(
                                &source_code[start_split.position..split.position],
                                file_path,
                            )?;

                            chunks.extend(fallback_chunks.into_iter().map(|mut c| {
                                c.start_byte += start_split.position;
                                c.end_byte += start_split.position;
                                c.language = Some(*root.lang());
                                c
                            }));

                            start_split = split.clone();
                            last_split = split;
                            continue;
                        }
                        None => return Err(ChunkError::NoSuitableSplitPoints),
                    }
                }

                let chunk = Chunk {
                    file_path,
                    start_byte: start_split.position,
                    end_byte: last_split.position,
                    language: Some(*root.lang()),
                };

                chunks.push(chunk);

                start_split = last_split;
            }

            last_split = split;
        }

        if start_split.position < source_code.len() {
            let chunk_size = source_code.len() - start_split.position;
            if chunk_size > self.max_chunk_size {
                return Err(ChunkError::NoSuitableSplitPoints);
            }

            let last_chunk = Chunk {
                file_path,
                start_byte: start_split.position,
                end_byte: source_code.len(),
                language: Some(*root.lang()),
            };

            chunks.push(last_chunk);
        }

        Ok(chunks)
    }

    fn find_split_points_ast_grep(&self, root: &Node<StrDoc<SupportLang>>) -> Vec<SplitPoint> {
        let mut split_points = Vec::new();

        for node in root.dfs() {
            let node_kind = node.kind();
            let kind_str = &*node_kind;

            match kind_str {
                // Top-level declarations
                "function_declaration"
                | "method_declaration"
                | "class_declaration"
                | "module_declaration"
                | "struct_type_declaration"
                | "interface_declaration"
                | "function_item"
                | "struct_item"
                | "impl_item"
                | "trait_item"
                | "enum_item"
                | "mod_item"
                | "type_item"
                | "const_item"
                | "static_item"
                | "function_definition"
                | "class_definition"
                | "method_definition"
                | "function"
                | "class"
                | "method"
                | "interface"
                | "namespace"
                | "module" => {
                    split_points.push(SplitPoint {
                        position: node.range().start,
                    });
                    split_points.push(SplitPoint {
                        position: node.range().end,
                    });
                }

                // Import/require statements
                "import_declaration" | "import_statement" | "require_statement"
                | "use_declaration" | "extern_crate_item" | "import" | "include" => {
                    // Good to split after imports section
                    split_points.push(SplitPoint {
                        position: node.range().end,
                    });
                }

                // Variable and constant declarations
                "var_declaration"
                | "const_declaration"
                | "variable_declaration"
                | "let_declaration"
                | "variable_declarator" => {
                    split_points.push(SplitPoint {
                        position: node.range().end,
                    });
                }

                // Control flow statements
                "if_statement" | "for_statement" | "while_statement" | "switch_statement"
                | "case_statement" | "do_statement" | "begin_block" | "if_expression"
                | "while_expression" | "for_expression" | "loop_expression"
                | "match_expression" | "try_statement" | "with_statement" | "foreach_statement" => {
                    split_points.push(SplitPoint {
                        position: node.range().end,
                    });
                }

                // Comments often indicate logical breaks
                "comment" | "line_comment" | "block_comment" => {
                    split_points.push(SplitPoint {
                        position: node.range().start,
                    });
                }

                // Basic statements
                "expression_statement" | "assignment_expression" | "return_statement" => {
                    split_points.push(SplitPoint {
                        position: node.range().end,
                    });
                }

                _ => {
                    // Ignore anything else
                }
            }
        }

        split_points.sort_by_key(|sp| sp.position);

        split_points
    }
}

impl StrChunker for SplitCodeChunker {
    fn chunk_string<'a>(
        &self,
        source_code: &str,
        file_path: &'a str,
    ) -> Result<Vec<Chunk<'a>>, ChunkError> {
        if source_code.is_empty() {
            return Ok(vec![]);
        }

        let path = Path::new(file_path);
        match SupportLang::from_path(path) {
            Some(supported_lang) => {
                let ast_grep = supported_lang.ast_grep(source_code);
                let root = ast_grep.root();

                self.chunk_ast_grep(&root, source_code, file_path)
            }
            None => match self.fallback.as_ref() {
                Some(fallback) => fallback.chunk_string(source_code, file_path),
                None => Err(ChunkError::UnsupportedLanguage(file_path.to_string())),
            },
        }
    }
}

#[cfg(test)]
mod tests {
    use ast_grep_language::{Python, Ruby, Rust};

    use crate::size_chunker::SizeChunker;

    use super::*;

    const TEST_CHUNK_SIZE: usize = 50;

    fn create_code_chunker() -> SplitCodeChunker {
        SplitCodeChunker::new(TEST_CHUNK_SIZE)
    }

    fn assert_chunk_success(
        result: Result<Vec<Chunk>, ChunkError>,
        source_code: &str,
        language: Option<SupportLang>,
    ) {
        let chunks: Vec<Chunk> = match result {
            Ok(c) => c,
            Err(e) => {
                panic!("e = {e}");
            }
        };

        assert!(!chunks.is_empty());

        let mut reconstructed = String::from("");

        for chunk in chunks {
            let content = chunk.content(source_code);
            assert!(
                (chunk.end_byte - chunk.start_byte) <= TEST_CHUNK_SIZE,
                "start_byte = {}, end_byte = {}, content = {}",
                chunk.start_byte,
                chunk.end_byte,
                content
            );
            assert_eq!(language, chunk.language);

            reconstructed.push_str(content);
        }

        assert_eq!(reconstructed, source_code);
    }

    #[test]
    fn test_chunk_string_unknown_language() {
        let chunker = create_code_chunker();
        let source_code = "1234567890";
        let file_path = "unknown_language";

        let result = chunker.chunk_string(source_code, file_path);
        match result {
            Ok(..) => panic!("expected an error"),
            Err(e) => assert!(matches!(e, ChunkError::UnsupportedLanguage(..))),
        }
    }

    #[test]
    fn test_chunk_string_unknown_language_with_fallback() {
        let fallback = Box::new(SizeChunker::new(TEST_CHUNK_SIZE, 0).unwrap());
        let chunker = create_code_chunker().with_fallback(fallback);

        let source_code = "1234567890";
        let file_path = "unknown_language";

        let result = chunker.chunk_string(source_code, file_path);
        assert_chunk_success(result, source_code, None);
    }

    #[test]
    fn test_chunk_string_too_large_chunk() {
        let chunker = create_code_chunker();
        let source_code =
            "fn main() {\n    println!(\"Hell".to_owned() + &"o".repeat(100) + "\");\n}";
        let file_path = "large.rs";

        let result = chunker.chunk_string(&source_code, file_path);
        match result {
            Ok(..) => panic!("expected an error"),
            Err(e) => assert!(matches!(e, ChunkError::NoSuitableSplitPoints)),
        }
    }

    #[test]
    fn test_chunk_string_too_large_chunk_with_fallback() {
        let fallback = Box::new(SizeChunker::new(TEST_CHUNK_SIZE, 0).unwrap());
        let chunker = create_code_chunker().with_fallback(fallback);

        let source_code =
            "fn main() {\n    println!(\"Hell".to_owned() + &"o".repeat(100) + "\");\n}";
        let file_path = "large.rs";

        let result = chunker.chunk_string(&source_code, file_path);
        assert_chunk_success(result, &source_code, Some(Rust.into()));
    }

    #[test]
    fn test_chunk_string_rust() {
        let chunker = create_code_chunker();
        let source_code = r#"
fn main() {
    println!("Hello, world!");
}
"#;
        let file_path = "main.rs";

        let result = chunker.chunk_string(source_code, file_path);
        assert_chunk_success(result, source_code, Some(Rust.into()));
    }

    #[test]
    fn test_chunk_string_ruby() {
        let chunker = create_code_chunker();
        let source_code = r#"
module Thing
  class Foo
    def bar
      puts("Hello, world!")
    end
  end
end
"#;
        let file_path = "main.rb";

        let result = chunker.chunk_string(source_code, file_path);
        assert_chunk_success(result, source_code, Some(Ruby.into()));
    }

    #[test]
    fn test_chunk_string_python() {
        let chunker = create_code_chunker();
        let source_code = r#"
def main():
    print("Hello, World!")

if __name__ == "__main__":
    main()
"#;
        let file_path = "main.py";

        let result = chunker.chunk_string(source_code, file_path);
        assert_chunk_success(result, source_code, Some(Python.into()));
    }
}
