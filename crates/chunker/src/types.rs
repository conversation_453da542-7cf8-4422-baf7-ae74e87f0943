use ast_grep_language::SupportLang;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct Chunk<'a> {
    pub file_path: &'a str,
    pub start_byte: usize,
    pub end_byte: usize,
    pub language: Option<SupportLang>,
}

impl<'a> Chunk<'a> {
    pub fn content<'b>(&self, source_code: &'b str) -> &'b str {
        &source_code[self.start_byte..self.end_byte]
    }
}

#[derive(Debug)]
pub enum ChunkError {
    ParseError(String),
    NoSuitableSplitPoints,
    UnsupportedLanguage(String),
    InvalidOptions(String),
}

impl std::fmt::Display for ChunkError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ChunkError::ParseError(e) => write!(f, "Parse error: {e}"),
            ChunkError::NoSuitableSplitPoints => {
                write!(
                    f,
                    "No suitable split points found for code chunk exceeding max size"
                )
            }
            ChunkError::UnsupportedLanguage(lang) => write!(f, "Unsupported language: {lang}"),
            ChunkError::InvalidOptions(msg) => write!(f, "Invalid options: {msg}"),
        }
    }
}

impl std::error::Error for ChunkError {}

pub trait StrChunker {
    fn chunk_string<'a>(
        &self,
        source_code: &str,
        file_path: &'a str,
    ) -> Result<Vec<Chunk<'a>>, ChunkError>;
}
