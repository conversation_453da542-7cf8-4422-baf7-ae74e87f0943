[package]
name = "chunker"
version = "0.5.0"
edition = "2024"

[lib]
path = "src/lib.rs"

[dependencies]
ast-grep-core = { workspace = true }
ast-grep-language = { workspace = true }

[dev-dependencies]
criterion = { workspace = true }
flamegraph = { workspace = true }

[[bench]]
name = "split_code_chunker_benchmark"
path = "benches/criterion/split_code_chunker_benchmark.rs"
harness = false

[[bench]]
name = "split_code_chunker_flamegraph"
path = "benches/flamegraph/split_code_chunker_flamegraph.rs"
harness = false
