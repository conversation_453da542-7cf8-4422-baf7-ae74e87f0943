use crate::utils::{execute_command_with_desc, get_bench_targets};
use anyhow::{Context, Result, anyhow};
use std::fs;
use std::path::{Path, PathBuf};
use std::process::Command;
use toml::Value;
use tracing::{info, warn};

struct BenchmarkPaths {
    package_cargo_toml_path: PathBuf,
}

pub fn run_criterion(
    package_name: &str,
    commit_branch_opt: &Option<String>,
    default_branch_opt: &Option<String>,
    skip_stable_toolchain: bool,
) -> Result<()> {
    let paths = setup_environment_and_paths(package_name, skip_stable_toolchain)?;

    let criterion_benches =
        get_criterion_benches_from_package_toml(package_name, &paths.package_cargo_toml_path)?;

    if criterion_benches.is_empty() {
        warn!(
            "No Criterion benchmarks found for package {}. Ensure they are defined in {} and their path does not include 'flamegraph'.",
            package_name,
            paths.package_cargo_toml_path.display()
        );
        return Ok(());
    }

    info!(
        "Found Criterion benchmarks for package '{}': {:?}",
        package_name, criterion_benches
    );

    let determined_default_branch_str = default_branch_opt.as_deref();
    let is_main_branch = match (commit_branch_opt.as_deref(), determined_default_branch_str) {
        (Some(commit), Some(default_val)) if !default_val.is_empty() => commit == default_val,
        _ => {
            info!(
                "Commit branch or default branch not provided, or default branch is empty. Assuming not main branch for benchmark logic."
            );
            false
        }
    };

    let baseline_name = "main";
    let mut benchmarks_ran_successfully_count = 0;

    for benchmark_name in &criterion_benches {
        if process_single_benchmark(
            package_name,
            benchmark_name,
            is_main_branch,
            baseline_name,
            commit_branch_opt.as_deref(),
            determined_default_branch_str,
        )? {
            benchmarks_ran_successfully_count += 1;
        }
    }

    if benchmarks_ran_successfully_count == 0 && !criterion_benches.is_empty() {
        warn!(
            "No Criterion benchmarks were successfully run for package {}.",
            package_name
        );
    } else if benchmarks_ran_successfully_count > 0 {
        info!(
            "Successfully processed {} Criterion benchmark(s) for package {}.",
            benchmarks_ran_successfully_count, package_name
        );
    }

    Ok(())
}

fn setup_environment_and_paths(
    package_name: &str,
    skip_stable_toolchain: bool,
) -> Result<BenchmarkPaths> {
    if !skip_stable_toolchain {
        info!("Ensuring stable toolchain...");
        execute_command_with_desc(
            "rustup",
            &["default", "stable"],
            "set rustup default to stable",
        )?;
    }

    let manifest_dir = PathBuf::from(std::env::var("CARGO_MANIFEST_DIR")?);
    let workspace_root_ref = manifest_dir
        .parent()
        .and_then(std::path::Path::parent)
        .ok_or_else(|| anyhow!("Failed to find workspace root from xtask"))?;
    let workspace_root = workspace_root_ref.to_path_buf();
    info!("Workspace root: {}", workspace_root.display());

    let package_cargo_toml_path = workspace_root
        .join("crates")
        .join(package_name)
        .join("Cargo.toml");

    if !package_cargo_toml_path.exists() {
        return Err(anyhow!(
            "Package Cargo.toml not found at {}",
            package_cargo_toml_path.display()
        ));
    }
    info!(
        "Using package Cargo.toml: {}",
        package_cargo_toml_path.display()
    );

    Ok(BenchmarkPaths {
        package_cargo_toml_path,
    })
}

fn get_criterion_benches_from_package_toml(
    package_name: &str,
    package_cargo_toml_path: &Path,
) -> Result<Vec<String>> {
    let cargo_toml_content = fs::read_to_string(package_cargo_toml_path).with_context(|| {
        format!(
            "Failed to read package Cargo.toml at {}",
            package_cargo_toml_path.display()
        )
    })?;
    let toml_value = cargo_toml_content
        .parse::<Value>()
        .with_context(|| "Failed to parse package Cargo.toml")?;

    get_bench_targets(&toml_value, package_name, |path| {
        !path.contains("flamegraph")
    })
    .with_context(|| format!("Failed to get criterion bench targets for package {package_name}"))
}

fn run_benchmark_without_baseline(
    package_name: &str,
    benchmark_name: &str,
    reason: &str,
) -> Result<bool> {
    let action_desc = format!(
        "cargo bench for benchmark {benchmark_name} (no baseline after {reason})
"
    );
    match execute_command_with_desc(
        "cargo",
        &[
            "bench",
            "--package",
            package_name,
            "--bench",
            benchmark_name,
        ],
        &action_desc,
    ) {
        Ok(_) => Ok(true),
        Err(e) => {
            warn!(
                "Failed to run benchmark {} without baseline (reason: {}): {:?}. Skipping.",
                benchmark_name, reason, e
            );
            Ok(false)
        }
    }
}

fn process_single_benchmark(
    package_name: &str,
    benchmark_name: &str,
    is_main_branch: bool,
    baseline_name: &str,
    commit_branch_for_log: Option<&str>,
    default_branch_for_log: Option<&str>,
) -> Result<bool> {
    info!("Processing Criterion benchmark: {}", benchmark_name);
    if is_main_branch {
        info!(
            "On default branch ({}). Saving baseline '{}' for benchmark '{}' in package '{}'.",
            default_branch_for_log.unwrap_or("<unknown_default_branch>"),
            baseline_name,
            benchmark_name,
            package_name
        );
        let action_desc =
            format!("cargo bench --save-baseline {baseline_name} for benchmark {benchmark_name}");
        match execute_command_with_desc(
            "cargo",
            &[
                "bench",
                "--package",
                package_name,
                "--bench",
                benchmark_name,
                "--",
                "--save-baseline",
                baseline_name,
            ],
            &action_desc,
        ) {
            Ok(_) => Ok(true),
            Err(e) => {
                warn!(
                    "Failed to save baseline for benchmark {}: {:?}. Skipping.",
                    benchmark_name, e
                );
                Ok(false)
            }
        }
    } else {
        info!(
            "Not on default branch (current: {}). Attempting to compare with baseline '{}' for benchmark '{}' in package '{}'.",
            commit_branch_for_log.unwrap_or("<unknown_commit_branch>"),
            baseline_name,
            benchmark_name,
            package_name
        );

        let baseline_args = [
            "bench",
            "--package",
            package_name,
            "--bench",
            benchmark_name,
            "--",
            "--baseline",
            baseline_name,
        ];

        let mut cmd = Command::new("cargo");
        cmd.args(baseline_args);
        info!("Executing comparison: cargo {}", baseline_args.join(" "));

        match cmd.status() {
            Ok(status) if status.success() => {
                info!(
                    "Successfully compared benchmark '{}' with baseline '{}'.",
                    benchmark_name, baseline_name
                );
                Ok(true)
            }
            Ok(status) => {
                info!(
                    "Comparison for benchmark '{}' with baseline '{}' exited with status: {}. Running benchmark without comparison.",
                    benchmark_name, baseline_name, status
                );
                run_benchmark_without_baseline(package_name, benchmark_name, "comparison failed")
            }
            Err(e) => {
                info!(
                    "Failed to execute comparison for benchmark '{}' with baseline '{}': {}. Running benchmark without comparison.",
                    benchmark_name, baseline_name, e
                );
                run_benchmark_without_baseline(package_name, benchmark_name, "comparison error")
            }
        }
    }
}
