use std::fs;
use std::path::{Path, PathBuf};
mod artifacts;
mod criterion;
mod flamegraph;
use anyhow::{Context, anyhow};
use clap::Parser;
use criterion::run_criterion;
use ignore::WalkBuilder;
use std::sync::Mutex;
use toml::Value;
use tracing::{error, info};
mod utils;
use artifacts::fetcher::GitLabArtifactFetcher;
use flamegraph::run_flamegraph;

#[derive(Parser, Debug)]
#[clap(author, version, about, long_about = None)]
struct Cli {
    #[clap(subcommand)]
    command: Commands,
}

#[derive(Parser, Debug)]
enum Commands {
    /// Checks that all Cargo.toml versions match the VERSION file.
    CheckVersions,
    /// Runs the benchmark suite.
    RunCriterion {
        /// The name of the package to benchmark.
        #[clap(long)]
        package_name: String,
        /// The current commit branch.
        #[clap(long)]
        commit_branch: Option<String>,
        /// The default branch of the repository.
        #[clap(long)]
        default_branch: Option<String>,
        /// Skip ensuring a stable toolchain is used.
        #[clap(long)]
        skip_stable_toolchain: bool,
    },
    RunFlamegraph {
        /// The name of the package to benchmark.
        #[clap(long)]
        package_name: String,
        /// Whether to use debug symbols.
        #[clap(long)]
        use_debug_symbols: bool,
    },
    /// Fetches and combines benchmark artifacts from all branches.
    CombineArtifacts {
        /// GitLab server URL.
        #[clap(long)]
        gitlab_url: Option<String>,
        /// GitLab project ID.
        #[clap(long)]
        project_id: Option<String>,
        /// GitLab access token.
        #[clap(long)]
        token: Option<String>,
        /// Output directory for combined artifacts.
        #[clap(long, default_value = "public")]
        output_dir: PathBuf,
    },
    /// Verifies that all non-gitignored files end with newlines.
    VerifyNewlines {
        /// The files to exclude from the verification.
        #[clap(long)]
        exclude_files: Option<Vec<String>>,
    },
}

// Entrypoint for the xtask CLI - A utility for performing project tasks.
// Usage: cargo run --package xtask -- <command>
// For help: cargo run --package xtask -- --help
#[tokio::main]
async fn main() -> anyhow::Result<()> {
    let cli = Cli::parse();
    tracing_subscriber::fmt::init();

    match cli.command {
        Commands::CheckVersions => check_versions(),
        Commands::RunCriterion {
            package_name,
            commit_branch,
            default_branch,
            skip_stable_toolchain,
        } => run_criterion(
            &package_name,
            &commit_branch,
            &default_branch,
            skip_stable_toolchain,
        ),
        Commands::RunFlamegraph {
            package_name,
            use_debug_symbols,
        } => run_flamegraph(&package_name, use_debug_symbols),
        Commands::CombineArtifacts {
            gitlab_url,
            project_id,
            token,
            output_dir,
        } => combine_artifacts(gitlab_url, project_id, token, &output_dir).await,
        Commands::VerifyNewlines { exclude_files } => verify_newlines(exclude_files),
    }
}

async fn combine_artifacts(
    gitlab_url: Option<String>,
    project_id: Option<String>,
    token: Option<String>,
    output_dir: &Path,
) -> anyhow::Result<()> {
    info!("Combining artifacts from all branches...");

    // Get values from CLI args or environment variables
    let gitlab_url = gitlab_url
        .or_else(|| std::env::var("CI_SERVER_URL").ok())
        .ok_or_else(|| anyhow!("GitLab URL is required (--gitlab-url or CI_SERVER_URL)"))?;

    let project_id = project_id
        .or_else(|| std::env::var("CI_PROJECT_ID").ok())
        .ok_or_else(|| anyhow!("Project ID is required (--project-id or CI_PROJECT_ID)"))?;

    let token = token
        .or_else(|| std::env::var("GITLAB_TOKEN").ok())
        .ok_or_else(|| anyhow!("GitLab token is required (--token or GITLAB_TOKEN)"))?;

    let fetcher = GitLabArtifactFetcher::new(&gitlab_url, &project_id, &token)?;
    fetcher.fetch_and_combine_artifacts(output_dir).await?;

    info!("Successfully combined artifacts from all branches");
    Ok(())
}

fn check_versions() -> anyhow::Result<()> {
    let manifest_dir = PathBuf::from(std::env::var("CARGO_MANIFEST_DIR")?);
    let root_dir = manifest_dir
        .parent()
        .and_then(Path::parent)
        .ok_or_else(|| anyhow!("Failed to find workspace root"))?;

    let version_file_path = root_dir.join("VERSION");
    let expected_version = fs::read_to_string(&version_file_path)
        .with_context(|| {
            format!(
                "Failed to read version from {}",
                version_file_path.display()
            )
        })?
        .trim()
        .to_string();

    info!("Expected version: {}", expected_version);

    let mismatches = Mutex::new(false);
    let errors = Mutex::new(Vec::new());

    let crates_dir = root_dir.join("crates");

    WalkBuilder::new(crates_dir).build_parallel().run(|| {
        Box::new(|entry_result| {
            match entry_result {
                Ok(entry) => {
                    if entry.file_type().is_some_and(|ft| ft.is_file())
                        && entry.file_name().to_string_lossy() == "Cargo.toml"
                    {
                        let cargo_toml_path = entry.path();
                        info!("Checking {}", cargo_toml_path.display());

                        let cargo_toml_content = match fs::read_to_string(cargo_toml_path) {
                            Ok(content) => content,
                            Err(e) => {
                                errors.lock().unwrap().push(anyhow!(e).context(format!(
                                    "Failed to read {}",
                                    cargo_toml_path.display()
                                )));
                                return ignore::WalkState::Continue;
                            }
                        };

                        let toml_value = match cargo_toml_content.parse::<Value>() {
                            Ok(value) => value,
                            Err(e) => {
                                errors.lock().unwrap().push(anyhow!(e).context(format!(
                                    "Failed to parse {}",
                                    cargo_toml_path.display()
                                )));
                                return ignore::WalkState::Continue;
                            }
                        };

                        if let Some(package) = toml_value.get("package") {
                            // don't check self
                            if let Some(name_value) = package.get("name") {
                                if let Some(name_str) = name_value.as_str() {
                                    if name_str == "xtask" {
                                        return ignore::WalkState::Continue;
                                    }
                                }
                            }
                            if let Some(version_value) = package.get("version") {
                                if let Some(version_str) = version_value.as_str() {
                                    if version_str != expected_version {
                                        error!(
                                            "Version mismatch in {}: expected {}, found {}",
                                            cargo_toml_path.display(),
                                            expected_version,
                                            version_str
                                        );
                                        *mismatches.lock().unwrap() = true;
                                    }
                                } else {
                                    error!(
                                        "Version is not a string in {}",
                                        cargo_toml_path.display()
                                    );
                                    *mismatches.lock().unwrap() = true;
                                }
                            }
                        }
                    }
                }
                Err(err) => {
                    errors
                        .lock()
                        .unwrap()
                        .push(anyhow!(err).context("Error walking directory"));
                }
            }
            ignore::WalkState::Continue
        })
    });

    let collected_errors = errors.into_inner().unwrap();
    if !collected_errors.is_empty() {
        for err in collected_errors {
            error!("{:?}", err);
        }
        return Err(anyhow!("Errors occurred during version check"));
    }

    if *mismatches.lock().unwrap() {
        Err(anyhow!("Version mismatches found"))
    } else {
        info!("All Cargo.toml versions match the VERSION file.");
        Ok(())
    }
}

fn verify_newlines(exclude_files: Option<Vec<String>>) -> anyhow::Result<()> {
    let cwd = std::env::current_dir()?;

    let exclude_patterns: Vec<String> = exclude_files.unwrap_or_default();
    let missing_newlines = Mutex::new(Vec::new());
    let errors = Mutex::new(Vec::new());

    info!(
        "Verifying that all files end with newlines for {}",
        cwd.display()
    );

    WalkBuilder::new(&cwd)
        .hidden(false)
        .standard_filters(true)
        .build_parallel()
        .run(|| {
            Box::new(|entry_result| {
                match entry_result {
                    Ok(entry) => {
                        if entry.file_type().is_some_and(|ft| ft.is_file()) {
                            let file_path = entry.path();
                            let relative_path = file_path.strip_prefix(&cwd).unwrap_or(file_path);
                            let path_str = relative_path.to_string_lossy();

                            // Skip .git directory entirely
                            if path_str.starts_with(".git/") {
                                return ignore::WalkState::Continue;
                            }

                            // Skip excluded files
                            if exclude_patterns
                                .iter()
                                .any(|pattern| path_str.contains(pattern))
                            {
                                return ignore::WalkState::Continue;
                            }

                            // Only check specific file extensions
                            let should_check = if let Some(extension) = file_path.extension() {
                                let ext = extension.to_string_lossy().to_lowercase();
                                matches!(ext.as_str(), "rs" | "toml" | "md" | "sh" | "yml" | "yaml")
                            } else {
                                false
                            };

                            if !should_check {
                                return ignore::WalkState::Continue;
                            }

                            match fs::read(file_path) {
                                Ok(contents) => {
                                    // Skip empty files
                                    if contents.is_empty() {
                                        return ignore::WalkState::Continue;
                                    }

                                    // Check if file ends with newline
                                    if !contents.ends_with(b"\n") {
                                        missing_newlines
                                            .lock()
                                            .unwrap()
                                            .push(relative_path.to_path_buf());
                                    }
                                }
                                Err(e) => {
                                    // Skip files that can't be read (likely binary or permission issues)
                                    if e.kind() == std::io::ErrorKind::InvalidData {
                                        return ignore::WalkState::Continue;
                                    }
                                    errors.lock().unwrap().push(anyhow!(e).context(format!(
                                        "Failed to read {}",
                                        file_path.display()
                                    )));
                                }
                            }
                        }
                    }
                    Err(err) => {
                        errors
                            .lock()
                            .unwrap()
                            .push(anyhow!(err).context("Error walking directory"));
                    }
                }
                ignore::WalkState::Continue
            })
        });

    let collected_errors = errors.into_inner().unwrap();
    if !collected_errors.is_empty() {
        for err in collected_errors {
            error!("{:?}", err);
        }
        return Err(anyhow!("Errors occurred during newline verification"));
    }

    let files_missing_newlines = missing_newlines.into_inner().unwrap();
    if !files_missing_newlines.is_empty() {
        error!("Files missing trailing newlines:");
        for file in &files_missing_newlines {
            error!("  {}", file.display());
        }
        Err(anyhow!(
            "{} files are missing trailing newlines",
            files_missing_newlines.len()
        ))
    } else {
        info!("All files end with newlines.");
        Ok(())
    }
}
