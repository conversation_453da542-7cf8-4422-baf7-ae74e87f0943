use crate::utils::{execute_command_with_desc, get_bench_targets};
use anyhow::{Context, Result, anyhow};
use std::fs::{self, File};
use std::io::Write;
use std::path::{Path, PathBuf};
use toml::Value;
use tracing::{error, info, warn};

struct FlamegraphSetupInfo {
    package_cargo_toml_path: PathBuf,
    flamegraph_benches: Vec<String>,
    flamegraphs_output_dir: PathBuf,
}

pub fn run_flamegraph(package_name: &str, use_debug_symbols: bool) -> Result<()> {
    let setup_info = match prepare_flamegraph_run(package_name)? {
        Some(info) => info,
        None => return Ok(()),
    };

    let mut successful_svgs = Vec::new();
    for bench_name in &setup_info.flamegraph_benches {
        if let Some(svg_file) = generate_flamegraph_for_single_benchmark(
            bench_name,
            &setup_info.package_cargo_toml_path,
            &setup_info.flamegraphs_output_dir,
            use_debug_symbols,
        )? {
            successful_svgs.push(svg_file);
        }
    }

    if successful_svgs.is_empty() {
        warn!(
            "No flamegraphs were successfully generated for package {}.",
            package_name
        );
        return Ok(());
    }

    generate_html_report(
        &setup_info.flamegraphs_output_dir,
        &successful_svgs,
        package_name,
    )?;

    Ok(())
}

fn prepare_flamegraph_run(package_name: &str) -> Result<Option<FlamegraphSetupInfo>> {
    info!(
        "Preparing flamegraph generation for package: {}",
        package_name
    );

    let manifest_dir = PathBuf::from(std::env::var("CARGO_MANIFEST_DIR")?);
    let workspace_root = manifest_dir
        .parent()
        .and_then(Path::parent)
        .ok_or_else(|| anyhow!("Failed to find workspace root from xtask"))?;
    info!("Workspace root: {}", workspace_root.display());

    let package_cargo_toml_path = workspace_root
        .join("crates")
        .join(package_name)
        .join("Cargo.toml");

    if !package_cargo_toml_path.exists() {
        return Err(anyhow!(
            "Package Cargo.toml not found at {}",
            package_cargo_toml_path.display()
        ));
    }
    info!(
        "Using package Cargo.toml: {}",
        package_cargo_toml_path.display()
    );

    let cargo_toml_content = fs::read_to_string(&package_cargo_toml_path).with_context(|| {
        format!(
            "Failed to read package Cargo.toml at {}",
            package_cargo_toml_path.display()
        )
    })?;
    let toml_value = cargo_toml_content
        .parse::<Value>()
        .with_context(|| "Failed to parse package Cargo.toml")?;

    let flamegraph_benches = get_bench_targets(&toml_value, package_name, |path| {
        path.contains("flamegraph")
    })
    .with_context(|| {
        format!("Failed to get flamegraph bench targets for package {package_name}")
    })?;

    if flamegraph_benches.is_empty() {
        warn!(
            "No flamegraph benchmarks found for package {}. Ensure they are defined in {} and their path includes 'flamegraph'.",
            package_name,
            package_cargo_toml_path.display()
        );
        return Ok(None);
    }

    let flamegraphs_output_dir = workspace_root.join("target").join("flamegraphs");
    fs::create_dir_all(&flamegraphs_output_dir).with_context(|| {
        format!(
            "Failed to create flamegraphs output directory at {}",
            flamegraphs_output_dir.display()
        )
    })?;
    info!(
        "Flamegraphs will be saved to: {}",
        flamegraphs_output_dir.display()
    );

    Ok(Some(FlamegraphSetupInfo {
        package_cargo_toml_path,
        flamegraph_benches,
        flamegraphs_output_dir,
    }))
}

fn generate_flamegraph_for_single_benchmark(
    bench_name: &str,
    package_cargo_toml_path: &Path,
    flamegraphs_output_dir: &Path,
    use_debug_symbols: bool,
) -> Result<Option<String>> {
    info!("Running flamegraph for benchmark: {}", bench_name);
    let svg_file_name = format!("{bench_name}.svg");
    let output_svg_path = flamegraphs_output_dir.join(&svg_file_name);

    let action_desc = format!("generate flamegraph for benchmark {bench_name}");
    let manifest_path_str = package_cargo_toml_path
        .to_str()
        .ok_or_else(|| anyhow!("Invalid manifest path"))?;
    let output_svg_path_str = output_svg_path
        .to_str()
        .ok_or_else(|| anyhow!("Invalid output SVG path"))?;

    if use_debug_symbols {
        unsafe {
            std::env::set_var("CARGO_PROFILE_BENCH_DEBUG", "true");
        }
    } else {
        unsafe {
            std::env::remove_var("CARGO_PROFILE_BENCH_DEBUG");
        }
    }

    match execute_command_with_desc(
        "cargo",
        &[
            "flamegraph",
            "--manifest-path",
            manifest_path_str,
            "--bench",
            bench_name,
            "-o",
            output_svg_path_str,
        ],
        &action_desc,
    ) {
        Ok(_) => {
            info!(
                "Successfully generated flamegraph: {}",
                output_svg_path.display()
            );
            Ok(Some(svg_file_name))
        }
        Err(e) => {
            error!(
                "Failed to generate flamegraph for benchmark {}: {:?}",
                bench_name, e
            );
            Err(e)
        }
    }
}

fn generate_html_report(output_dir: &Path, svg_files: &[String], package_name: &str) -> Result<()> {
    let report_path = output_dir.join("index.html");
    let mut report_file = File::create(&report_path)
        .with_context(|| format!("Failed to create HTML report at {}", report_path.display()))?;

    writeln!(
        report_file,
        "<html><head><title>Flamegraph Report for {package_name}</title></head><body>"
    )?;
    writeln!(report_file, "<h1>Flamegraph Report for {package_name}</h1>")?;
    writeln!(report_file, "<ul>")?;
    for svg_file in svg_files {
        writeln!(
            report_file,
            "<li><a href=\"./{svg_file}\">{svg_file}</a></li>"
        )?;
    }
    writeln!(report_file, "</ul>")?;
    writeln!(report_file, "</body></html>")?;

    info!("HTML report generated at {}", report_path.display());
    Ok(())
}
