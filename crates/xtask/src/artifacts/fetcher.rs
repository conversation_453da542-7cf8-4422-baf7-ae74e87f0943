use anyhow::{Context, Result, anyhow};
use reqwest::Client;
use serde::Deserialize;
use std::fs;
use std::path::Path;
use tracing::{info, warn};

#[derive(Debug, Deserialize)]
struct GitLabBranch {
    name: String,
}

#[derive(Debug, Deserialize)]
struct GitLabJob {
    id: i64,
    name: String,
    status: String,
}

#[derive(Debug, Deserialize)]
struct GitLabPipeline {
    id: i64,
}

pub struct GitLabArtifactFetcher {
    client: Client,
    base_url: String,
    project_id: String,
    token: String,
}

impl GitLabArtifactFetcher {
    pub fn new(gitlab_url: &str, project_id: &str, token: &str) -> Result<Self> {
        let client = Client::new();
        let base_url = format!("{}/api/v4", gitlab_url.trim_end_matches('/'));

        Ok(Self {
            client,
            base_url,
            project_id: project_id.to_string(),
            token: token.to_string(),
        })
    }

    pub async fn fetch_and_combine_artifacts(&self, output_dir: &Path) -> Result<()> {
        info!("Fetching branches and their benchmark artifacts...");

        // Create output directory
        fs::create_dir_all(output_dir).with_context(|| {
            format!(
                "Failed to create output directory: {}",
                output_dir.display()
            )
        })?;

        // Get all branches
        let branches = self.get_branches().await?;
        info!("Found {} branches", branches.len());

        let mut successful_branches = Vec::new();

        for branch in branches {
            info!("Processing branch: {}", branch.name);

            if let Err(e) = self.fetch_branch_artifacts(&branch.name, output_dir).await {
                warn!(
                    "Failed to fetch artifacts for branch {}: {}",
                    branch.name, e
                );
                continue;
            }

            successful_branches.push(branch.name);
        }

        info!(
            "Successfully processed {} branches",
            successful_branches.len()
        );

        // Generate index page
        self.generate_index_page(output_dir, &successful_branches)?;

        Ok(())
    }

    async fn get_branches(&self) -> Result<Vec<GitLabBranch>> {
        let url = format!(
            "{}/projects/{}/repository/branches",
            self.base_url, self.project_id
        );

        let response = self
            .client
            .get(&url)
            .header("PRIVATE-TOKEN", &self.token)
            .send()
            .await
            .with_context(|| "Failed to fetch branches")?;

        if !response.status().is_success() {
            return Err(anyhow!(
                "Failed to fetch branches: HTTP {}",
                response.status()
            ));
        }

        let branches: Vec<GitLabBranch> = response
            .json()
            .await
            .with_context(|| "Failed to parse branches response")?;

        Ok(branches)
    }

    async fn fetch_branch_artifacts(&self, branch_name: &str, output_dir: &Path) -> Result<()> {
        // Find the latest successful pipeline for this branch
        let pipeline = self.get_latest_successful_pipeline(branch_name).await?;

        // Get jobs from this pipeline
        let jobs = self.get_pipeline_jobs(pipeline.id).await?;

        // Find benchmark jobs (criterion and flamegraph)
        let criterion_job = jobs
            .iter()
            .find(|j| j.name == "rust-criterion-benchmarks" && j.status == "success");
        let flamegraph_job = jobs
            .iter()
            .find(|j| j.name == "rust-flamegraph-benchmarks" && j.status == "success");

        let branch_dir = output_dir.join(sanitize_branch_name(branch_name));
        fs::create_dir_all(&branch_dir)?;

        let mut has_artifacts = false;

        // Download criterion artifacts
        if let Some(job) = criterion_job {
            if self
                .download_job_artifacts(job.id, &branch_dir, "criterion")
                .await
                .is_ok()
            {
                has_artifacts = true;
                info!("Downloaded criterion artifacts for branch {}", branch_name);
            } else {
                warn!(
                    "Failed to download criterion artifacts for branch {}",
                    branch_name
                );
            }
        }

        // Download flamegraph artifacts
        if let Some(job) = flamegraph_job {
            if self
                .download_job_artifacts(job.id, &branch_dir, "flamegraphs")
                .await
                .is_ok()
            {
                has_artifacts = true;
                info!("Downloaded flamegraph artifacts for branch {}", branch_name);
            } else {
                warn!(
                    "Failed to download flamegraph artifacts for branch {}",
                    branch_name
                );
            }
        }

        if !has_artifacts {
            return Err(anyhow!(
                "No benchmark artifacts found for branch {}",
                branch_name
            ));
        }

        Ok(())
    }

    async fn get_latest_successful_pipeline(&self, branch_name: &str) -> Result<GitLabPipeline> {
        let url = format!(
            "{}/projects/{}/pipelines?ref={}&status=success&per_page=1",
            self.base_url, self.project_id, branch_name
        );

        let response = self
            .client
            .get(&url)
            .header("PRIVATE-TOKEN", &self.token)
            .send()
            .await
            .with_context(|| format!("Failed to fetch pipelines for branch {branch_name}"))?;

        if !response.status().is_success() {
            return Err(anyhow!(
                "Failed to fetch pipelines: HTTP {}",
                response.status()
            ));
        }

        let pipelines: Vec<GitLabPipeline> = response
            .json()
            .await
            .with_context(|| "Failed to parse pipelines response")?;

        pipelines
            .into_iter()
            .next()
            .ok_or_else(|| anyhow!("No successful pipeline found for branch {}", branch_name))
    }

    async fn get_pipeline_jobs(&self, pipeline_id: i64) -> Result<Vec<GitLabJob>> {
        let url = format!(
            "{}/projects/{}/pipelines/{}/jobs",
            self.base_url, self.project_id, pipeline_id
        );

        let response = self
            .client
            .get(&url)
            .header("PRIVATE-TOKEN", &self.token)
            .send()
            .await
            .with_context(|| format!("Failed to fetch jobs for pipeline {pipeline_id}"))?;

        if !response.status().is_success() {
            return Err(anyhow!("Failed to fetch jobs: HTTP {}", response.status()));
        }

        let jobs: Vec<GitLabJob> = response
            .json()
            .await
            .with_context(|| "Failed to parse jobs response")?;

        Ok(jobs)
    }

    async fn download_job_artifacts(
        &self,
        job_id: i64,
        output_dir: &Path,
        artifact_type: &str,
    ) -> Result<()> {
        let url = format!(
            "{}/projects/{}/jobs/{}/artifacts",
            self.base_url, self.project_id, job_id
        );

        let response = self
            .client
            .get(&url)
            .header("PRIVATE-TOKEN", &self.token)
            .send()
            .await
            .with_context(|| format!("Failed to download artifacts for job {job_id}"))?;

        if !response.status().is_success() {
            return Err(anyhow!(
                "Failed to download artifacts: HTTP {}",
                response.status()
            ));
        }

        let bytes = response.bytes().await?;

        let zip_path = output_dir.join(format!("{artifact_type}_artifacts.zip"));
        fs::write(&zip_path, bytes)?;

        self.extract_artifacts(&zip_path, output_dir, artifact_type)?;

        fs::remove_file(&zip_path)?;

        Ok(())
    }

    fn extract_artifacts(
        &self,
        zip_path: &Path,
        output_dir: &Path,
        artifact_type: &str,
    ) -> Result<()> {
        let file = fs::File::open(zip_path)?;
        let mut archive = zip::ZipArchive::new(file)?;

        let target_prefix = format!("target/{artifact_type}/");

        for i in 0..archive.len() {
            let mut file = archive.by_index(i)?;
            let outpath = match file.enclosed_name() {
                Some(path) => {
                    // Extract only files from target/criterion/ or target/flamegraphs/
                    if let Ok(stripped) = path.strip_prefix(&target_prefix) {
                        output_dir.join(artifact_type).join(stripped)
                    } else {
                        continue;
                    }
                }
                None => continue,
            };

            if file.name().ends_with('/') {
                fs::create_dir_all(&outpath)?;
            } else {
                if let Some(p) = outpath.parent() {
                    fs::create_dir_all(p)?;
                }
                let mut outfile = fs::File::create(&outpath)?;
                std::io::copy(&mut file, &mut outfile)?;
            }
        }

        Ok(())
    }

    fn generate_index_page(&self, output_dir: &Path, branches: &[String]) -> Result<()> {
        let index_path = output_dir.join("index.html");

        let mut html = String::from(
            r#"<!DOCTYPE html>
<html>
<head>
    <title>GitLab Code Parser - Benchmark Reports</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
        h1 { color: #333; text-align: center; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .branch { margin: 15px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: #fafafa; }
        .branch h3 { margin: 0 0 10px 0; color: #666; }
        .links a { margin-right: 20px; padding: 8px 16px; text-decoration: none; color: white; background: #007cba; border-radius: 4px; display: inline-block; margin-bottom: 5px; }
        .links a:hover { background: #005a87; }
        .no-reports { color: #666; font-style: italic; }
    </style>
</head>
<body>
    <div class="container">
        <h1>GitLab Code Parser - Benchmark Reports</h1>
"#,
        );

        for branch in branches {
            let sanitized_branch = sanitize_branch_name(branch);
            html.push_str("        <div class=\"branch\">\n");
            html.push_str(&format!("            <h3>Branch: {branch}</h3>\n"));
            html.push_str("            <div class=\"links\">\n");

            let criterion_path = output_dir.join(&sanitized_branch).join("criterion");
            let flamegraphs_path = output_dir.join(&sanitized_branch).join("flamegraphs");

            let mut has_links = false;

            if criterion_path.exists() {
                html.push_str(&format!("                <a href=\"./{sanitized_branch}/criterion/report/index.html\">Criterion Benchmarks</a>\n"));
                has_links = true;
            }

            if flamegraphs_path.exists() {
                html.push_str(&format!("                <a href=\"./{sanitized_branch}/flamegraphs/index.html\">Flamegraph Reports</a>\n"));
                has_links = true;
            }

            if !has_links {
                html.push_str("                <span class=\"no-reports\">No benchmark reports available</span>\n");
            }

            html.push_str("            </div>\n");
            html.push_str("        </div>\n");
        }

        html.push_str(
            r#"    </div>
</body>
</html>"#,
        );

        fs::write(index_path, html).with_context(|| "Failed to write index.html")?;

        Ok(())
    }
}

fn sanitize_branch_name(branch_name: &str) -> String {
    branch_name
        .chars()
        .map(|c| {
            if c.is_alphanumeric() || c == '-' || c == '_' {
                c
            } else {
                '-'
            }
        })
        .collect()
}
