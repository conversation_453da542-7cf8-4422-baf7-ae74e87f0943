use anyhow::{Context, Result, anyhow};
use std::process::Command;
use toml::Value;
use tracing::info;

pub fn execute_command_with_desc(
    command_name: &str,
    args: &[&str],
    action_description: &str,
) -> Result<()> {
    info!("Executing: {} {}", command_name, args.join(" "));
    let status = Command::new(command_name)
        .args(args)
        .status()
        .with_context(|| format!("Failed to execute {action_description}"))?;
    if status.success() {
        Ok(())
    } else {
        Err(anyhow!(
            "{} command failed with status: {}",
            action_description,
            status
        ))
    }
}

/// Parses a TOML value (presumably from a Cargo.toml) and extracts benchmark names
/// based on a provided filter function.
///
/// The filter function `filter_fn` takes the benchmark's path (as a &str) and
/// should return `true` if the benchmark is to be included.
/// Primarily used to filter out flamegraph / criterion benchmarks.
pub fn get_bench_targets(
    toml_value: &Value,
    package_name: &str,
    filter_fn: impl Fn(&str) -> bool,
) -> Result<Vec<String>> {
    let mut bench_names = Vec::new();
    if let Some(benches) = toml_value.get("bench").and_then(|b| b.as_array()) {
        for bench_target in benches {
            if let Some(name) = bench_target.get("name").and_then(|n| n.as_str()) {
                if let Some(path) = bench_target.get("path").and_then(|p| p.as_str()) {
                    if filter_fn(path) {
                        bench_names.push(name.to_string());
                        info!(
                            "Found benchmark target: '{}' for package '{}' matching filter criteria.",
                            name, package_name
                        );
                    }
                } else {
                    info!(
                        "Benchmark target '{}' in package '{}' is missing a 'path' field. Skipping.",
                        name, package_name
                    );
                }
            }
        }
    }
    Ok(bench_names)
}
