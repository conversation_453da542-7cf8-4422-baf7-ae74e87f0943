{"name": "@gitlab-org/gitlab-code-parser", "version": "0.5.0", "description": "A single, efficient, and extensible static code‑analysis library written in Rust.", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "semantic-release": "semantic-release"}, "repository": {"type": "git", "url": "git+ssh://**************/gitlab-org/rust/gitlab-code-parser.git"}, "keywords": ["gitlab", "code", "parser", "one", "parser"], "author": "GitLab", "license": "MIT", "bugs": {"url": "https://gitlab.com/gitlab-org/rust/gitlab-code-parser/issues"}, "homepage": "https://gitlab.com/gitlab-org/rust/gitlab-code-parser#readme", "devDependencies": {"@semantic-release/changelog": "^6.0.3", "@semantic-release/commit-analyzer": "^13.0.1", "@semantic-release/exec": "^7.1.0", "@semantic-release/git": "^10.0.1", "@semantic-release/gitlab": "^13.2.6", "@semantic-release/npm": "^12.0.2", "@semantic-release/release-notes-generator": "^14.0.3", "conventional-changelog-conventionalcommits": "^9.0.0", "semantic-release": "^24.2.5", "semantic-release-slack-bot": "^4.0.2"}}