[workspace]
resolver = "2"
members = ["crates/parser-core", "crates/chunker", "crates/xtask"]

[workspace.package]
version = "0.5.0"
license = "MIT"
authors = ["GitLab Inc."]
description = "A single, efficient, and extensible static code‑analysis library written in Rust."
repository = "https://gitlab.com/gitlab-org/rust/gitlab-code-parser"

[workspace.dependencies]
ast-grep-core = "0.38.5"
ast-grep-config = "0.38.5"
ast-grep-language = "0.38.5"
serde = { version = "1.0.219", features = ["derive"] }
once_cell = "1.21.3"
anyhow = "1.0.98"
clap = { version = "4.5.37", features = ["derive"] }
toml = "0.8.23"
ignore = "0.4.23"
tracing = "0.1.41"
tracing-subscriber = "0.3.19"
criterion = { version = "0.6.0", features = ["html_reports"] }
flamegraph = "0.6.8"
thiserror = "2.0.12"
serde_json = "1.0.140"
tempfile = "3.20.0"
paste = "1.0.15"
rustc-hash = "2.1.1"
smallvec = "1.15.1"
reqwest = { version = "0.12.20", features = ["json"] }
tokio = { version = "1.45.1", features = ["rt-multi-thread", "macros"] }
zip = "4.1.0"
