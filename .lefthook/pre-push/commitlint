#!/usr/bin/env bash

command -v commitlint >/dev/null 2>&1 || { echo >&2 "commitlint is not found. Please install it: npm install -g @commitlint/cli @commitlint/config-conventional"; exit 0; }

if remoteBranch=$(git rev-parse --abbrev-ref --symbolic-full-name '@{u}' 2>/dev/null); then
  npx commitlint --from="$remoteBranch" --to=HEAD
else
  # should only happen when first pushing the branch
  commitCount=$(git rev-list origin/main..HEAD --count)
  npx commitlint --from=HEAD~"$commitCount" --to=HEAD
fi

