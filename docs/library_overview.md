# GitLab Code Parser Core Library

This document provides an overview of the `parser-core` library, covering its architecture, parsing infrastructure, and language support.

## Table of Contents

- [High-Level Overview](#high-level-overview)
- [Architecture](#architecture)
- [Language Support](#language-support)
  - [Adding a New Language](#adding-a-new-language)
- [Parser Infrastructure](#parser-infrastructure)
- [Rule System](#rule-system)
  - [Rule Definitions](#rule-definitions)
  - [Rule Execution](#rule-execution)
- [Extracting Matches](#extracting-matches)
  - [Match Information](#match-information)
  - [Raw AST Node Access](#raw-ast-node-access)

## High-Level Overview

The `parser-core` library is a Rust crate designed to parse and analyze source code from multiple programming languages. It uses `tree-sitter` for generating Abstract Syntax Trees (ASTs) and `ast-grep` for pattern matching against these trees.

The library provides the foundational components for building a knowledge graph of code relationships by identifying definitions, calls, and other language constructs.

## Architecture

The library is composed of several components that work together to parse code and find matches.

```mermaid
graph TB
    subgraph "Input"
        A[Source Code File]
    end

    subgraph "Parsing Pipeline"
        A --> B{Language Detection};
        B --> C[Generic Parser];
        C --> D[Tree-sitter AST];
    end

    subgraph "Rule Matching"
        D --> E[RuleManager];
        E -- "Executes" --> F(Pattern Matching);
        F -- "Produces" --> G[Match Results];
    end

    subgraph "Post-processing"
        G -- "Are Transformed Into" --> K(Structured Data);
    end

    subgraph "Library Internals"
        H[parser.rs]
        I[rules.rs]
        J[ruby/definitions.rs]
    end

    H -.-> C;
    I -.-> E;
    J -.-> K;
```
*A high-level diagram illustrating the data flow from source code to structured data results.*

### Component Responsibilities

| File | Responsibility | Structs/Traits |
|---|---|---|
| `parser.rs` | Defines supported languages and provides the core parsing logic. | `SupportedLanguage`, `LanguageParser`, `GenericParser` |
| `rules.rs` | Manages rule loading, execution, and processing of match results. | `RuleManager`, `MatchInfo`, `MatchWithNodes` |
| `ruby/ruby_ast.rs` | Contains the language-specific configuration for Ruby. | `RULES_CONFIG` |
| `ruby/definitions.rs` | Contains the post-processing logic for converting matches into structured data. | `DefinitionInfo`, `find_definitions` |
| `lib.rs` | The main library crate, exporting key components and error types. | `Error`, `Result<T>` |

## Language Support

The library is designed to be extensible for multiple languages. 

### Adding a New Language

Adding support for a new language requires the following steps:

1.  **Define the language**: Add the language to the `define_languages!` macro in `parser.rs`. This involves specifying its name and associated file extensions.

    ```rust
    // In crates/parser-core/src/parser.rs
    define_languages! {
        Ruby { /* ... */ },
        Python {
            name: "python",
            extensions: ["py", "pyw"],
            names: ["python"]
        }
    }
    ```

2.  **Create rule files**: Create a YAML file in a new language-specific module (e.g., `crates/parser-core/src/python/rules/`) to define the code patterns to be matched.

3.  **Load the rules**: In the language's module (e.g., `crates/parser-core/src/python/mod.rs` and `python_ast.rs`), load the YAML rules using `include_str!` and expose them via a `LazyLock`-initialized `RULES_CONFIG` static variable, similar to the existing language implementations.

4.  **Update the RuleManager**: Modify `RuleManager::load_rules_for_language` in `rules.rs` to load the rules for the new language.

    ```rust
    // In crates/parser-core/src/rules.rs
    fn load_rules_for_language(language: SupportedLanguage) -> Vec<&'static RuleConfig<SupportLang>> {
        match language {
            SupportedLanguage::Ruby => { /* ... */ },
            SupportedLanguage::Python => {
                let python_rules = &*crate::python::python_ast::RULES_CONFIG;
                python_rules.iter().collect()
            }
        }
    }
    ```

## Parser Infrastructure

The `GenericParser` provides a consistent interface for parsing any of the supported languages. It takes source code as input and produces a `ParseResult`, which contains the AST ready for analysis.

```rust
// In crates/parser-core/src/parser.rs
pub struct GenericParser {
    language: SupportedLanguage,
}

impl LanguageParser for GenericParser {
    fn parse(&self, code: &str, file_path: Option<&str>) -> Result<ParseResult>;
    // ...
}
```

The `ParseResult` struct holds the language, an optional file path, and the `ast-grep` AST.

```rust
// In crates/parser-core/src/parser.rs
pub struct ParseResult {
    pub language: SupportedLanguage,
    pub file_path: Option<String>,
    pub ast: AstGrep<StrDoc<SupportLang>>,
}
```

## Rule System

The rule system is built on `ast-grep`'s YAML-based pattern definition format. This allows for declarative, flexible, and maintainable code pattern recognition.

### Rule Definitions

Rules are defined in YAML files, specifying an `id`, `language`, and the `rule` itself. The `rule` section contains patterns that `ast-grep` will use to match against the code's AST.

Here is an example from `ruby/rules/definitions.yaml` that captures class and method names:

```yaml
id: ruby-definitions
language: ruby
rule:
  any:
    # Class definition: capture class name
    - kind: class
      has:
        field: name
        kind: constant
        pattern: $CLASS_DEF_NAME

    # Method definition: capture method name
    - kind: method
      has:
        field: name
        kind: identifier
        pattern: $METHOD_DEF_NAME
```

In this example, `$CLASS_DEF_NAME` and `$METHOD_DEF_NAME` are metavariables that capture the corresponding parts of the AST.

### Rule Execution

The `RuleManager` is responsible for loading rules for a specific language and executing them.

```rust
// In crates/parser-core/src/rules.rs
pub struct RuleManager {
    pub language: SupportedLanguage,
    combined_scan: CombinedScan<'static, SupportLang>,
    rules: Vec<&'static RuleConfig<SupportLang>>,
}
```

When `RuleManager::new()` is called for a language, it loads the associated rules from the corresponding module (e.g., `ruby::ruby_ast::RULES_CONFIG`) and compiles them into an efficient `CombinedScan` object for reuse. The `execute_rules` method then runs these compiled patterns against a given AST.

## Extracting Matches

When `execute_rules` finds matches, it returns a `Vec<MatchWithNodes>`. This structure gives access to both high-level, serializable information about the match and the raw `tree-sitter` AST nodes. This is the raw output from the pattern matching engine.

### Post-processing Matches

The raw matches are then post-processed to produce more structured data, such as a list of definitions. This is handled by language-specific logic, such as the `find_definitions` function in the the language-specific module.

This step is responsible for:
-   **Transforming** raw matches into structured types like `DefinitionInfo`.
-   **Enriching** the data, for example, by computing a Fully Qualified Name (FQN) for a definition.
-   **Filtering** out matches that are not relevant for a specific analysis.

The final output is a list of structured data types, like `DefinitionInfo`, which can then be used to build a knowledge graph.

### Match Information

The `MatchInfo` struct contains serializable data about a match.

```rust
// In crates/parser-core/src/rules.rs
pub struct MatchInfo {
    pub rule_id: String,
    pub range: Range,
    pub matched: String,
    pub file: Option<String>,
    pub language: Option<String>,
    pub env: HashMap<String, EnvNode>,
}
```

The `env` field is a map of captured metavariables. Each `EnvNode` provides the captured text, its location (line/column and byte offsets), and the capture type.

```rust
// In crates/parser-core/src/rules.rs
pub struct EnvNode {
    pub text: String,
    pub range: (usize, usize, usize, usize), // (start_line, start_col, end_line, end_col)
    pub byte_offset: (usize, usize),
    pub capture_type: CaptureType,
}
```
The `CaptureType` can be `Single` (for `$VAR`), `Multi` (for `$$$VAR`), or `Transformed`.

### Raw AST Node Access

For more advanced use cases that require interacting directly with the `tree-sitter` AST, `MatchWithNodes` provides direct access to the underlying nodes.

```rust
// In crates/parser-core/src/rules.rs
pub struct MatchWithNodes<'t> {
    pub match_info: MatchInfo,
    pub raw_node: Node<'t, StrDoc<SupportLang>>,
    pub env_nodes: HashMap<String, Node<'t, StrDoc<SupportLang>>>,
    pub multi_env_nodes: HashMap<String, Vec<Node<'t, StrDoc<SupportLang>>>>,
}
```
This allows for traversal of the AST from the matched node or from any of the captured metavariables, enabling more complex analysis than what is available through `MatchInfo` alone.
