# Benchmarking Framework Documentation

This document outlines how to use the benchmarking framework in this project, which leverages Criterion for detailed performance analysis and Flamegraph for visualizing hot spots.

## Table of Contents

- [Overview](#overview)
- [Defining Benchmarks](#defining-benchmarks)
  - [Cargo.toml Configuration](#cargotoml-configuration)
  - [Benchmark Code Structure](#benchmark-code-structure)
- [Running Benchmarks Locally](#running-benchmarks-locally)
  - [Using `xtask`](#using-xtask)
  - [Running Criterion Benchmarks](#running-criterion-benchmarks)
  - [Running Flamegraph Benchmarks](#running-flamegraph-benchmarks)
- [Automated Benchmark Reporting and Deployment](#automated-benchmark-reporting-and-deployment)
  - [CI/CD Workflow](#ci-cd-workflow)
  - [Combining All Branch Artifacts with `xtask`](#combining-all-branch-artifacts-with-xtask)
- [Writing Effective Benchmarks](#writing-effective-benchmarks)

## Overview

The benchmarking setup is designed to provide actionable insights into the performance characteristics of our codebase. It uses:

- **Criterion.rs**: For statistically sound micro-benchmarking.
- **Flamegraph**: For generating interactive flamegraphs to identify performance bottlenecks.
- **`xtask`**: A custom CLI tool to simplify running benchmarks and other development tasks.

## Defining Benchmarks

To add a new benchmark suite for a crate, you need to configure it in the crate's `Cargo.toml` and then write the benchmark code.

### Cargo.toml Configuration

For each benchmark target, you need to add a `[[bench]]` section to your crate's `Cargo.toml`.

**Example from `crates/parser-core/Cargo.toml`:**

```toml
# Criterion benchmarks
[[bench]]
name = "parser_benchmark_1" # A unique name for your benchmark executable
path = "benches/criterion/parser_benchmark_1.rs" # Path to the benchmark file
harness = false # Important: Criterion manages its own harness

# Flamegraph benchmarks
[[bench]]
name = "parser_flamegraph_1"
path = "benches/flamegraph/parser_flamegraph_1.rs"
harness = false # Also false for flamegraph harness managed by xtask
```

**Key Points:**

*   **`name`**: This will be the name of the binary generated for your benchmark.
*   **`path`**: Specifies the location of your benchmark's main Rust file. Conventionally, benchmarks are placed in a `benches/` directory within the crate. It's good practice to separate criterion and flamegraph benchmarks into subdirectories like `benches/criterion/` and `benches/flamegraph/`.
*   **`harness = false`**: This is crucial. Both Criterion and our Flamegraph setup (via `xtask`) provide their own test harness. Setting this to `false` tells Cargo not to use its default libtest harness, allowing our specialized tools to take over.

### Benchmark Code Structure

#### Criterion

Criterion benchmarks are typically structured using the `criterion_group!` and `criterion_main!` macros.

**Example (`crates/parser-core/benches/criterion/parser_benchmark_1.rs`):**

```rust
use criterion::{black_box, criterion_group, criterion_main, Criterion};
// Import the functions/modules you want to benchmark
// e.g., use parser_core::fibonacci;

fn fibonacci_benchmark(c: &mut Criterion) {
    c.bench_function("fib 20", |b| b.iter(|| parser_core::fibonacci(black_box(20))));
}

criterion_group!(benches, fibonacci_benchmark);
criterion_main!(benches);
```

- Use `black_box` to prevent the compiler from optimizing away the code you intend to benchmark.

#### Flamegraph

Flamegraph benchmarks are simpler in structure. They are typically `main` functions that call the code you want to profile. The `xtask` tooling handles the flamegraph generation.

**Example (`crates/parser-core/benches/flamegraph/parser_flamegraph_2.rs`):**

```rust
use parser_core::string_concat;

fn main() {
    // Call the function you want to profile
    // Potentially with more complex logic or loops if needed
    string_concat("Hello", "World");
}
```

## Running Benchmarks Locally

The `xtask` crate provides convenient commands for running benchmarks.

### Using `xtask`

You can invoke `xtask` commands from the root of the workspace:

```bash
cargo xtask <command>
```

### Running Criterion Benchmarks

To run all Criterion benchmarks defined in the workspace:

```bash
cargo xtask run-criterion
```

This command will:
1. Build the benchmark targets.
2. Execute them using Criterion.
3. Store results in `target/criterion/`.

You can also run benchmarks for a specific package:
```bash
cargo xtask run-criterion --package parser-core
```
And compare against a baseline:
```bash
cargo xtask run-criterion --baseline main
```

### Running Flamegraph Benchmarks

To generate flamegraphs for all flamegraph benchmark targets:

```bash
cargo xtask run-flamegraph
```

This command will:
1. Build the specified benchmark targets.
2. Run them under `flamegraph` to capture profiling data.
3. Generate SVG flamegraphs in `target/flamegraph/`.
4. Generate an HTML report linking to all flamegraphs at `target/flamegraph/index.html`.

You can also run flamegraph generation for a specific package:
```bash
cargo xtask run-flamegraph --package parser-core
```

## Automated Benchmark Reporting and Deployment

The project is configured to automatically run benchmarks and deploy them to GitLab Pages in a way that preserves reports from all branches.

### CI/CD Workflow

The `.gitlab-ci.yml` file defines a `pages` job that orchestrates this process:

1.  **Run Benchmarks**: The `rust-criterion-benchmarks` and `rust-flamegraph-benchmarks` jobs run on every commit, saving their results (`target/criterion` and `target/flamegraphs`) as artifacts.
2.  **Combine and Deploy**: The `pages` job then executes. It first copies the current branch's newly generated artifacts into the `public/` directory.
3.  **Fetch All Artifacts**: It then runs the `combine-artifacts` `xtask` command, which fetches the latest benchmark artifacts from all other branches via the GitLab API and merges them into the `public/` directory.
4.  **Publish**: The final `public/` directory, containing reports from all branches, is published to GitLab Pages.

### Combining All Branch Artifacts with `xtask`

The `combine-artifacts` command is the core of the automated deployment process. It can also be run locally, though it is primarily designed for CI.

```bash
# In CI, variables are supplied automatically
cargo xtask combine-artifacts --output-dir public

# For local execution, you must provide credentials
cargo xtask combine-artifacts \
  --gitlab-url <CI_SERVER_URL> \
  --project-id <CI_PROJECT_ID> \
  --token <YOUR_GITLAB_TOKEN>
```

This command generates a unified site with a root `index.html` file that links out to the reports for each individual branch.

## Writing Effective Benchmarks

- **Isolate**: Benchmark small, specific units of code.
- **Realistic Inputs**: Use input data that reflects real-world usage. `black_box` can be essential here.
- **Iterations**: Ensure your benchmarked function runs long enough for Criterion to gather stable measurements, but not so long that the benchmark suite takes excessive time. For flamegraphs, ensure the profiled section executes enough times to capture meaningful data.
- **Avoid side effects**: Benchmarks should generally avoid I/O or other side effects that can skew results, unless those side effects are specifically what you intend to measure.
- **Consistency**: Run benchmarks in a stable environment to minimize variance from external factors. 
