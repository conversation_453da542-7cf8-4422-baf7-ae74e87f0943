# Python definitions coverage

This document provides an overview of Python definitions that are currently captured by our parser, along with definitions that should be added in future iterations.

> Please refer to [#29](https://gitlab.com/gitlab-org/rust/gitlab-code-parser/-/issues/29) for context on how we're making inclusion/exclusion decisions for what counts as definition or reference.

## Currently supported definitions

| Definition Type | Description | Example | Capture Variables |
|----------------|-------------|---------|-------------------|
| Class | Basic class definition | `class MyClass:` | `CLASS_DEF_NAME` |
| Decorated class | Class with one or more decorators | `@dataclass`<br />`class MyClass:` | `DECORATED_CLASS_DEF_NAME` |
| Method | Function inside a class | `def method(self):` | `METHOD_DEF_NAME` |
| Decorated method | Method with one or more decorators | `@property`<br />`def method(self):` | `DECORATED_METHOD_DEF_NAME` |
| Asynchronous method | Async method | `async def method(self):` | `ASYNC_METHOD_DEF_NAME` |
| Decorated asynchronous method | Async method with one or more decorators  | `@cached`<br />`async def method(self):` | `DECORATED_ASYNC_METHOD_DEF_NAME` |
| Function | Basic function definition | `def my_func():` | `FUNCTION_DEF_NAME` |
| Decorated function | Function with one or more decorators | `@lru_cache`<br />`def my_func():` | `DECORATED_FUNCTION_DEF_NAME` |
| Asynchronous function | Async function definition | `async def my_func():` | `ASYNC_FUNCTION_DEF_NAME` |
| Decorated asynchronous function | Async function with decorators | `@retry`<br>`async def my_func():` | `DECORATED_ASYNC_FUNCTION_DEF_NAME` |
| Lambda | Named (not anonymous) lambda expression | `my_lambda = lambda x: x * 2` | `LAMBDA_DEF`, `LAMBDA_VARIABLE_NAME`, `LAMBDA_ATTRIBUTE_NAME` |

## Missing definitions and limitations

1. Scope changes using `nonlocal` or `global` keywords are not handled
2. Dynamic classes are not captured (e.g. `DynamicClass = type("DynamicClass", (object,), {"method": lambda self: self.do_something()})`)
3. Non-callable definitions (e.g. constants, types, etc.) are not captured
    - Eventually, we want to capture every object defined in the namespace, including objects that aren't callable
4. Anonymous functions (i.e. unnamed lambdas) are intentionally not handled because they can't be referenced
5. Nested lambda definitions (e.g. `funcs = {"fn_1": lambda x: x}`) aren't captured, even though they are technically named
