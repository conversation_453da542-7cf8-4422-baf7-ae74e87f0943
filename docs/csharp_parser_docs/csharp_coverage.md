# C# Definitions Coverage

This document provides an overview of C# definitions that are currently captured by our parser, along with definitions that should be added in future iterations.

## Currently Supported Definitions

| Definition Type | Status | Description | Example | Capture Variables | FQN Support |
|----------------|--------|-------------|---------|-------------------|-------------|
| **Namespace** | ✅ Supported | Namespace declarations | `namespace MyNamespace` | `NAMESPACE_IDENTIFIER` | ✅ Full |
| **File-Scoped Namespace** | ⚠️ Limited | File-scoped namespace declarations | `namespace MyNamespace;` | `NAMESPACE_IDENTIFIER` | ⚠️ Limited |
| **Class** | ✅ Supported | Class declarations | `class MyClass` | `CLASS_IDENTIFIER` | ✅ Full |
| **Interface** | ✅ Supported | Interface declarations | `interface IRepository` | `INTERFACE_IDENTIFIER` | ✅ Full |
| **Record** | ✅ Supported | Record declarations | `record Person(string Name)` | `RECORD_IDENTIFIER` | ✅ Full |
| **Struct** | ✅ Supported | Struct declarations | `struct Point` | `STRUCT_IDENTIFIER` | ✅ Full |
| **Enum** | ✅ Supported | Enum declarations | `enum Color` | `ENUM_IDENTIFIER` | ✅ Full |
| **Delegate** | ✅ Supported | Delegate declarations | `delegate void MyDelegate()` | `DELEGATE_IDENTIFIER` | ✅ Full |
| **Property** | ✅ Supported | Property declarations | `public int MyProperty { get; set; }` | `PROPERTY_IDENTIFIER` | ✅ Full |
| **Constructor** | ✅ Supported | Constructor declarations | `public MyClass()` | `CONSTRUCTOR` | ✅ Full |
| **Destructor/Finalizer** | ✅ Supported | Destructor declarations | `~MyClass()` | `FINALIZER` | ✅ Full |
| **Instance Method** | ✅ Supported | Non-static method declarations | `public void MyMethod()` | `INSTANCE_METHOD_IDENTIFIER` | ✅ Full |
| **Static Method** | ✅ Supported | Static method declarations | `public static void MyMethod()` | `STATIC_METHOD_IDENTIFIER` | ✅ Full |
| **Extension Method** | ⚠️ Limited | Extension method declarations. Limited detection for the target type. | `public static void ExtMethod(this string s)` | `EXTENSION_METHOD_IDENTIFIER` | ✅ Full |
| **Lambda (Local Variable)** | ✅ Supported | Lambda expressions assigned to variables | `var func = () => 42;` | `LAMBDA_LOCAL_NAME` | ✅ Full |
| **Indexer** | ✅ Supported | Custom indexer properties | `public string this[int index] { get; set; }` | `INDEXER_DECLARATION` | ✅ Full |
| **Event** | ✅ Supported | Event field declarations | `public event EventHandler MyEvent;` | `EVENT_DECLARATION` | ✅ Full |
| **Anonymous Type** | ✅ Supported | Compiler-generated types assigned to variables | `var obj = new { Name = "Test" };` | `ANONYMOUS_TYPE` | ✅ Full |

## Partially Supported Definitions

| Definition Type | Status | Description | Example | Limitations |
|----------------|--------|-------------|---------|-------------|
| **Method Overloads** | ⚠️ Limited | Multiple methods with same name | `void Method(int a)` and `void Method(string a)` | No parameter type resolution |
| **Constructor Overloads** | ⚠️ Limited | Multiple constructors | `MyClass(int a)` and `MyClass(string a)` | No parameter type resolution |
| **Operator Overloads** | ⚠️ Limited | Custom operator overloads | `public static Point operator +(Point a, Point b)` | Does not support `implicit` or `explicit` conversion operators. |

## Unsupported Definitions (Future Roadmap)

| Definition Type | Description | Example | Complexity | Notes |
|----------------|-------------|---------|------------|-------|
| **Local Function Statements** | Functions declared inside methods | `void Method() { void LocalFunc() { } }` | Medium |  |
| **Global Statements** | Top-level statements in programs | `Console.WriteLine("Hello");` | Low |  |
