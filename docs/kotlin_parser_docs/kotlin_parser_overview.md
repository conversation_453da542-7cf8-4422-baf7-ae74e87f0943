# Kotlin Parser Documentation

This document provides a high-level overview of the Kotlin parser implementation. For common patterns and concepts, see the [Ruby Parser Overview](../ruby_parser_docs/ruby_parser_overview.md).

## Table of Contents

- [Coverage](#coverage)
- [Files and Organization](#files-and-organization)
- [Kotlin-Specific Components](#kotlin-specific-components)
- [Data Structures](#data-structures)
- [Variable Captures](#variable-captures)

## Coverage

The Kotlin parser supports the following definition types:

- Packages
- Classes
- Functions (including extension functions)
- Properties
- Companion Objects
- Constructors (primary and secondary)

## Files and Organization

```
src/kotlin/
├── README.md                          # Documentation
├── mod.rs                             # Module exports
├── kotlin_ast.rs                      # YAML rules loading and configuration
├── fqn.rs                            # FQN computation and metadata
├── definitions.rs                     # Definition extraction and classification  
├── analyzer.rs                       # Main API and result aggregation
├── rules/
│   └── definitions.yaml              # YAML rule definitions
└── fixtures/                         # Test fixtures
    ├── ComprehensiveKotlinDefinitions.kt   # Complete definition type coverage
    ├── JetbrainsExtensionSample.kt   # JetBrains IDE extension example
    └── EclipseExtensionSample.kt     # Eclipse IDE extension example
```

## Kotlin-Specific Components

The architecture follows the same pattern as the [Ruby Parser Overview](../ruby_parser_docs/ruby_parser_overview.md#architecture-components), with these Kotlin-specific components:

| Component | Responsibility | Key Types |
|-----------|---------------|-----------|
| **`kotlin_ast.rs`** | YAML rule loading, rule-to-kind mapping | `KotlinMatchKind`, `RULES_CONFIG` |
| **`fqn.rs`** | FQN computation, AST traversal, node indexing | `KotlinFqn`, `KotlinFqnMetadata`, `KotlinNodeFqnMap` |
| **`definitions.rs`** | Definition extraction, type classification | `DefinitionInfo`, `KotlinDefinitionType`, `DefinitionExtractor` |
| **`analyzer.rs`** | Main API, result aggregation | `KotlinAnalyzer`, `KotlinAnalysisResult` |

## Data Structures

### KotlinFqnMetadata
```rust
pub struct KotlinFqnMetadata {
    pub receiver_type_reference: Option<String>,  // For extension functions
}
```

### KotlinFqnPart
```rust
pub type KotlinFqnPart = FQNPart<String, KotlinFqnMetadata>;

// Example: Class part for "User" class
KotlinFqnPart {
    node_type: "class".to_string(),
    node_name: "User".to_string(),
    metadata: Some(KotlinFqnMetadata {
        receiver_type_reference: None,
    })
}
```

### KotlinFqn
```rust
pub type KotlinFqn = Fqn<KotlinFqnPart>;

// Example: "com.example.User.initialize"
KotlinFqn {
    parts: Arc<Vec<KotlinFqnPart>>[
        KotlinFqnPart { node_type: "package", node_name: "com.example", metadata: ... },
        KotlinFqnPart { node_type: "class", node_name: "User", metadata: ... },
        KotlinFqnPart { node_type: "function", node_name: "initialize", metadata: ... },
    ]
}
```

## Variable Captures

| Definition Type | Primary Env Vars |
|----------------|------------------|
| **Package** | `PACKAGE_NAME` |
| **Class** | `CLASS_DEF_NAME` |
| **Function** | `FUNCTION_DEF_NAME` |
| **Property** | `PROPERTY_DEF_NAME` |
| **Companion Object** | `COMPANION_OBJECT_DEF` |
| **Constructor** | `CONSTRUCTOR_DEF` |
