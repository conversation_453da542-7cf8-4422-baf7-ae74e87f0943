# Kotlin Definitions Coverage

This document provides an overview of Kotlin definitions that are currently captured by our parser, along with definitions that should be added in future iterations.

> See the [Kotlin Parser](kotlin_parser_overview.md) documentation for how the parser works.

## Currently Supported Definitions

| Definition Type | Status | Description | Example | Capture Variables | FQN Support |
|----------------|--------|-------------|---------|-------------------|-------------|
| **Package** | ✅ Supported | Package declarations | `package com.example` | `PACKAGE_NAME` | ✅ Full |
| **Class** | ✅ Supported | Class declarations | `class MyClass` | `CLASS_DEF_NAME` | ✅ Full |
| **Interface** | ✅ Supported | Interface declarations | `interface Repository<T>` | `CLASS_DEF_NAME` | ✅ Full |
| **Function** | ✅ Supported | Function declarations | `fun myFunction()` | `FUNCTION_DEF_NAME` | ✅ Full |
| **Property** | ✅ Supported | Property declarations | `val myProperty: Int` | `PROPERTY_DEF_NAME` | ✅ Full |
| **Extension Property** | ✅ Supported | Extension property declarations | `val String.lastChar: Char` | `PROPERTY_DEF_NAME` | ✅ Full |
| **Companion Object** | ✅ Supported | Companion object declarations | `companion object` | `COMPANION_OBJECT_DEF` | ✅ Full |
| **Constructor** | ✅ Supported | Constructor declarations | `constructor()` | `CONSTRUCTOR_DEF` | ✅ Full |
| **Data Class** | ✅ Supported | Data class declarations | `data class User` | `CLASS_DEF_NAME` | ✅ Full |
| **Sealed Class** | ✅ Supported | Sealed class declarations | `sealed class Result` | `CLASS_DEF_NAME` | ✅ Full |
| **Object** | ✅ Supported | Object declarations | `object MyObject` | `OBJECT_DEF_NAME` | ✅ Full |
| **Top-Level Function** | ✅ Supported | Functions outside classes | `fun main()` | `FUNCTION_DEF_NAME` | ✅ Full |
| **Top-Level Property** | ✅ Supported | Properties outside classes | `val VERSION = "1.0"` | `PROPERTY_DEF_NAME` | ✅ Full |
| **Annotation Class** | ✅ Supported | Annotation class declarations | `annotation class MyAnnotation` | `CLASS_DEF_NAME` | ✅ Full |
| **Enum Class** | ✅ Supported | Enum class declarations | `enum class Color` | `CLASS_DEF_NAME` | ✅ Full |
| **Enum Entry** | ✅ Supported | Enum entry declarations | `RED, GREEN, BLUE` | `ENUM_ENTRY_DEF_NAME` | ✅ Full |

## Missing Definitions (Future Roadmap)

| Definition Type | Description | Example | Notes |
|----------------|-------------|---------|-------|
| **Type Alias** | Type alias declarations | `typealias UserId = String` | Useful for type resolution |
| **Method Overloading** | Multiple methods with same name | `fun add(a: Int)` and `fun add(a: String)` | Requires parameter type resolution |
| **Constructor Overloading** | Multiple constructors | `constructor(a: Int)` and `constructor(a: String)` | Requires parameter type resolution |
| **Delegates** | Property delegate declarations | `val lazy by lazy { ... }` | Requires delegate expression analysis |

