# TypeScript Definitions Coverage

This document provides an overview of TypeScript definitions that are currently captured by our parser, along with definitions that should be added in future iterations.
Do note that this also includes support for Javascript natively, as typescript is a superset of JS.

> See the [Typescript Parser](parser_overview.md) documentation for how the parser works.

## Currently Supported Definitions

| Definition Type | Status | Description | Example | Capture Variables | FQN Support |
|----------------|--------|-------------|---------|-------------------|-------------|
| **Class** | ✅ Supported | Class declarations | `class User extends BaseUser` | `CLASS_DEF_NAME` | ✅ Full |
| **Named Class Expression** | ✅ Supported | Class expressions assigned to variables/constants | `const UserClass = class {}` | `CLASS_EXPR_NAME`, `CLASS_EXPR_BODY` | ✅ Full |
| **Method** | ✅ Supported | Instance and class methods in classes | `authenticate(user, password)` | `METHOD_DEF_NAME` | ✅ Full |
| **Static Field** | ✅ Supported | Static class fields with expressions | `static InnerStatic = class {}` | `CLASS_EXPR_NAME`, `CLASS_EXPR_BODY` | ✅ Full |
| **Private Method** | ✅ Supported | Private class methods | `#validateConfig(config)` | `METHOD_DEF_NAME` | ✅ Full |
| **Function** | ✅ Supported | Function declarations | `function authenticate(user, password)` | `FUNCTION_DEF_NAME` | ✅ Full |
| **Named Arrow Function** | ✅ Supported | Arrow functions assigned to variables/constants | `const validate = (input) => {}` | `NAMED_ARROW_FUNC_DEF_NAME`, `NAMED_ARROW_FUNC_BODY` | ✅ Full |
| **Named Function Expression** | ✅ Supported | Function expressions assigned to variables/constants | `const validate = function(input) {}` | `NAMED_FUNC_EXP_NAME`, `NAMED_FUNC_EXP_BODY` | ✅ Full |
| **Named Generator Function** | ✅ Supported | Generator functions assigned to variables/constants | `const generator = function* () {}` | `NAMED_GENERATOR_FUNC_DEF_NAME`, `NAMED_GENERATOR_FUNC_BODY` | ✅ Full |

## Missing Definitions (Future Roadmap)

| Definition Type | Description | Example | Complexity | Notes |
|----------------|-------------|---------|------------|-------|
| **Object Methods** | Methods defined in object literals | `const obj = { method() {} }` | Medium | Important for object-oriented patterns |
| **Getters/Setters** | Property accessors in objects and classes | `get name() {}` | Medium | Essential for property access patterns |
| **Decorators** | Method and class decorators | `@decorator class` | High | Requires experimental support |
| **Complex Class Expressions** | Classes defined in object literals or complex expressions | `const classes = { A: class A {} }` | High | Important for class factories and registries |
| **Class Factory Functions** | Functions that return class definitions | `const ClassFactory = (base) => class extends base {}` | High | Essential for higher-order component patterns |
| **Dynamic Method Names** | Methods with computed property names | `[`dynamic${Date.now()}`]() {}` | Medium | Important for meta-programming |
| **Named IIFEs** | Named Immediately Invoked Function Expressions | `(function namedIIFE() {})()` | Low | Useful for debugging and stack traces |
| **Ambient Declarations** | Declarations using the `declare` keyword | `declare class ExternalClass {}` | Medium | Important for type definitions and module augmentation |
| **Enums** | Enumerated type definitions | `enum Direction { Up, Down }` | Low | Common in TypeScript for defining constants |
| **Interface Method Declarations** | Method signatures in interfaces | `interface Name { methodName(): void }` | Low | Essential for interface definitions and type contracts |
