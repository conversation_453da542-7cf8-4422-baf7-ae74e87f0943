### What is a Fully Qualified Name (FQN)?

A Fully Qualified Name (FQN) is a unique, unambiguous identifier for a code entity (such as a class, function, method, variable, module, or package) that specifies its complete hierarchical path within a codebase or project. The primary purpose of an FQN is to provide a stable and precise reference to a specific code element, resolving any ambiguity that might arise from elements sharing the same simple name in different scopes or modules.

**FQNs have:**

* **Unique Identification:** Ensures that each distinct code entity has a singular, unambiguous identifier across the entire codebase. This is fundamental for any system that needs to track and relate code elements.
* **Hierarchical Context:** Captures the nested structure of code, showing how an entity relates to its parent modules, classes, or functions. This reflects the organizational structure of the source code.
* **Linking and Relationships:** Can serve as a primary key for nodes in a graph database (like the GitLab Knowledge Graph). This enables the creation of edges representing various relationships such as calls, imports, inheritance, and containment between different code entities.
* **Cross-File/Cross-Module Resolution:** Can allow tools to accurately link references to their definitions, even when they reside in different files or modules.

**Code Examples of FQNs:**

The specific syntax and conventions for FQNs vary by programming language, but the underlying concept of a complete, hierarchical path remains consistent.

* **Python:**
  * For a function `calculate_sum` defined in `my_project/src/utils/helpers.py`: `my_project.src.utils.helpers.calculate_sum`
  * For a method `my_method` within `MyClass` in `my_project/main.py`: `my_project.main.MyClass.my_method`
* **Java:**
  * For a method `createUser` within the `UserService` class in the `com.example.myapp.services` package: `com.example.myapp.services.UserService.createUser`
  * For the `add` method of the `ArrayList` class: `java.util.ArrayList.add`
* **Kotlin:**
  * For a function `myFunction` in `MyClass` within the `com.example.MyPackage` package: `com.example.MyPackage.MyClass.myFunction`
  * For a property `myProperty` in `MyObject` within the `com.example.MyPackage` package: `com.example.MyPackage.MyObject.myProperty`
* **JavaScript/TypeScript (Conceptual):**
  * For a method `onClick` in a `Button` component within `myApp.components`: `myApp.components.Button.onClick`
* **Ruby:**
  * For an instance method `instance_method` in `MyClass` within `MyModule`: `MyModule::MyClass::instance_method`
  * For a class method `class_method` in `MyClass` within `MyModule`: `MyModule::MyClass::class_method`
  * For a top-level method `top_level_method`: `top_level_method`
  * For a method `valid_password?` in `CredentialsChecker` class, nested under `AuthenticationService` module: `AuthenticationService::CredentialsChecker::valid_password?`

## Language-Specific FQNs

### Ruby

See [Ruby FQN Documentation](./ruby_parser_docs/ruby_parser_overview.md) for more details.
