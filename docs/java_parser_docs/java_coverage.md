# Java Definitions Coverage

This document provides an overview of Java definitions that are currently captured by our parser, along with definitions that should be added in future iterations.

## Currently Supported Definitions

> **Note**: Currently, we only support callable definitions (classes, interfaces, methods, constructors, etc.). Non-callable definitions like fields, local variables, and parameters are not yet captured.


| Definition Type | Status | Description | Example | Capture Variables | FQN Support |
|----------------|--------|-------------|---------|-------------------|-------------|
| **Package** | ✅ Supported | Package declarations | `package com.example.test;` | `PACKAGE_IDENTIFIER` | ✅ Full |
| **Class** | ✅ Supported | Class declarations | `public class MyClass` | `CLASS_DEF_NAME` | ✅ Full |
| **Interface** | ✅ Supported | Interface declarations | `public interface Repository<T>` | `INTERFACE_DEF_NAME` | ✅ Full |
| **Enum** | ✅ Supported | Enum declarations | `public enum Status` | `ENUM_DEF_NAME` | ✅ Full |
| **Enum Constant** | ✅ Supported | Enum constant declarations | `ACTIVE("active")` | `ENUM_CONSTANT_DEF_NAME` | ✅ Full |
| **Record** | ✅ Supported | Record declarations | `public record Person(String name)` | `RECORD_DEF_NAME` | ✅ Full |
| **Annotation** | ✅ Supported | Annotation type declarations | `public @interface MyAnnotation` | `ANNOTATION_DEF_NAME` | ✅ Full |
| **Annotation Declaration** | ✅ Supported | Annotation element declarations | `String value() default "";` | `ANNOTATION_DECLARATION_DEF_NAME` | ✅ Full |
| **Method** | ✅ Supported | Method declarations | `public void myMethod()` | `METHOD_DEF_NAME` | ✅ Full |
| **Constructor** | ✅ Supported | Constructor declarations | `public MyClass()` | `CONSTRUCTOR_DEF_NAME` | ✅ Full |
| **Record Constructor** | ✅ Supported | Record compact constructors | `public Person { if (age < 0) ... }` | `CONSTRUCTOR_DEF_NAME` | ✅ Full |

## Partially Supported Definitions

| Definition Type | Status | Description | Example | Limitations |
|----------------|--------|-------------|---------|-------------|
| **Method Overloads** | ⚠️ Limited | Multiple methods with same name | `void method(int a)` and `void method(String a)` | No parameter type resolution, same FQN |
| **Constructor Overloads** | ⚠️ Limited | Multiple constructors | `MyClass(int a)` and `MyClass(String a)` | No parameter type resolution, same FQN |

## Unsupported Definitions (Future Roadmap)

| Definition Type | Description | Example | Notes |
|----------------|-------------|---------|-------|
| **Field** | Instance and static field declarations | `private String name;` | Basic field capture needed |
| **Static Field** | Static field declarations | `public static final int MAX_SIZE = 100;` | Field with static modifier |
| **Local Variable** | Local variable declarations | `int localVar = 42;` | Variable scope tracking needed |
| **Parameter** | Method and constructor parameters | `public void method(String param)` | Parameter type resolution |
| **Lambda Expression** | Lambda expressions | `(a, b) -> a + b` | Complex AST traversal needed |
| **Module Declaration** | Module declarations (Java 9+) | `module com.example { }` | Module system |
| **Requires Clause** | Module requires clauses | `requires java.base;` | Module dependencies |
| **Exports Clause** | Module exports clauses | `exports com.example.api;` | Module API |
| **Opens Clause** | Module opens clauses | `opens com.example.internal;` | Module reflection |
| **Provides Clause** | Module provides clauses | `provides Service with ServiceImpl;` | Module services |
| **Uses Clause** | Module uses clauses | `uses ServiceLoader;` | Module services |
