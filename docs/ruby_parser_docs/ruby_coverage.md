# Ruby Definitions Coverage

This document provides an overview of Ruby definitions that are currently captured by our parser, along with definitions that should be added in future iterations.

> See the [Ruby Parser](ruby_parser_overview.md) documentation for how the parser works.

## Currently Supported Definitions

| Definition Type | Status | Description | Example | Capture Variables | FQN Support |
|----------------|--------|-------------|---------|---------------------|-------------|
| **Class** | ✅ Supported | Class definitions | `class User < ActiveRecord::Base` | `CLASS_DEF_NAME` | ✅ Full |
| **Module** | ✅ Supported | Module definitions and namespaces | `module Authentication` | `MODULE_DEF_NAME` | ✅ Full |
| **Method** | ✅ Supported | Instance and class method definitions | `def authenticate(user, password)` | `METHOD_DEF_NAME` | ✅ Full |
| **Singleton Method** | ✅ Supported | Class methods and singleton methods | `def self.find_by_email(email)` | `SINGLETON_METHOD_DEF_NAME` | ✅ Full |
| **Lambda** | ✅ Supported | Lambda expressions assigned to variables/constants | `VALIDATOR = lambda { \|x\| x > 0 }` | `LAMBDA_DEF`, `LAMBDA_CONSTANT_NAME`, `LAMBDA_VARIABLE_NAME`, `LAMBDA_INSTANCE_VAR`, `LAMBDA_CLASS_VAR` | ✅ Full |
| **Proc** | ✅ Supported | Proc.new expressions (assignments and standalone) | `Proc.new { \|x\| x + 1 }` | `PROC_DEF`, `PROC_ASSIGNMENT`, `PROC_CONSTANT_NAME`, `PROC_VARIABLE_NAME`, `PROC_INSTANCE_VAR`, `PROC_CLASS_VAR` | ✅ Full |

## Missing Definitions (Future Roadmap)

| Definition Type | Description | Example | Complexity | Notes |
|----------------|-------------|---------|------------|-------|
| **Constant** | Constant definitions | `CONSTANT_NAME = 1` | Medium | Essential for constant resolution |
| **Attribute** | Attribute definitions | `attr_reader :name` | Medium | Essential for attribute resolution |
