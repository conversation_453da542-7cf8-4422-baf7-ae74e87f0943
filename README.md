# gitlab-code-parser (One Parser)

A single, **efficient** and **extensible** static code‑analysis library written in Rust.
It powers multiple GitLab features, such as the [Knowledge Graph](https://gitlab.com/gitlab-org/rust/knowledge-graph) and [Embeddings](https://gitlab.com/groups/gitlab-org/-/epics/16910) features, by offering one AST (Abstract Syntax Tree) parsing engine that runs everywhere.

> Note: This project is not production ready and is still under active development.


## ✨ Features

* `tree-sitter` + `ast‑grep` – performant, language‑aware parsing
* **Stateless API** – give it a file path + contents, get structured entities & relationships
* **Multi‑runtime bindings** – Rust crate • Node.js (napi‑rs) • Go (cgo FFI) • WASM • Ruby (FFI)


## Quick start

### Prerequisites

| Purpose                   | Minimum Version |
| ------------------------- | --------------- |
| Rust tool‑chain           | stable (via mise) |

### Setup with mise

This project uses [mise](https://mise.jdx.dev/) for toolchain management. All required tool versions are specified in `mise.toml`.

1. **Install mise** (if not already):
   ```bash
   curl https://mise.run | bash
   # or see https://mise.jdx.dev/ for more options
   ```
2. **Install the required tools:**
   ```bash
   mise install
   ```
   This will install the correct Rust toolchain and any other tools specified in `mise.toml`.

### Rust toolchain

The Rust toolchain is pinned via [`rust-toolchain.toml`](./rust-toolchain.toml):
```toml
[toolchain]
channel = "stable"
components = ["rustfmt", "clippy"]
```

### Build the Rust crate

```bash
$ git clone https://gitlab.com/gitlab-org/code-creation/gitlab-code-parser
$ cd gitlab-code-parser
$ cargo build --release
```

### Parse a file (Rust example)

```rust
NA
```

> See `/examples` for Node, Go and WASM usage samples.

## Roadmap

Follow progress in the 👉 [One Parser epic](https://gitlab.com/groups/gitlab-org/-/epics/17516).

## Development

### Testing

Run all tests:
```bash
cargo test --all
```

### Linting (clippy)

Run [clippy](https://github.com/rust-lang/rust-clippy) for lint checks:
```bash
cargo clippy --all -- -D warnings
```

### Formatting

Format the codebase using [rustfmt](https://github.com/rust-lang/rustfmt):
```bash
cargo fmt --all
```
